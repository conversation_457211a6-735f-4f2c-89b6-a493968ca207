# 📱 دليل تطبيقات الأندرويد - نظام Clinineo

## 🎯 **النظام الكامل:**
1. **🖥️ خادم محلي** على جهاز كمبيوتر متصل بالراوتر
2. **📱 تطبيق أندرويد للاستقبال** - موظف الاستقبال
3. **📱 تطبيق أندرويد للطبيب** - الطبيب
4. **🌐 الربط عبر الراوتر** - لا حاجة لإنترنت

---

## 📋 **الخطوة 1: إعداد الخادم (جهاز الكمبيوتر)**

### 1.1 تثبيت المتطلبات:
```bash
pip install requests
```

### 1.2 تشغيل الخادم:
```bash
python android_server.py
```

### 1.3 معرفة عنوان IP:
سيظهر في الخادم شيء مثل:
```
✅ الخادم يعمل على:
   📍 المحلي: http://127.0.0.1:8080
   🌐 الشبكة: http://*************:8080
```

**احفظ عنوان الشبكة:** `*************:8080`

---

## 📋 **الخطوة 2: إعداد تطبيق الاستقبال (أندرويد)**

### 2.1 تثبيت المتطلبات:
```bash
pip install kivy requests
```

### 2.2 تشغيل التطبيق:
```bash
python android_registrar.py
```

### 2.3 إعداد عنوان الخادم:
1. **اضغط "إعدادات الخادم"**
2. **أدخل عنوان الخادم:** `http://*************:8080`
3. **اضغط "حفظ"**

### 2.4 تسجيل الدخول:
- **البريد:** <EMAIL>
- **كلمة المرور:** registrar123

---

## 📋 **الخطوة 3: إعداد تطبيق الطبيب (أندرويد)**

### 3.1 تشغيل التطبيق:
```bash
python android_doctor.py
```

### 3.2 إعداد عنوان الخادم:
1. **اضغط "إعدادات الخادم"**
2. **أدخل نفس عنوان الخادم:** `http://*************:8080`
3. **اضغط "حفظ"**

### 3.3 تسجيل الدخول:
- **البريد:** <EMAIL>
- **كلمة المرور:** doctor123

---

## 📋 **الخطوة 4: اختبار النظام**

### 🧪 **الاختبار الأول:**

#### في تطبيق موظف الاستقبال:
1. **اضغط "إضافة مريض جديد"**
2. **املأ البيانات:**
   - الاسم الأول: أحمد
   - الاسم الأخير: محمد
   - تاريخ الميلاد: 1990-01-01
   - الجنس: male
   - الهاتف: 0123456789
3. **اضغط "حفظ"**

#### في تطبيق الطبيب:
1. **انتظر 10 ثواني**
2. **ستظهر نافذة تنبيه منبثقة** 🔔
3. **اضغط "تمييز كمقروء"**
4. **اذهب إلى "التنبيهات الجديدة"**

---

## 🔧 **إعدادات الشبكة:**

### 🌐 **للربط عبر الراوتر:**

#### تأكد من:
1. **جهاز الكمبيوتر متصل بالراوتر** (WiFi أو كابل)
2. **الهواتف متصلة بنفس الراوتر**
3. **نفس الشبكة المحلية** (مثل 192.168.1.x)

#### معرفة عنوان IP:
```bash
# في Windows
ipconfig

# في Linux/Mac
ifconfig
```

#### في التطبيقات:
- **استخدم عنوان IP الحقيقي** للكمبيوتر
- **مثال:** `http://*************:8080`
- **ليس:** `http://127.0.0.1:8080`

---

## 📱 **تحويل إلى APK (اختياري):**

### باستخدام Buildozer:
```bash
pip install buildozer
buildozer init
buildozer android debug
```

### أو باستخدام PyInstaller:
```bash
pip install pyinstaller
pyinstaller --onefile android_registrar.py
pyinstaller --onefile android_doctor.py
```

---

## 🔧 **حل المشاكل:**

### ❌ **"غير متصل بالخادم"**
**الحل:**
1. تأكد من تشغيل الخادم على الكمبيوتر
2. تأكد من عنوان IP صحيح
3. تأكد من اتصال الأجهزة بنفس الشبكة
4. تعطيل Firewall مؤقتاً للاختبار

### ❌ **"لا تظهر التنبيهات"**
**الحل:**
1. تأكد من تسجيل الدخول في كلا التطبيقين
2. انتظر 10-15 ثانية للتحديث التلقائي
3. اضغط "تحديث البيانات"

### ❌ **"Kivy لا يعمل"**
**الحل:**
```bash
pip install kivy[base]
# أو
pip install kivymd
```

### ❌ **"خطأ في الاتصال"**
**الحل:**
1. تحقق من عنوان IP: `ping *************`
2. تحقق من المنفذ: `telnet ************* 8080`
3. أعد تشغيل الخادم والتطبيقات

---

## 🌟 **الميزات المتاحة:**

### ✅ **تطبيق موظف الاستقبال:**
- تسجيل دخول آمن
- إضافة مرضى جدد
- عرض قائمة المرضى
- إعدادات الخادم
- تحديث البيانات

### ✅ **تطبيق الطبيب:**
- تسجيل دخول آمن
- استقبال تنبيهات فورية
- عرض التنبيهات الجديدة
- عرض قائمة المرضى
- لوحة معلومات
- تحديث تلقائي كل 10 ثواني

### ✅ **الخادم:**
- قاعدة بيانات SQLite محلية
- API RESTful كامل
- تنبيهات فورية
- إحصائيات شاملة
- أمان وتشفير

---

## 📁 **الملفات المطلوبة:**

### للخادم (الكمبيوتر):
- ✅ `android_server.py`

### لتطبيق الاستقبال:
- ✅ `android_registrar.py`

### لتطبيق الطبيب:
- ✅ `android_doctor.py`

---

## 🎯 **سير العمل النهائي:**

```
🖥️ الكمبيوتر (الخادم):
   ├── تشغيل android_server.py
   ├── معرفة عنوان IP
   └── ترك الخادم يعمل

📱 هاتف موظف الاستقبال:
   ├── تشغيل android_registrar.py
   ├── إعداد عنوان الخادم
   ├── تسجيل دخول
   └── إضافة المرضى

📱 هاتف الطبيب:
   ├── تشغيل android_doctor.py
   ├── إعداد عنوان الخادم
   ├── تسجيل دخول
   └── استقبال التنبيهات
```

---

## 🎉 **النتيجة النهائية:**

✅ **نظام كامل لإدارة العيادة**
✅ **تطبيقين أندرويد منفصلين**
✅ **ربط عبر الراوتر المحلي**
✅ **تنبيهات فورية وحقيقية**
✅ **لا حاجة لإنترنت**
✅ **واجهات عربية كاملة**

**🚀 ابدأ بتشغيل الخادم ثم التطبيقين!**
