#!/usr/bin/env python3
"""
Simple server starter for Clinineo
مشغل خادم بسيط لـ Clinineo
"""

import subprocess
import sys
import os
import time

def install_requirements():
    """Install required packages"""
    packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'python-jose[cryptography]',
        'passlib[bcrypt]',
        'python-multipart',
        'python-dotenv',
        'pydantic-settings'
    ]
    
    print("📦 Installing required packages...")
    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    return True

def start_backend():
    """Start the backend server"""
    print("🚀 Starting Clinineo Backend Server...")
    
    # Change to backend directory
    backend_dir = os.path.join(os.getcwd(), 'backend')
    if not os.path.exists(backend_dir):
        print("❌ Backend directory not found")
        return False
    
    os.chdir(backend_dir)
    
    try:
        # Start the server
        subprocess.run([
            sys.executable, '-m', 'uvicorn', 
            'main:app', 
            '--host', '127.0.0.1', 
            '--port', '8000', 
            '--reload'
        ])
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("=" * 50)
    print("🏥 Clinineo Clinic Management System")
    print("=" * 50)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        input("Press Enter to exit...")
        return
    
    print("\n✅ All requirements installed successfully!")
    print("🚀 Starting server...")
    print("📍 Server will be available at: http://127.0.0.1:8000")
    print("📚 API Documentation: http://127.0.0.1:8000/docs")
    print("🔍 Health Check: http://127.0.0.1:8000/health")
    print("=" * 50)
    
    # Start backend
    start_backend()

if __name__ == "__main__":
    main()
