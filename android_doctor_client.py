#!/usr/bin/env python3
"""
Android Doctor Client App
تطبيق الطبيب - يتصل بتطبيق الاستقبال مباشرة
"""

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
import requests
import json
from datetime import datetime

class DoctorClientApp(App):
    """تطبيق الطبيب - عميل"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.registrar_server_url = "http://192.168.1.100:8080"  # عنوان تطبيق الاستقبال
        self.current_user = None
        self.last_notification_count = 0
    
    def build(self):
        """بناء واجهة التطبيق"""
        self.title = "Clinineo - الطبيب (عميل)"
        
        # التخطيط الرئيسي
        main_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # شريط العنوان
        title_label = Label(
            text='نظام إدارة العيادة - الطبيب\n(متصل بتطبيق الاستقبال)',
            size_hint_y=None,
            height=60,
            font_size=18
        )
        main_layout.add_widget(title_label)
        
        # شريط الحالة والتنبيهات
        status_layout = BoxLayout(size_hint_y=None, height=40)
        
        self.status_label = Label(
            text='غير متصل بتطبيق الاستقبال',
            color=(1, 0, 0, 1)  # أحمر
        )
        status_layout.add_widget(self.status_label)
        
        self.notification_label = Label(
            text='',
            color=(0, 0, 1, 1)  # أزرق
        )
        status_layout.add_widget(self.notification_label)
        
        main_layout.add_widget(status_layout)
        
        # عنوان الخادم
        self.server_url_label = Label(
            text=f'الاتصال بـ: {self.registrar_server_url}',
            size_hint_y=None,
            height=30,
            color=(0, 0, 1, 1)
        )
        main_layout.add_widget(self.server_url_label)
        
        # منطقة المحتوى
        self.content_layout = BoxLayout(orientation='vertical', spacing=10)
        main_layout.add_widget(self.content_layout)
        
        # عرض الشاشة الرئيسية
        self.show_main_screen()
        
        # فحص الاتصال بتطبيق الاستقبال
        Clock.schedule_once(self.check_connection, 1)
        
        return main_layout
    
    def show_main_screen(self):
        """عرض الشاشة الرئيسية"""
        self.content_layout.clear_widgets()
        
        main_layout = GridLayout(cols=1, spacing=10, size_hint_y=None)
        main_layout.bind(minimum_height=main_layout.setter('height'))
        
        # تسجيل دخول تلقائي
        if not self.current_user:
            self.current_user = {
                "id": "2",
                "first_name": "د. أحمد",
                "last_name": "محمد",
                "role": "doctor",
                "specialization": "طب عام"
            }
        
        # ترحيب
        welcome_text = f"مرحباً {self.current_user['first_name']} {self.current_user['last_name']}"
        if self.current_user.get('specialization'):
            welcome_text += f"\n{self.current_user['specialization']}"
        
        main_layout.add_widget(Label(text=welcome_text, font_size=16, size_hint_y=None, height=60))
        
        # معلومات الاتصال
        connection_info = f"متصل بتطبيق الاستقبال:\n{self.registrar_server_url}"
        main_layout.add_widget(Label(text=connection_info, size_hint_y=None, height=60))
        
        # الأزرار الرئيسية
        notifications_btn = Button(
            text='التنبيهات الجديدة',
            size_hint_y=None,
            height=60
        )
        notifications_btn.bind(on_press=self.show_notifications)
        main_layout.add_widget(notifications_btn)
        
        patients_btn = Button(
            text='قائمة المرضى',
            size_hint_y=None,
            height=60
        )
        patients_btn.bind(on_press=self.show_patients_list)
        main_layout.add_widget(patients_btn)
        
        dashboard_btn = Button(
            text='لوحة المعلومات',
            size_hint_y=None,
            height=60
        )
        dashboard_btn.bind(on_press=self.show_dashboard)
        main_layout.add_widget(dashboard_btn)
        
        settings_btn = Button(
            text='إعدادات الاتصال',
            size_hint_y=None,
            height=60
        )
        settings_btn.bind(on_press=self.show_connection_settings)
        main_layout.add_widget(settings_btn)
        
        refresh_btn = Button(
            text='تحديث البيانات',
            size_hint_y=None,
            height=60
        )
        refresh_btn.bind(on_press=self.refresh_data)
        main_layout.add_widget(refresh_btn)
        
        # إضافة إلى التمرير
        scroll = ScrollView()
        scroll.add_widget(main_layout)
        self.content_layout.add_widget(scroll)
    
    def show_connection_settings(self, instance):
        """عرض إعدادات الاتصال"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        content.add_widget(Label(text='عنوان تطبيق الاستقبال:', size_hint_y=None, height=30))
        
        server_input = TextInput(
            text=self.registrar_server_url,
            hint_text='مثال: http://192.168.1.100:8080',
            multiline=False,
            size_hint_y=None,
            height=40
        )
        content.add_widget(server_input)
        
        content.add_widget(Label(text='تأكد من تشغيل تطبيق الاستقبال أولاً', size_hint_y=None, height=40))
        
        buttons_layout = BoxLayout(spacing=10, size_hint_y=None, height=50)
        
        save_btn = Button(text='حفظ')
        save_btn.bind(on_press=lambda x: self.save_connection_settings(server_input.text, popup))
        buttons_layout.add_widget(save_btn)
        
        test_btn = Button(text='اختبار الاتصال')
        test_btn.bind(on_press=lambda x: self.test_connection(server_input.text))
        buttons_layout.add_widget(test_btn)
        
        cancel_btn = Button(text='إلغاء')
        cancel_btn.bind(on_press=lambda x: popup.dismiss())
        buttons_layout.add_widget(cancel_btn)
        
        content.add_widget(buttons_layout)
        
        popup = Popup(
            title='إعدادات الاتصال',
            content=content,
            size_hint=(0.9, 0.7)
        )
        popup.open()
    
    def save_connection_settings(self, server_url, popup):
        """حفظ إعدادات الاتصال"""
        self.registrar_server_url = server_url.strip()
        self.server_url_label.text = f'الاتصال بـ: {self.registrar_server_url}'
        popup.dismiss()
        self.show_popup('تم حفظ إعدادات الاتصال')
        Clock.schedule_once(self.check_connection, 0.5)
    
    def test_connection(self, server_url):
        """اختبار الاتصال"""
        try:
            response = requests.get(f"{server_url}/api/health", timeout=3)
            if response.status_code == 200:
                self.show_popup('✅ الاتصال ناجح!')
            else:
                self.show_popup('❌ فشل الاتصال')
        except:
            self.show_popup('❌ لا يمكن الوصول لتطبيق الاستقبال')
    
    def check_connection(self, dt=None):
        """فحص الاتصال بتطبيق الاستقبال"""
        try:
            response = requests.get(f"{self.registrar_server_url}/api/health", timeout=3)
            if response.status_code == 200:
                self.status_label.text = 'متصل بتطبيق الاستقبال'
                self.status_label.color = (0, 1, 0, 1)  # أخضر
                
                # فحص التنبيهات
                self.check_notifications()
                
                # جدولة فحص دوري
                Clock.schedule_interval(self.check_notifications, 10)  # كل 10 ثواني
            else:
                self.status_label.text = 'خطأ في الاتصال'
                self.status_label.color = (1, 0, 0, 1)  # أحمر
        except:
            self.status_label.text = 'غير متصل بتطبيق الاستقبال'
            self.status_label.color = (1, 0, 0, 1)  # أحمر
    
    def check_notifications(self, dt=None):
        """فحص التنبيهات الجديدة"""
        try:
            response = requests.get(f"{self.registrar_server_url}/api/notifications", timeout=3)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    notifications = data.get("data", [])
                    unread_notifications = [n for n in notifications if not n.get("is_read", False)]
                    count = len(unread_notifications)
                    
                    if count > 0:
                        self.notification_label.text = f'🔔 {count} تنبيه جديد'
                        self.notification_label.color = (1, 0, 0, 1)  # أحمر
                        
                        # إذا كان هناك تنبيهات جديدة، أظهر رسالة
                        if count > self.last_notification_count:
                            self.show_notification_popup(unread_notifications[0])
                    else:
                        self.notification_label.text = 'لا توجد تنبيهات جديدة'
                        self.notification_label.color = (0, 1, 0, 1)  # أخضر
                    
                    self.last_notification_count = count
                    
        except Exception as e:
            print(f"خطأ في فحص التنبيهات: {e}")
    
    def show_notification_popup(self, notification):
        """عرض تنبيه منبثق"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        content.add_widget(Label(text=notification.get('title', ''), font_size=16))
        content.add_widget(Label(text=notification.get('message', '')))
        
        buttons_layout = BoxLayout(spacing=10, size_hint_y=None, height=50)
        
        read_btn = Button(text='تمييز كمقروء')
        read_btn.bind(on_press=lambda x: self.mark_notification_read(notification['id'], popup))
        buttons_layout.add_widget(read_btn)
        
        close_btn = Button(text='إغلاق')
        close_btn.bind(on_press=lambda x: popup.dismiss())
        buttons_layout.add_widget(close_btn)
        
        content.add_widget(buttons_layout)
        
        popup = Popup(
            title='🔔 تنبيه جديد',
            content=content,
            size_hint=(0.8, 0.6)
        )
        popup.open()
    
    def mark_notification_read(self, notification_id, popup):
        """تمييز التنبيه كمقروء"""
        try:
            response = requests.post(
                f"{self.registrar_server_url}/api/notifications/read",
                json={"notification_id": notification_id},
                timeout=3
            )
            
            if response.status_code == 200:
                popup.dismiss()
                self.check_notifications()
                
        except Exception as e:
            print(f"خطأ في تمييز التنبيه: {e}")
    
    def show_notifications(self, instance):
        """عرض جميع التنبيهات"""
        try:
            response = requests.get(f"{self.registrar_server_url}/api/notifications", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    notifications = data.get("data", [])
                    self.display_notifications_popup(notifications)
                else:
                    self.show_popup("فشل في تحميل التنبيهات")
            else:
                self.show_popup("خطأ في الاتصال")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def display_notifications_popup(self, notifications):
        """عرض التنبيهات في نافذة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        notifications_layout = GridLayout(cols=1, spacing=5, size_hint_y=None)
        notifications_layout.bind(minimum_height=notifications_layout.setter('height'))
        
        notifications_layout.add_widget(Label(text=f'التنبيهات ({len(notifications)})', font_size=16, size_hint_y=None, height=40))
        
        if notifications:
            for notification in notifications:
                status = "✅" if notification.get('is_read') else "🔔"
                time_str = notification.get('created_at', '')[:16].replace('T', ' ')
                notification_text = f"{status} {time_str}\n{notification.get('title', '')}\n{notification.get('message', '')}"
                
                notification_label = Label(
                    text=notification_text,
                    size_hint_y=None,
                    height=80,
                    text_size=(None, None)
                )
                notifications_layout.add_widget(notification_label)
        else:
            notifications_layout.add_widget(Label(text='لا توجد تنبيهات', size_hint_y=None, height=40))
        
        scroll = ScrollView()
        scroll.add_widget(notifications_layout)
        content.add_widget(scroll)
        
        close_btn = Button(text='إغلاق', size_hint_y=None, height=50)
        close_btn.bind(on_press=lambda x: popup.dismiss())
        content.add_widget(close_btn)
        
        popup = Popup(
            title='جميع التنبيهات',
            content=content,
            size_hint=(0.9, 0.8)
        )
        popup.open()
    
    def show_patients_list(self, instance):
        """عرض قائمة المرضى"""
        try:
            response = requests.get(f"{self.registrar_server_url}/api/patients", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    patients = data.get("data", [])
                    self.display_patients_popup(patients)
                else:
                    self.show_popup("فشل في تحميل المرضى")
            else:
                self.show_popup("خطأ في الاتصال")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def display_patients_popup(self, patients):
        """عرض قائمة المرضى في نافذة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        patients_layout = GridLayout(cols=1, spacing=5, size_hint_y=None)
        patients_layout.bind(minimum_height=patients_layout.setter('height'))
        
        patients_layout.add_widget(Label(text=f'قائمة المرضى ({len(patients)})', font_size=16, size_hint_y=None, height=40))
        
        for patient in patients:
            patient_text = f"{patient.get('first_name', '')} {patient.get('last_name', '')} - {patient.get('phone', '')}"
            patients_layout.add_widget(Label(text=patient_text, size_hint_y=None, height=30))
        
        scroll = ScrollView()
        scroll.add_widget(patients_layout)
        content.add_widget(scroll)
        
        close_btn = Button(text='إغلاق', size_hint_y=None, height=50)
        close_btn.bind(on_press=lambda x: popup.dismiss())
        content.add_widget(close_btn)
        
        popup = Popup(
            title='قائمة المرضى',
            content=content,
            size_hint=(0.9, 0.8)
        )
        popup.open()
    
    def show_dashboard(self, instance):
        """عرض لوحة المعلومات"""
        try:
            response = requests.get(f"{self.registrar_server_url}/api/dashboard", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    stats = data.get("data", {})
                    self.display_dashboard_popup(stats)
                else:
                    self.show_popup("فشل في تحميل الإحصائيات")
            else:
                self.show_popup("خطأ في الاتصال")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def display_dashboard_popup(self, stats):
        """عرض لوحة المعلومات في نافذة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        stats_layout = GridLayout(cols=1, spacing=10, size_hint_y=None)
        stats_layout.bind(minimum_height=stats_layout.setter('height'))
        
        stats_layout.add_widget(Label(text='لوحة المعلومات', font_size=18, size_hint_y=None, height=40))
        
        stats_layout.add_widget(Label(text=f"إجمالي المرضى: {stats.get('total_patients', 0)}", size_hint_y=None, height=30))
        stats_layout.add_widget(Label(text=f"إجمالي التنبيهات: {stats.get('total_notifications', 0)}", size_hint_y=None, height=30))
        stats_layout.add_widget(Label(text=f"التنبيهات غير المقروءة: {stats.get('unread_notifications', 0)}", size_hint_y=None, height=30))
        stats_layout.add_widget(Label(text=f"حالة الخادم: {stats.get('server_status', 'غير معروف')}", size_hint_y=None, height=30))
        
        content.add_widget(stats_layout)
        
        close_btn = Button(text='إغلاق', size_hint_y=None, height=50)
        close_btn.bind(on_press=lambda x: popup.dismiss())
        content.add_widget(close_btn)
        
        popup = Popup(
            title='لوحة المعلومات',
            content=content,
            size_hint=(0.8, 0.6)
        )
        popup.open()
    
    def refresh_data(self, instance):
        """تحديث البيانات"""
        self.check_connection()
        self.show_popup('تم تحديث البيانات')
    
    def show_popup(self, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        content.add_widget(Label(text=message))
        
        close_btn = Button(text='موافق', size_hint_y=None, height=50)
        content.add_widget(close_btn)
        
        popup = Popup(
            title='رسالة',
            content=content,
            size_hint=(0.8, 0.4)
        )
        
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

if __name__ == '__main__':
    DoctorClientApp().run()
