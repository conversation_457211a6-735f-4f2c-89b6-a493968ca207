#!/usr/bin/env python3
"""
Quick start script for Clinineo Desktop Application
سكريبت بدء سريع لتطبيق سطح المكتب
"""

import sys
import os
import subprocess

def check_requirements():
    """Check if required packages are installed"""
    required_packages = [
        'tkinter',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Installing missing packages...")
        
        for package in missing_packages:
            if package == 'tkinter':
                print("⚠️  tkinter usually comes with Python. Please check your Python installation.")
            else:
                try:
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                    print(f"✅ {package} installed successfully")
                except subprocess.CalledProcessError:
                    print(f"❌ Failed to install {package}")
                    return False
    
    return True

def main():
    """Main function"""
    print("🚀 Starting Clinineo Desktop Application...")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        print("❌ Failed to install required packages")
        input("Press Enter to exit...")
        return
    
    # Add current directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    try:
        # Import and run the application
        from main import main as app_main
        app_main()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all required files are present")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"❌ Application error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
