<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinineo Simple - نظام العيادة البسيط</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }
        
        .content {
            padding: 25px;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 18px;
            margin: 12px 0;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        
        .btn-primary { background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%); }
        .btn-success { background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%); }
        .btn-warning { background: linear-gradient(135deg, #ff9800 0%, #ffc107 100%); }
        .btn-secondary { background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%); }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 5px solid #667eea;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .patient-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #4caf50;
        }
        
        .hidden {
            display: none;
        }
        
        .success-msg {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        
        .notification {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 0;
                border-radius: 0;
                min-height: 100vh;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الصفحة الرئيسية -->
        <div id="home-page">
            <div class="header">
                <h1>🏥 Clinineo Simple</h1>
                <p>نظام إدارة العيادة البسيط</p>
                <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 10px; margin-top: 15px;">
                    <span id="current-time"></span>
                </div>
            </div>
            <div class="content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-patients">0</div>
                        <div class="stat-label">إجمالي المرضى</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="total-notifications">0</div>
                        <div class="stat-label">التنبيهات</div>
                    </div>
                </div>
                
                <button onclick="showPage('registrar')" class="btn btn-primary">
                    🏥 تطبيق الاستقبال
                </button>
                
                <button onclick="showPage('doctor')" class="btn btn-success">
                    🩺 تطبيق الطبيب
                </button>
                
                <div class="card">
                    <h4>📱 نظام بسيط بدون خادم</h4>
                    <p>يعمل بملفات HTML فقط - لا يحتاج تثبيت</p>
                    <p>البيانات محفوظة في المتصفح محلياً</p>
                </div>
            </div>
        </div>
        
        <!-- صفحة الاستقبال -->
        <div id="registrar-page" class="hidden">
            <div class="header" style="background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);">
                <h1>🏥 الاستقبال</h1>
                <p>إدارة المرضى والمواعيد</p>
            </div>
            <div class="content">
                <button onclick="showPage('home')" class="btn btn-secondary">← العودة للرئيسية</button>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="registrar-patients">0</div>
                        <div class="stat-label">إجمالي المرضى</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="today-patients">0</div>
                        <div class="stat-label">مرضى اليوم</div>
                    </div>
                </div>
                
                <button onclick="showPage('add-patient')" class="btn btn-primary">
                    ➕ إضافة مريض جديد
                </button>
                
                <button onclick="showPage('patients-list')" class="btn btn-primary">
                    👥 عرض المرضى
                </button>
            </div>
        </div>
        
        <!-- صفحة الطبيب -->
        <div id="doctor-page" class="hidden">
            <div class="header" style="background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);">
                <h1>🩺 د. أحمد محمد</h1>
                <p>طب عام - عيادة Clinineo</p>
            </div>
            <div class="content">
                <button onclick="showPage('home')" class="btn btn-secondary">← العودة للرئيسية</button>
                
                <div id="notifications-alert" class="notification hidden">
                    🔔 لديك تنبيهات جديدة!
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="doctor-patients">0</div>
                        <div class="stat-label">المرضى</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="doctor-notifications">0</div>
                        <div class="stat-label">التنبيهات</div>
                    </div>
                </div>
                
                <button onclick="showPage('notifications')" class="btn btn-warning">
                    🔔 التنبيهات
                </button>
                
                <button onclick="showPage('patients-list')" class="btn btn-success">
                    👥 المرضى
                </button>
                
                <button onclick="showPage('prescriptions')" class="btn btn-success">
                    📝 الروشتات
                </button>
            </div>
        </div>
        
        <!-- صفحة إضافة مريض -->
        <div id="add-patient-page" class="hidden">
            <div class="header" style="background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);">
                <h1>➕ إضافة مريض جديد</h1>
                <p>إدخال بيانات المريض</p>
            </div>
            <div class="content">
                <button onclick="showPage('registrar')" class="btn btn-secondary">← العودة</button>
                
                <div id="success-message" class="success-msg hidden">
                    ✅ تم إضافة المريض بنجاح!
                </div>
                
                <form id="patient-form">
                    <div class="form-group">
                        <label>الاسم الكامل *</label>
                        <input type="text" id="patient-name" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label>رقم الهاتف *</label>
                        <input type="tel" id="patient-phone" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label>العمر</label>
                        <input type="number" id="patient-age" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label>الجنس</label>
                        <select id="patient-gender" class="form-control">
                            <option value="">اختر الجنس</option>
                            <option value="male">ذكر</option>
                            <option value="female">أنثى</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea id="patient-notes" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-success">
                        💾 حفظ المريض
                    </button>
                </form>
            </div>
        </div>
        
        <!-- صفحة قائمة المرضى -->
        <div id="patients-list-page" class="hidden">
            <div class="header" style="background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);">
                <h1>👥 قائمة المرضى</h1>
                <p>جميع مرضى العيادة</p>
            </div>
            <div class="content">
                <button onclick="showPage('registrar')" class="btn btn-secondary">← العودة</button>
                
                <div id="patients-container">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
        
        <!-- صفحة التنبيهات -->
        <div id="notifications-page" class="hidden">
            <div class="header" style="background: linear-gradient(135deg, #ff9800 0%, #ffc107 100%);">
                <h1>🔔 التنبيهات</h1>
                <p>تنبيهات العيادة</p>
            </div>
            <div class="content">
                <button onclick="showPage('doctor')" class="btn btn-secondary">← العودة</button>
                
                <div id="notifications-container">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
        
        <!-- صفحة الروشتات -->
        <div id="prescriptions-page" class="hidden">
            <div class="header" style="background: linear-gradient(135deg, #9c27b0 0%, #e1bee7 100%);">
                <h1>📝 الروشتات</h1>
                <p>إدارة الروشتات الطبية</p>
            </div>
            <div class="content">
                <button onclick="showPage('doctor')" class="btn btn-secondary">← العودة</button>
                
                <div class="card">
                    <h4>📝 نظام الروشتات</h4>
                    <p>قريباً - سيتم إضافة نظام الروشتات الكامل</p>
                    <p>سيشمل: التشخيص، الأدوية، التعليمات</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات النظام
        let clinicData = {
            patients: [],
            notifications: []
        };

        // تحميل البيانات من localStorage
        function loadData() {
            const saved = localStorage.getItem('clinineo-data');
            if (saved) {
                clinicData = JSON.parse(saved);
            }
            updateStats();
        }

        // حفظ البيانات في localStorage
        function saveData() {
            localStorage.setItem('clinineo-data', JSON.stringify(clinicData));
            updateStats();
        }

        // تحديث الإحصائيات
        function updateStats() {
            const totalPatients = clinicData.patients.length;
            const totalNotifications = clinicData.notifications.filter(n => !n.read).length;
            const today = new Date().toISOString().split('T')[0];
            const todayPatients = clinicData.patients.filter(p => p.date.startsWith(today)).length;

            document.getElementById('total-patients').textContent = totalPatients;
            document.getElementById('total-notifications').textContent = totalNotifications;
            document.getElementById('registrar-patients').textContent = totalPatients;
            document.getElementById('today-patients').textContent = todayPatients;
            document.getElementById('doctor-patients').textContent = totalPatients;
            document.getElementById('doctor-notifications').textContent = totalNotifications;

            // إظهار تنبيه للطبيب
            if (totalNotifications > 0) {
                document.getElementById('notifications-alert').classList.remove('hidden');
            } else {
                document.getElementById('notifications-alert').classList.add('hidden');
            }
        }

        // إظهار صفحة معينة
        function showPage(pageId) {
            // إخفاء جميع الصفحات
            const pages = document.querySelectorAll('[id$="-page"]');
            pages.forEach(page => page.classList.add('hidden'));

            // إظهار الصفحة المطلوبة
            document.getElementById(pageId + '-page').classList.remove('hidden');

            // تحديث المحتوى حسب الصفحة
            if (pageId === 'patients-list') {
                displayPatients();
            } else if (pageId === 'notifications') {
                displayNotifications();
            }
        }

        // إضافة مريض جديد
        document.getElementById('patient-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const patient = {
                id: Date.now().toString(),
                name: document.getElementById('patient-name').value,
                phone: document.getElementById('patient-phone').value,
                age: document.getElementById('patient-age').value,
                gender: document.getElementById('patient-gender').value,
                notes: document.getElementById('patient-notes').value,
                date: new Date().toISOString()
            };

            clinicData.patients.unshift(patient);

            // إضافة تنبيه للطبيب
            const notification = {
                id: Date.now().toString(),
                message: `مريض جديد: ${patient.name}`,
                date: new Date().toISOString(),
                read: false
            };
            clinicData.notifications.unshift(notification);

            saveData();

            // إظهار رسالة نجاح
            document.getElementById('success-message').classList.remove('hidden');
            setTimeout(() => {
                document.getElementById('success-message').classList.add('hidden');
            }, 3000);

            // مسح النموذج
            document.getElementById('patient-form').reset();
        });

        // عرض المرضى
        function displayPatients() {
            const container = document.getElementById('patients-container');
            
            if (clinicData.patients.length === 0) {
                container.innerHTML = '<div class="card"><p>لا توجد مرضى مسجلين</p></div>';
                return;
            }

            let html = '';
            clinicData.patients.forEach(patient => {
                html += `
                    <div class="patient-card">
                        <h4>👤 ${patient.name}</h4>
                        <p>📞 ${patient.phone}</p>
                        ${patient.age ? `<p>🎂 العمر: ${patient.age}</p>` : ''}
                        ${patient.gender ? `<p>👤 الجنس: ${patient.gender === 'male' ? 'ذكر' : 'أنثى'}</p>` : ''}
                        <p>📅 ${new Date(patient.date).toLocaleDateString('ar-SA')}</p>
                        ${patient.notes ? `<p>📝 ${patient.notes}</p>` : ''}
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // عرض التنبيهات
        function displayNotifications() {
            const container = document.getElementById('notifications-container');
            
            if (clinicData.notifications.length === 0) {
                container.innerHTML = '<div class="card"><p>لا توجد تنبيهات</p></div>';
                return;
            }

            let html = '';
            clinicData.notifications.forEach(notification => {
                html += `
                    <div class="notification">
                        <p>${notification.message}</p>
                        <small>${new Date(notification.date).toLocaleString('ar-SA')}</small>
                    </div>
                `;
            });

            container.innerHTML = html;

            // تحديد التنبيهات كمقروءة
            clinicData.notifications.forEach(n => n.read = true);
            saveData();
        }

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = 
                `🕒 ${now.toLocaleTimeString('ar-SA')} - ${now.toLocaleDateString('ar-SA')}`;
        }

        // تشغيل التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            updateTime();
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>
