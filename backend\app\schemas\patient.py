"""
Patient schemas for API requests and responses
نماذج المرضى لطلبات واستجابات API
"""

from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime, date

class PatientBase(BaseModel):
    """Base patient schema"""
    first_name: str
    last_name: str
    date_of_birth: date
    gender: str
    phone: str
    email: Optional[str] = None
    address: Optional[str] = None
    emergency_contact: Optional[str] = None
    blood_type: Optional[str] = None
    allergies: Optional[str] = None
    medical_history: Optional[str] = None
    insurance_number: Optional[str] = None

class PatientCreate(PatientBase):
    """Schema for creating a new patient"""
    
    @validator('gender')
    def validate_gender(cls, v):
        allowed_genders = ['male', 'female']
        if v not in allowed_genders:
            raise ValueError(f'Gender must be one of: {", ".join(allowed_genders)}')
        return v
    
    @validator('blood_type')
    def validate_blood_type(cls, v):
        if v is not None:
            allowed_types = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
            if v not in allowed_types:
                raise ValueError(f'Blood type must be one of: {", ".join(allowed_types)}')
        return v
    
    @validator('date_of_birth')
    def validate_date_of_birth(cls, v):
        if v > date.today():
            raise ValueError('Date of birth cannot be in the future')
        return v

class PatientUpdate(BaseModel):
    """Schema for updating patient information"""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    date_of_birth: Optional[date] = None
    gender: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    emergency_contact: Optional[str] = None
    blood_type: Optional[str] = None
    allergies: Optional[str] = None
    medical_history: Optional[str] = None
    insurance_number: Optional[str] = None

class PatientResponse(PatientBase):
    """Schema for patient response"""
    id: str
    created_at: datetime
    
    class Config:
        from_attributes = True

class PatientSearch(BaseModel):
    """Schema for patient search"""
    query: Optional[str] = None
    gender: Optional[str] = None
    blood_type: Optional[str] = None
    age_min: Optional[int] = None
    age_max: Optional[int] = None
