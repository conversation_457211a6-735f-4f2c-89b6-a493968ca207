# 🚀 كيفية تشغيل نظام Clinineo

## 📋 **الطرق المتاحة للتشغيل:**

### 🎯 **الطريقة الأولى: التشغيل المنفصل (الأسهل)**

#### 1. تشغيل الخادم:
```bash
python run_server.py
```
أو
```bash
python local_server.py
```

#### 2. تشغيل تطبيق موظف الاستقبال (نافذة جديدة):
```bash
python run_registrar.py
```
أو
```bash
python registrar_app.py
```

#### 3. تشغيل تطبيق الطبيب (نافذة جديدة):
```bash
python run_doctor.py
```
أو
```bash
python doctor_app.py
```

---

### 🎛️ **الطريقة الثانية: المشغل الشامل**
```bash
python start_clinic.py
```

ثم اضغط على الأزرار في النافذة:
1. "تشغيل الخادم"
2. "تطبيق موظف الاستقبال"
3. "تطبيق الطبيب"

---

## 🔧 **إذا لم تظهر التطبيقات:**

### الحل الأول: التشغيل اليدوي
افتح **3 نوافذ Command Prompt** منفصلة:

**النافذة الأولى (الخادم):**
```bash
python local_server.py
```

**النافذة الثانية (موظف الاستقبال):**
```bash
python registrar_app.py
```

**النافذة الثالثة (الطبيب):**
```bash
python doctor_app.py
```

### الحل الثاني: استخدام ملفات التشغيل المباشرة
```bash
# في نافذة أولى
python run_server.py

# في نافذة ثانية
python run_registrar.py

# في نافذة ثالثة
python run_doctor.py
```

---

## 🔑 **بيانات تسجيل الدخول:**

| التطبيق | البريد الإلكتروني | كلمة المرور |
|---------|------------------|-------------|
| **تطبيق الطبيب** | <EMAIL> | doctor123 |
| **تطبيق موظف الاستقبال** | <EMAIL> | registrar123 |

---

## 🌐 **للربط عبر الشبكة المحلية:**

### 1. اعرف عنوان IP للجهاز الرئيسي:
```bash
# في Windows
ipconfig

# في Linux/Mac
ifconfig
```

### 2. في التطبيقات الأخرى:
- اذهب إلى "الإعدادات" → "إعدادات الخادم"
- أدخل عنوان IP للجهاز الرئيسي
- المنفذ: 8080

**مثال:** إذا كان IP الجهاز الرئيسي `*************`
- عنوان الخادم: `*************`
- المنفذ: `8080`

---

## ✅ **التحقق من عمل النظام:**

### 1. تحقق من الخادم:
افتح المتصفح واذهب إلى: http://127.0.0.1:8080

يجب أن ترى رسالة ترحيب.

### 2. تحقق من التطبيقات:
- يجب أن تظهر نوافذ منفصلة لكل تطبيق
- يجب أن تظهر رسالة "متصل بالخادم" باللون الأخضر

---

## 🔧 **حل المشاكل الشائعة:**

### مشكلة: "غير متصل بالخادم"
**الحل:**
1. تأكد من تشغيل الخادم أولاً
2. في التطبيق، اذهب إلى "الإعدادات" → "إعدادات الخادم"
3. تأكد من العنوان: `127.0.0.1` والمنفذ: `8080`

### مشكلة: "التطبيق لا يظهر"
**الحل:**
1. استخدم التشغيل اليدوي (3 نوافذ منفصلة)
2. تأكد من وجود جميع الملفات في نفس المجلد
3. تحقق من عدم وجود أخطاء في Command Prompt

### مشكلة: "Python is not recognized"
**الحل:**
1. تأكد من تثبيت Python
2. أضف Python إلى PATH
3. أعد تشغيل Command Prompt

---

## 📁 **الملفات المطلوبة:**

تأكد من وجود هذه الملفات في نفس المجلد:
- ✅ `local_server.py`
- ✅ `doctor_app.py`
- ✅ `registrar_app.py`
- ✅ `start_clinic.py`
- ✅ `run_server.py`
- ✅ `run_doctor.py`
- ✅ `run_registrar.py`

---

## 🎯 **الطريقة المضمونة 100%:**

### الخطوة 1: افتح Command Prompt الأول
```bash
python local_server.py
```
انتظر حتى ترى رسالة "الخادم يعمل على..."

### الخطوة 2: افتح Command Prompt الثاني
```bash
python registrar_app.py
```

### الخطوة 3: افتح Command Prompt الثالث
```bash
python doctor_app.py
```

### الخطوة 4: سجل دخول في كل تطبيق
- استخدم البيانات المذكورة أعلاه

**🎉 الآن النظام يعمل بالكامل!**
