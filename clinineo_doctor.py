#!/usr/bin/env python3
"""
Clinineo Doctor App - Premium Android Interface
تطبيق Clinineo للطبيب - واجهة أندرويد مميزة
"""

from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.floatlayout import FloatLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.uix.progressbar import ProgressBar
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.graphics import Color, RoundedRectangle, Line
from kivy.uix.widget import Widget
from kivy.animation import Animation
import json
import os
import requests
from datetime import datetime
import threading

class StyledButton(Button):
    """زر مخصص بتصميم مميز"""
    
    def __init__(self, bg_color=(0.3, 0.7, 0.3, 1), **kwargs):
        super().__init__(**kwargs)
        self.background_color = (0, 0, 0, 0)  # شفاف
        self.bg_color = bg_color
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        
        with self.canvas.before:
            Color(*self.bg_color)
            self.rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[15])
    
    def update_graphics(self, *args):
        self.rect.pos = self.pos
        self.rect.size = self.size

class NotificationCard(Widget):
    """بطاقة تنبيه مخصصة"""
    
    def __init__(self, notification_data, **kwargs):
        super().__init__(**kwargs)
        self.notification_data = notification_data
        self.size_hint_y = None
        self.height = dp(100)
        
        with self.canvas.before:
            Color(1, 0.95, 0.8, 1)  # خلفية صفراء فاتحة
            self.rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[10])
            Color(1, 0.7, 0, 1)  # حدود برتقالية
            self.border = Line(rounded_rectangle=(self.x, self.y, self.width, self.height, 10), width=2)
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        self.build_content()
    
    def build_content(self):
        """بناء محتوى التنبيه"""
        layout = BoxLayout(orientation='horizontal', padding=dp(15), spacing=dp(10))
        
        # Icon
        icon_label = Label(
            text='🔔',
            font_size=dp(24),
            size_hint_x=None,
            width=dp(40)
        )
        
        # Content
        content_layout = BoxLayout(orientation='vertical', spacing=dp(5))
        
        title_label = Label(
            text=self.notification_data.get('title', ''),
            font_size=dp(16),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        title_label.bind(size=title_label.setter('text_size'))
        
        message_label = Label(
            text=self.notification_data.get('message', ''),
            font_size=dp(14),
            color=(0.4, 0.4, 0.4, 1),
            size_hint_y=None,
            height=dp(40),
            halign='right',
            text_size=(None, None)
        )
        message_label.bind(size=message_label.setter('text_size'))
        
        time_label = Label(
            text=self.notification_data.get('created_at', '')[:16].replace('T', ' '),
            font_size=dp(12),
            color=(0.6, 0.6, 0.6, 1),
            size_hint_y=None,
            height=dp(20),
            halign='right'
        )
        time_label.bind(size=time_label.setter('text_size'))
        
        content_layout.add_widget(title_label)
        content_layout.add_widget(message_label)
        content_layout.add_widget(time_label)
        
        # Action Button
        action_btn = Button(
            text='✓',
            font_size=dp(18),
            size_hint_x=None,
            width=dp(50),
            background_color=(0.3, 0.7, 0.3, 1)
        )
        action_btn.bind(on_press=self.mark_as_read)
        
        layout.add_widget(icon_label)
        layout.add_widget(content_layout)
        layout.add_widget(action_btn)
        
        self.add_widget(layout)
    
    def mark_as_read(self, instance):
        """تمييز التنبيه كمقروء"""
        # Animation to fade out
        anim = Animation(opacity=0.3, duration=0.5)
        anim.start(self)
        
        # Remove from notifications list
        Clock.schedule_once(lambda dt: self.parent.remove_widget(self), 0.5)
    
    def update_graphics(self, *args):
        self.rect.pos = self.pos
        self.rect.size = self.size
        self.border.rounded_rectangle = (self.x, self.y, self.width, self.height, 10)

class PatientCard(Widget):
    """بطاقة مريض مخصصة"""
    
    def __init__(self, patient_data, **kwargs):
        super().__init__(**kwargs)
        self.patient_data = patient_data
        self.size_hint_y = None
        self.height = dp(120)
        
        with self.canvas.before:
            Color(0.95, 0.98, 1, 1)  # خلفية زرقاء فاتحة
            self.rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[10])
            Color(0.2, 0.6, 1, 1)  # حدود زرقاء
            self.border = Line(rounded_rectangle=(self.x, self.y, self.width, self.height, 10), width=2)
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        self.build_content()
    
    def build_content(self):
        """بناء محتوى بطاقة المريض"""
        layout = BoxLayout(orientation='horizontal', padding=dp(15), spacing=dp(15))
        
        # Avatar
        avatar_layout = BoxLayout(orientation='vertical', size_hint_x=None, width=dp(60))
        avatar_label = Label(
            text='👤' if self.patient_data.get('gender') == 'male' else '👩',
            font_size=dp(32),
            size_hint_y=None,
            height=dp(50)
        )
        avatar_layout.add_widget(avatar_label)
        
        # Patient Info
        info_layout = BoxLayout(orientation='vertical', spacing=dp(5))
        
        name_label = Label(
            text=f"{self.patient_data.get('first_name', '')} {self.patient_data.get('last_name', '')}",
            font_size=dp(18),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(30),
            halign='right'
        )
        name_label.bind(size=name_label.setter('text_size'))
        
        details_label = Label(
            text=f"📞 {self.patient_data.get('phone', '')} | 🎂 {self.patient_data.get('date_of_birth', '')}",
            font_size=dp(14),
            color=(0.4, 0.4, 0.4, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        details_label.bind(size=details_label.setter('text_size'))
        
        blood_label = Label(
            text=f"🩸 {self.patient_data.get('blood_type', 'غير محدد')}",
            font_size=dp(14),
            color=(0.6, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        blood_label.bind(size=blood_label.setter('text_size'))
        
        time_label = Label(
            text=f"📅 {self.patient_data.get('created_at', '')[:10]}",
            font_size=dp(12),
            color=(0.6, 0.6, 0.6, 1),
            size_hint_y=None,
            height=dp(20),
            halign='right'
        )
        time_label.bind(size=time_label.setter('text_size'))
        
        info_layout.add_widget(name_label)
        info_layout.add_widget(details_label)
        info_layout.add_widget(blood_label)
        info_layout.add_widget(time_label)
        
        # Action Buttons
        actions_layout = BoxLayout(orientation='vertical', size_hint_x=None, width=dp(80), spacing=dp(5))
        
        view_btn = Button(
            text='👁️',
            font_size=dp(16),
            size_hint_y=None,
            height=dp(35),
            background_color=(0.2, 0.6, 1, 1)
        )
        
        edit_btn = Button(
            text='✏️',
            font_size=dp(16),
            size_hint_y=None,
            height=dp(35),
            background_color=(0.9, 0.6, 0.1, 1)
        )
        
        actions_layout.add_widget(view_btn)
        actions_layout.add_widget(edit_btn)
        
        layout.add_widget(avatar_layout)
        layout.add_widget(info_layout)
        layout.add_widget(actions_layout)
        
        self.add_widget(layout)
    
    def update_graphics(self, *args):
        self.rect.pos = self.pos
        self.rect.size = self.size
        self.border.rounded_rectangle = (self.x, self.y, self.width, self.height, 10)

class DoctorHomeScreen(Screen):
    """الشاشة الرئيسية للطبيب"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'doctor_home'
        self.server_url = "http://192.168.1.100:8080"
        self.build_ui()
    
    def build_ui(self):
        """بناء واجهة الشاشة الرئيسية"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))
        
        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(80))
        
        # Doctor Info
        doctor_layout = BoxLayout(orientation='vertical')
        doctor_label = Label(
            text='🩺 د. أحمد محمد',
            font_size=dp(24),
            bold=True,
            color=(0.3, 0.7, 0.3, 1),
            size_hint_y=None,
            height=dp(40)
        )
        specialty_label = Label(
            text='طب عام | Clinineo',
            font_size=dp(16),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(30)
        )
        
        doctor_layout.add_widget(doctor_label)
        doctor_layout.add_widget(specialty_label)
        header.add_widget(doctor_layout)
        
        # Connection Status
        self.status_label = Label(
            text='🔴 غير متصل',
            font_size=dp(14),
            color=(1, 0, 0, 1),
            size_hint_x=None,
            width=dp(120)
        )
        header.add_widget(self.status_label)
        
        main_layout.add_widget(header)
        
        # Quick Stats
        stats_layout = GridLayout(cols=3, spacing=dp(10), size_hint_y=None, height=dp(100))
        
        # Notifications Count
        notifications_card = self.create_quick_stat("🔔", "تنبيهات", "0", (1, 0.6, 0, 1))
        stats_layout.add_widget(notifications_card)
        
        # Patients Count
        patients_card = self.create_quick_stat("👥", "مرضى", "0", (0.2, 0.6, 1, 1))
        stats_layout.add_widget(patients_card)
        
        # Today's Appointments
        appointments_card = self.create_quick_stat("📅", "مواعيد", "0", (0.3, 0.7, 0.3, 1))
        stats_layout.add_widget(appointments_card)
        
        main_layout.add_widget(stats_layout)
        
        # Action Buttons
        buttons_layout = GridLayout(cols=2, spacing=dp(15), size_hint_y=None, height=dp(140))
        
        # Notifications Button
        notifications_btn = StyledButton(
            text='🔔\nالتنبيهات',
            font_size=dp(16),
            bg_color=(1, 0.6, 0, 1)
        )
        notifications_btn.bind(on_press=self.go_to_notifications)
        
        # Patients Button
        patients_btn = StyledButton(
            text='👥\nالمرضى',
            font_size=dp(16),
            bg_color=(0.2, 0.6, 1, 1)
        )
        patients_btn.bind(on_press=self.go_to_patients)
        
        # Appointments Button
        appointments_btn = StyledButton(
            text='📅\nالمواعيد',
            font_size=dp(16),
            bg_color=(0.3, 0.7, 0.3, 1)
        )
        appointments_btn.bind(on_press=self.go_to_appointments)
        
        # Settings Button
        settings_btn = StyledButton(
            text='⚙️\nالإعدادات',
            font_size=dp(16),
            bg_color=(0.6, 0.6, 0.6, 1)
        )
        settings_btn.bind(on_press=self.go_to_settings)
        
        buttons_layout.add_widget(notifications_btn)
        buttons_layout.add_widget(patients_btn)
        buttons_layout.add_widget(appointments_btn)
        buttons_layout.add_widget(settings_btn)
        
        main_layout.add_widget(buttons_layout)
        
        # Recent Notifications Section
        notifications_section = BoxLayout(orientation='vertical', spacing=dp(10))
        
        notifications_header = Label(
            text='📋 آخر التنبيهات',
            font_size=dp(18),
            bold=True,
            color=(0.3, 0.3, 0.3, 1),
            size_hint_y=None,
            height=dp(40),
            halign='right'
        )
        notifications_header.bind(size=notifications_header.setter('text_size'))
        
        # Notifications ScrollView
        self.notifications_scroll = ScrollView(size_hint_y=None, height=dp(200))
        self.notifications_layout = BoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None)
        self.notifications_layout.bind(minimum_height=self.notifications_layout.setter('height'))
        
        self.notifications_scroll.add_widget(self.notifications_layout)
        
        notifications_section.add_widget(notifications_header)
        notifications_section.add_widget(self.notifications_scroll)
        
        main_layout.add_widget(notifications_section)
        
        # Refresh Button
        refresh_btn = StyledButton(
            text='🔄 تحديث البيانات',
            font_size=dp(16),
            size_hint_y=None,
            height=dp(50),
            bg_color=(0.5, 0.5, 0.5, 1)
        )
        refresh_btn.bind(on_press=self.refresh_data)
        main_layout.add_widget(refresh_btn)
        
        self.add_widget(main_layout)
        
        # Start auto-refresh
        Clock.schedule_interval(self.auto_refresh, 10)  # Every 10 seconds
    
    def create_quick_stat(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية سريعة"""
        card_layout = BoxLayout(orientation='vertical', padding=dp(10))
        
        icon_label = Label(
            text=icon,
            font_size=dp(24),
            size_hint_y=None,
            height=dp(30)
        )
        
        value_label = Label(
            text=value,
            font_size=dp(20),
            bold=True,
            color=color,
            size_hint_y=None,
            height=dp(25)
        )
        
        title_label = Label(
            text=title,
            font_size=dp(12),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(20)
        )
        
        card_layout.add_widget(icon_label)
        card_layout.add_widget(value_label)
        card_layout.add_widget(title_label)
        
        # Wrap in styled container
        container = Widget()
        with container.canvas.before:
            Color(1, 1, 1, 1)
            container.rect = RoundedRectangle(pos=container.pos, size=container.size, radius=[8])
            Color(0.9, 0.9, 0.9, 1)
            container.border = Line(rounded_rectangle=(container.x, container.y, container.width, container.height, 8), width=1)
        
        container.bind(pos=lambda instance, value: setattr(container.rect, 'pos', value))
        container.bind(size=lambda instance, value: setattr(container.rect, 'size', value))
        container.bind(pos=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 8)))
        container.bind(size=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 8)))
        
        container.add_widget(card_layout)
        return container
    
    def auto_refresh(self, dt):
        """تحديث تلقائي للبيانات"""
        self.check_connection()
        self.load_notifications()
    
    def check_connection(self):
        """فحص الاتصال بخادم الاستقبال"""
        def check():
            try:
                response = requests.get(f"{self.server_url}/api/health", timeout=3)
                if response.status_code == 200:
                    Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', '🟢 متصل'))
                    Clock.schedule_once(lambda dt: setattr(self.status_label, 'color', (0, 1, 0, 1)))
                else:
                    Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', '🔴 خطأ'))
                    Clock.schedule_once(lambda dt: setattr(self.status_label, 'color', (1, 0, 0, 1)))
            except:
                Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', '🔴 غير متصل'))
                Clock.schedule_once(lambda dt: setattr(self.status_label, 'color', (1, 0, 0, 1)))
        
        threading.Thread(target=check, daemon=True).start()
    
    def load_notifications(self):
        """تحميل التنبيهات"""
        def load():
            try:
                # Load from local file for demo
                if os.path.exists('clinineo_data.json'):
                    with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        notifications = data.get('notifications', [])[:3]  # Last 3 notifications
                        
                        Clock.schedule_once(lambda dt: self.update_notifications_ui(notifications))
            except:
                pass
        
        threading.Thread(target=load, daemon=True).start()
    
    def update_notifications_ui(self, notifications):
        """تحديث واجهة التنبيهات"""
        self.notifications_layout.clear_widgets()
        
        if notifications:
            for notification in notifications:
                if not notification.get('is_read', False):
                    card = NotificationCard(notification)
                    self.notifications_layout.add_widget(card)
        else:
            no_notifications = Label(
                text='لا توجد تنبيهات جديدة',
                font_size=dp(14),
                color=(0.6, 0.6, 0.6, 1),
                size_hint_y=None,
                height=dp(50)
            )
            self.notifications_layout.add_widget(no_notifications)
    
    def refresh_data(self, instance):
        """تحديث البيانات يدوياً"""
        self.check_connection()
        self.load_notifications()
        
        # Show refresh animation
        original_text = instance.text
        instance.text = '🔄 جاري التحديث...'
        Clock.schedule_once(lambda dt: setattr(instance, 'text', original_text), 2)
    
    def go_to_notifications(self, instance):
        """الانتقال لشاشة التنبيهات"""
        self.manager.current = 'notifications'
    
    def go_to_patients(self, instance):
        """الانتقال لشاشة المرضى"""
        self.manager.current = 'patients'
    
    def go_to_appointments(self, instance):
        """الانتقال لشاشة المواعيد"""
        self.manager.current = 'appointments'
    
    def go_to_settings(self, instance):
        """الانتقال لشاشة الإعدادات"""
        self.manager.current = 'doctor_settings'

class NotificationsScreen(Screen):
    """شاشة التنبيهات"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'notifications'
        self.build_ui()

    def build_ui(self):
        """بناء واجهة التنبيهات"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='🔔 التنبيهات', font_size=dp(20), bold=True, color=(1, 0.6, 0, 1))

        clear_btn = Button(text='مسح الكل', font_size=dp(14), size_hint_x=None, width=dp(100))
        clear_btn.bind(on_press=self.clear_all_notifications)

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(clear_btn)

        main_layout.add_widget(header)

        # Notifications ScrollView
        self.notifications_scroll = ScrollView()
        self.notifications_layout = BoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None)
        self.notifications_layout.bind(minimum_height=self.notifications_layout.setter('height'))

        self.notifications_scroll.add_widget(self.notifications_layout)
        main_layout.add_widget(self.notifications_scroll)

        self.add_widget(main_layout)

        # Load notifications when screen is created
        Clock.schedule_once(self.load_notifications, 0.1)

    def load_notifications(self, dt=None):
        """تحميل التنبيهات"""
        self.notifications_layout.clear_widgets()

        try:
            # Load from local file
            if os.path.exists('clinineo_data.json'):
                with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    notifications = data.get('notifications', [])

                    if notifications:
                        for notification in notifications:
                            notification_card = NotificationCard(notification)
                            self.notifications_layout.add_widget(notification_card)
                    else:
                        no_notifications = Label(
                            text='لا توجد تنبيهات',
                            font_size=dp(16),
                            color=(0.6, 0.6, 0.6, 1),
                            size_hint_y=None,
                            height=dp(100)
                        )
                        self.notifications_layout.add_widget(no_notifications)
            else:
                no_data = Label(
                    text='لا توجد بيانات',
                    font_size=dp(16),
                    color=(0.6, 0.6, 0.6, 1),
                    size_hint_y=None,
                    height=dp(100)
                )
                self.notifications_layout.add_widget(no_data)

        except Exception as e:
            error_label = Label(
                text=f'خطأ في تحميل التنبيهات: {str(e)}',
                font_size=dp(14),
                color=(1, 0, 0, 1),
                size_hint_y=None,
                height=dp(100)
            )
            self.notifications_layout.add_widget(error_label)

    def clear_all_notifications(self, instance):
        """مسح جميع التنبيهات"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text='هل تريد مسح جميع التنبيهات؟', font_size=dp(16)))

        buttons_layout = BoxLayout(spacing=dp(10), size_hint_y=None, height=dp(50))

        confirm_btn = Button(text='نعم', background_color=(1, 0.6, 0, 1))
        cancel_btn = Button(text='إلغاء')

        buttons_layout.add_widget(confirm_btn)
        buttons_layout.add_widget(cancel_btn)
        content.add_widget(buttons_layout)

        popup = Popup(title='تأكيد المسح', content=content, size_hint=(0.8, 0.4))

        def confirm_clear(instance):
            try:
                if os.path.exists('clinineo_data.json'):
                    with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    data['notifications'] = []

                    with open('clinineo_data.json', 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)

                popup.dismiss()
                self.load_notifications()

            except Exception as e:
                popup.dismiss()
                self.show_popup('خطأ', f'فشل في مسح التنبيهات: {str(e)}')

        confirm_btn.bind(on_press=confirm_clear)
        cancel_btn.bind(on_press=popup.dismiss)
        popup.open()

    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))

        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title=title, content=content, size_hint=(0.8, 0.5))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'doctor_home'

class PatientsScreen(Screen):
    """شاشة المرضى"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'patients'
        self.build_ui()

    def build_ui(self):
        """بناء واجهة المرضى"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='👥 المرضى', font_size=dp(20), bold=True, color=(0.2, 0.6, 1, 1))

        refresh_btn = Button(text='🔄', font_size=dp(16), size_hint_x=None, width=dp(60))
        refresh_btn.bind(on_press=self.refresh_patients)

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(refresh_btn)

        main_layout.add_widget(header)

        # Search Bar
        search_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))

        self.search_input = TextInput(
            hint_text='البحث عن مريض...',
            multiline=False,
            size_hint_y=None,
            height=dp(40)
        )
        self.search_input.bind(text=self.filter_patients)

        search_btn = Button(
            text='🔍',
            font_size=dp(16),
            size_hint_x=None,
            width=dp(60),
            size_hint_y=None,
            height=dp(40)
        )

        search_layout.add_widget(self.search_input)
        search_layout.add_widget(search_btn)

        main_layout.add_widget(search_layout)

        # Patients ScrollView
        self.patients_scroll = ScrollView()
        self.patients_layout = BoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None)
        self.patients_layout.bind(minimum_height=self.patients_layout.setter('height'))

        self.patients_scroll.add_widget(self.patients_layout)
        main_layout.add_widget(self.patients_scroll)

        self.add_widget(main_layout)

        # Store all patients for filtering
        self.all_patients = []

        # Load patients when screen is created
        Clock.schedule_once(self.load_patients, 0.1)

    def load_patients(self, dt=None):
        """تحميل قائمة المرضى"""
        self.patients_layout.clear_widgets()

        try:
            # Load from local file
            if os.path.exists('clinineo_data.json'):
                with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.all_patients = data.get('patients', [])

                    if self.all_patients:
                        for patient in self.all_patients:
                            patient_card = PatientCard(patient)
                            self.patients_layout.add_widget(patient_card)
                    else:
                        no_patients = Label(
                            text='لا توجد مرضى مسجلين',
                            font_size=dp(16),
                            color=(0.6, 0.6, 0.6, 1),
                            size_hint_y=None,
                            height=dp(100)
                        )
                        self.patients_layout.add_widget(no_patients)
            else:
                no_data = Label(
                    text='لا توجد بيانات',
                    font_size=dp(16),
                    color=(0.6, 0.6, 0.6, 1),
                    size_hint_y=None,
                    height=dp(100)
                )
                self.patients_layout.add_widget(no_data)

        except Exception as e:
            error_label = Label(
                text=f'خطأ في تحميل المرضى: {str(e)}',
                font_size=dp(14),
                color=(1, 0, 0, 1),
                size_hint_y=None,
                height=dp(100)
            )
            self.patients_layout.add_widget(error_label)

    def filter_patients(self, instance, text):
        """تصفية المرضى حسب النص المدخل"""
        self.patients_layout.clear_widgets()

        if not text.strip():
            # Show all patients if search is empty
            for patient in self.all_patients:
                patient_card = PatientCard(patient)
                self.patients_layout.add_widget(patient_card)
        else:
            # Filter patients
            filtered_patients = []
            search_text = text.lower()

            for patient in self.all_patients:
                name = f"{patient.get('first_name', '')} {patient.get('last_name', '')}".lower()
                phone = patient.get('phone', '').lower()

                if search_text in name or search_text in phone:
                    filtered_patients.append(patient)

            if filtered_patients:
                for patient in filtered_patients:
                    patient_card = PatientCard(patient)
                    self.patients_layout.add_widget(patient_card)
            else:
                no_results = Label(
                    text='لا توجد نتائج للبحث',
                    font_size=dp(16),
                    color=(0.6, 0.6, 0.6, 1),
                    size_hint_y=None,
                    height=dp(100)
                )
                self.patients_layout.add_widget(no_results)

    def refresh_patients(self, instance):
        """تحديث قائمة المرضى"""
        self.search_input.text = ''
        self.load_patients()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'doctor_home'

class DoctorSettingsScreen(Screen):
    """شاشة إعدادات الطبيب"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'doctor_settings'
        self.build_ui()

    def build_ui(self):
        """بناء واجهة الإعدادات"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='⚙️ إعدادات الطبيب', font_size=dp(20), bold=True, color=(0.3, 0.7, 0.3, 1))

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(Widget())  # Spacer

        main_layout.add_widget(header)

        # Settings Options
        settings_layout = BoxLayout(orientation='vertical', spacing=dp(15))

        # Connection Settings
        connection_card = self.create_setting_card(
            "🌐 إعدادات الاتصال",
            "تغيير عنوان خادم الاستقبال",
            self.show_connection_settings
        )
        settings_layout.add_widget(connection_card)

        # Notification Settings
        notification_card = self.create_setting_card(
            "🔔 إعدادات التنبيهات",
            "تخصيص التنبيهات والأصوات",
            self.show_notification_settings
        )
        settings_layout.add_widget(notification_card)

        # Profile Settings
        profile_card = self.create_setting_card(
            "👨‍⚕️ الملف الشخصي",
            "تحديث معلومات الطبيب",
            self.show_profile_settings
        )
        settings_layout.add_widget(profile_card)

        # About
        about_card = self.create_setting_card(
            "ℹ️ حول التطبيق",
            "معلومات عن Clinineo للطبيب",
            self.show_about
        )
        settings_layout.add_widget(about_card)

        main_layout.add_widget(settings_layout)
        self.add_widget(main_layout)

    def create_setting_card(self, title, description, callback):
        """إنشاء بطاقة إعداد"""
        card_layout = BoxLayout(orientation='horizontal', padding=dp(15), spacing=dp(15))

        # Info
        info_layout = BoxLayout(orientation='vertical')

        title_label = Label(
            text=title,
            font_size=dp(16),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(30),
            halign='right'
        )
        title_label.bind(size=title_label.setter('text_size'))

        desc_label = Label(
            text=description,
            font_size=dp(14),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        desc_label.bind(size=desc_label.setter('text_size'))

        info_layout.add_widget(title_label)
        info_layout.add_widget(desc_label)

        # Action Button
        action_btn = StyledButton(
            text='→',
            font_size=dp(18),
            size_hint_x=None,
            width=dp(60),
            bg_color=(0.3, 0.7, 0.3, 1)
        )
        action_btn.bind(on_press=callback)

        card_layout.add_widget(info_layout)
        card_layout.add_widget(action_btn)

        # Wrap in styled container
        container = Widget()
        container.size_hint_y = None
        container.height = dp(80)

        with container.canvas.before:
            Color(1, 1, 1, 1)
            container.rect = RoundedRectangle(pos=container.pos, size=container.size, radius=[10])
            Color(0.9, 0.9, 0.9, 1)
            container.border = Line(rounded_rectangle=(container.x, container.y, container.width, container.height, 10), width=1)

        container.bind(pos=lambda instance, value: setattr(container.rect, 'pos', value))
        container.bind(size=lambda instance, value: setattr(container.rect, 'size', value))
        container.bind(pos=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))
        container.bind(size=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))

        container.add_widget(card_layout)
        return container

    def show_connection_settings(self, instance):
        """عرض إعدادات الاتصال"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))

        content.add_widget(Label(text='عنوان خادم الاستقبال:', font_size=dp(16)))

        server_input = TextInput(
            text='http://192.168.1.100:8080',
            hint_text='مثال: http://192.168.1.100:8080',
            multiline=False,
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(server_input)

        buttons_layout = BoxLayout(spacing=dp(10), size_hint_y=None, height=dp(50))

        test_btn = Button(text='اختبار الاتصال')
        save_btn = Button(text='حفظ')
        cancel_btn = Button(text='إلغاء')

        buttons_layout.add_widget(test_btn)
        buttons_layout.add_widget(save_btn)
        buttons_layout.add_widget(cancel_btn)
        content.add_widget(buttons_layout)

        popup = Popup(title='إعدادات الاتصال', content=content, size_hint=(0.9, 0.6))

        def test_connection(instance):
            # Test connection logic here
            self.show_popup('اختبار الاتصال', 'تم اختبار الاتصال بنجاح!')

        def save_settings(instance):
            popup.dismiss()
            self.show_popup('نجح', 'تم حفظ إعدادات الاتصال')

        test_btn.bind(on_press=test_connection)
        save_btn.bind(on_press=save_settings)
        cancel_btn.bind(on_press=popup.dismiss)
        popup.open()

    def show_notification_settings(self, instance):
        """عرض إعدادات التنبيهات"""
        self.show_popup('قريباً', 'ستتوفر إعدادات التنبيهات قريباً')

    def show_profile_settings(self, instance):
        """عرض إعدادات الملف الشخصي"""
        self.show_popup('قريباً', 'ستتوفر إعدادات الملف الشخصي قريباً')

    def show_about(self, instance):
        """عرض معلومات التطبيق"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text='🩺 Clinineo للطبيب', font_size=dp(24), bold=True))
        content.add_widget(Label(text='تطبيق إدارة العيادات للأطباء', font_size=dp(16)))
        content.add_widget(Label(text='الإصدار: 1.0.0', font_size=dp(14)))
        content.add_widget(Label(text='© 2024 Clinineo Team', font_size=dp(12)))

        close_btn = Button(text='إغلاق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title='حول التطبيق', content=content, size_hint=(0.8, 0.6))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))

        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title=title, content=content, size_hint=(0.8, 0.5))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'doctor_home'

class ClinicoeDoctorApp(App):
    """التطبيق الرئيسي للطبيب"""
    
    def build(self):
        """بناء التطبيق"""
        self.title = 'Clinineo - الطبيب'
        
        # Screen Manager
        sm = ScreenManager()
        
        # Add Screens
        sm.add_widget(DoctorHomeScreen())
        sm.add_widget(NotificationsScreen())
        sm.add_widget(PatientsScreen())
        sm.add_widget(DoctorSettingsScreen())
        
        return sm

if __name__ == '__main__':
    ClinicoeDoctorApp().run()
