#!/usr/bin/env python3
"""
<PERSON>linineo Doctor App - Premium Android Interface
تطبيق Clinineo للطبيب - واجهة أندرويد مميزة
"""

from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.floatlayout import FloatLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.uix.progressbar import ProgressBar
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.graphics import Color, RoundedRectangle, Line
from kivy.uix.widget import Widget
from kivy.animation import Animation
import json
import os
import requests
import uuid
from datetime import datetime
import threading

class StyledButton(Button):
    """زر مخصص بتصميم مميز"""
    
    def __init__(self, bg_color=(0.3, 0.7, 0.3, 1), **kwargs):
        super().__init__(**kwargs)
        self.background_color = (0, 0, 0, 0)  # شفاف
        self.bg_color = bg_color
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        
        with self.canvas.before:
            Color(*self.bg_color)
            self.rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[15])
    
    def update_graphics(self, *args):
        self.rect.pos = self.pos
        self.rect.size = self.size

class NotificationCard(Widget):
    """بطاقة تنبيه مخصصة"""
    
    def __init__(self, notification_data, **kwargs):
        super().__init__(**kwargs)
        self.notification_data = notification_data
        self.size_hint_y = None
        self.height = dp(100)
        
        with self.canvas.before:
            Color(1, 0.95, 0.8, 1)  # خلفية صفراء فاتحة
            self.rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[10])
            Color(1, 0.7, 0, 1)  # حدود برتقالية
            self.border = Line(rounded_rectangle=(self.x, self.y, self.width, self.height, 10), width=2)
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        self.build_content()
    
    def build_content(self):
        """بناء محتوى التنبيه"""
        layout = BoxLayout(orientation='horizontal', padding=dp(15), spacing=dp(10))
        
        # Icon
        icon_label = Label(
            text='🔔',
            font_size=dp(24),
            size_hint_x=None,
            width=dp(40)
        )
        
        # Content
        content_layout = BoxLayout(orientation='vertical', spacing=dp(5))
        
        title_label = Label(
            text=self.notification_data.get('title', ''),
            font_size=dp(16),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        title_label.bind(size=title_label.setter('text_size'))
        
        message_label = Label(
            text=self.notification_data.get('message', ''),
            font_size=dp(14),
            color=(0.4, 0.4, 0.4, 1),
            size_hint_y=None,
            height=dp(40),
            halign='right',
            text_size=(None, None)
        )
        message_label.bind(size=message_label.setter('text_size'))
        
        time_label = Label(
            text=self.notification_data.get('created_at', '')[:16].replace('T', ' '),
            font_size=dp(12),
            color=(0.6, 0.6, 0.6, 1),
            size_hint_y=None,
            height=dp(20),
            halign='right'
        )
        time_label.bind(size=time_label.setter('text_size'))
        
        content_layout.add_widget(title_label)
        content_layout.add_widget(message_label)
        content_layout.add_widget(time_label)
        
        # Action Button
        action_btn = Button(
            text='✓',
            font_size=dp(18),
            size_hint_x=None,
            width=dp(50),
            background_color=(0.3, 0.7, 0.3, 1)
        )
        action_btn.bind(on_press=self.mark_as_read)
        
        layout.add_widget(icon_label)
        layout.add_widget(content_layout)
        layout.add_widget(action_btn)
        
        self.add_widget(layout)
    
    def mark_as_read(self, instance):
        """تمييز التنبيه كمقروء"""
        # Animation to fade out
        anim = Animation(opacity=0.3, duration=0.5)
        anim.start(self)
        
        # Remove from notifications list
        Clock.schedule_once(lambda dt: self.parent.remove_widget(self), 0.5)
    
    def update_graphics(self, *args):
        self.rect.pos = self.pos
        self.rect.size = self.size
        self.border.rounded_rectangle = (self.x, self.y, self.width, self.height, 10)

class PatientCard(Widget):
    """بطاقة مريض مخصصة"""
    
    def __init__(self, patient_data, **kwargs):
        super().__init__(**kwargs)
        self.patient_data = patient_data
        self.size_hint_y = None
        self.height = dp(120)
        
        with self.canvas.before:
            Color(0.95, 0.98, 1, 1)  # خلفية زرقاء فاتحة
            self.rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[10])
            Color(0.2, 0.6, 1, 1)  # حدود زرقاء
            self.border = Line(rounded_rectangle=(self.x, self.y, self.width, self.height, 10), width=2)
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        self.build_content()
    
    def build_content(self):
        """بناء محتوى بطاقة المريض"""
        layout = BoxLayout(orientation='horizontal', padding=dp(15), spacing=dp(15))
        
        # Avatar
        avatar_layout = BoxLayout(orientation='vertical', size_hint_x=None, width=dp(60))
        avatar_label = Label(
            text='👤' if self.patient_data.get('gender') == 'male' else '👩',
            font_size=dp(32),
            size_hint_y=None,
            height=dp(50)
        )
        avatar_layout.add_widget(avatar_label)
        
        # Patient Info
        info_layout = BoxLayout(orientation='vertical', spacing=dp(5))
        
        name_label = Label(
            text=f"{self.patient_data.get('first_name', '')} {self.patient_data.get('last_name', '')}",
            font_size=dp(18),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(30),
            halign='right'
        )
        name_label.bind(size=name_label.setter('text_size'))
        
        details_label = Label(
            text=f"📞 {self.patient_data.get('phone', '')} | 🎂 {self.patient_data.get('date_of_birth', '')}",
            font_size=dp(14),
            color=(0.4, 0.4, 0.4, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        details_label.bind(size=details_label.setter('text_size'))
        
        blood_label = Label(
            text=f"🩸 {self.patient_data.get('blood_type', 'غير محدد')}",
            font_size=dp(14),
            color=(0.6, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        blood_label.bind(size=blood_label.setter('text_size'))
        
        time_label = Label(
            text=f"📅 {self.patient_data.get('created_at', '')[:10]}",
            font_size=dp(12),
            color=(0.6, 0.6, 0.6, 1),
            size_hint_y=None,
            height=dp(20),
            halign='right'
        )
        time_label.bind(size=time_label.setter('text_size'))
        
        info_layout.add_widget(name_label)
        info_layout.add_widget(details_label)
        info_layout.add_widget(blood_label)
        info_layout.add_widget(time_label)
        
        # Action Buttons
        actions_layout = BoxLayout(orientation='vertical', size_hint_x=None, width=dp(80), spacing=dp(5))

        view_btn = Button(
            text='👁️',
            font_size=dp(16),
            size_hint_y=None,
            height=dp(35),
            background_color=(0.2, 0.6, 1, 1)
        )
        view_btn.bind(on_press=lambda x: self.view_patient_details(patient))

        edit_btn = Button(
            text='✏️',
            font_size=dp(16),
            size_hint_y=None,
            height=dp(35),
            background_color=(0.9, 0.6, 0.1, 1)
        )

        actions_layout.add_widget(view_btn)
        actions_layout.add_widget(edit_btn)
        
        layout.add_widget(avatar_layout)
        layout.add_widget(info_layout)
        layout.add_widget(actions_layout)
        
        self.add_widget(layout)
    
    def update_graphics(self, *args):
        self.rect.pos = self.pos
        self.rect.size = self.size
        self.border.rounded_rectangle = (self.x, self.y, self.width, self.height, 10)

class DoctorHomeScreen(Screen):
    """الشاشة الرئيسية للطبيب"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'doctor_home'
        self.server_url = "http://192.168.1.100:8080"
        self.build_ui()
    
    def build_ui(self):
        """بناء واجهة الشاشة الرئيسية"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))
        
        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(80))
        
        # Doctor Info
        doctor_layout = BoxLayout(orientation='vertical')
        doctor_label = Label(
            text='🩺 د. أحمد محمد',
            font_size=dp(24),
            bold=True,
            color=(0.3, 0.7, 0.3, 1),
            size_hint_y=None,
            height=dp(40)
        )
        specialty_label = Label(
            text='طب عام | Clinineo',
            font_size=dp(16),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(30)
        )
        
        doctor_layout.add_widget(doctor_label)
        doctor_layout.add_widget(specialty_label)
        header.add_widget(doctor_layout)
        
        # Connection Status
        self.status_label = Label(
            text='🔴 غير متصل',
            font_size=dp(14),
            color=(1, 0, 0, 1),
            size_hint_x=None,
            width=dp(120)
        )
        header.add_widget(self.status_label)
        
        main_layout.add_widget(header)
        
        # Quick Stats
        stats_layout = GridLayout(cols=3, spacing=dp(10), size_hint_y=None, height=dp(100))
        
        # Notifications Count
        notifications_card = self.create_quick_stat("🔔", "تنبيهات", "0", (1, 0.6, 0, 1))
        stats_layout.add_widget(notifications_card)
        
        # Patients Count
        patients_card = self.create_quick_stat("👥", "مرضى", "0", (0.2, 0.6, 1, 1))
        stats_layout.add_widget(patients_card)
        
        # Today's Appointments
        appointments_card = self.create_quick_stat("📅", "مواعيد", "0", (0.3, 0.7, 0.3, 1))
        stats_layout.add_widget(appointments_card)
        
        main_layout.add_widget(stats_layout)
        
        # Action Buttons
        buttons_layout = GridLayout(cols=2, spacing=dp(15), size_hint_y=None, height=dp(210))

        # Notifications Button
        notifications_btn = StyledButton(
            text='🔔\nالتنبيهات',
            font_size=dp(16),
            bg_color=(1, 0.6, 0, 1)
        )
        notifications_btn.bind(on_press=self.go_to_notifications)

        # Patients Button
        patients_btn = StyledButton(
            text='👥\nالمرضى',
            font_size=dp(16),
            bg_color=(0.2, 0.6, 1, 1)
        )
        patients_btn.bind(on_press=self.go_to_patients)

        # Prescription Button
        prescription_btn = StyledButton(
            text='📝\nالروشتات',
            font_size=dp(16),
            bg_color=(0.8, 0.2, 0.8, 1)
        )
        prescription_btn.bind(on_press=self.go_to_prescriptions)

        # Reports Button
        reports_btn = StyledButton(
            text='�\nالتقارير',
            font_size=dp(16),
            bg_color=(0.9, 0.5, 0.1, 1)
        )
        reports_btn.bind(on_press=self.go_to_reports)

        # Radiology Button
        radiology_btn = StyledButton(
            text='🩻\nالأشعة',
            font_size=dp(16),
            bg_color=(0.1, 0.8, 0.8, 1)
        )
        radiology_btn.bind(on_press=self.go_to_radiology)

        # Settings Button
        settings_btn = StyledButton(
            text='⚙️\nالإعدادات',
            font_size=dp(16),
            bg_color=(0.6, 0.6, 0.6, 1)
        )
        settings_btn.bind(on_press=self.go_to_settings)

        buttons_layout.add_widget(notifications_btn)
        buttons_layout.add_widget(patients_btn)
        buttons_layout.add_widget(prescription_btn)
        buttons_layout.add_widget(reports_btn)
        buttons_layout.add_widget(radiology_btn)
        buttons_layout.add_widget(settings_btn)
        
        main_layout.add_widget(buttons_layout)
        
        # Recent Notifications Section
        notifications_section = BoxLayout(orientation='vertical', spacing=dp(10))
        
        notifications_header = Label(
            text='📋 آخر التنبيهات',
            font_size=dp(18),
            bold=True,
            color=(0.3, 0.3, 0.3, 1),
            size_hint_y=None,
            height=dp(40),
            halign='right'
        )
        notifications_header.bind(size=notifications_header.setter('text_size'))
        
        # Notifications ScrollView
        self.notifications_scroll = ScrollView(size_hint_y=None, height=dp(200))
        self.notifications_layout = BoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None)
        self.notifications_layout.bind(minimum_height=self.notifications_layout.setter('height'))
        
        self.notifications_scroll.add_widget(self.notifications_layout)
        
        notifications_section.add_widget(notifications_header)
        notifications_section.add_widget(self.notifications_scroll)
        
        main_layout.add_widget(notifications_section)
        
        # Refresh Button
        refresh_btn = StyledButton(
            text='🔄 تحديث البيانات',
            font_size=dp(16),
            size_hint_y=None,
            height=dp(50),
            bg_color=(0.5, 0.5, 0.5, 1)
        )
        refresh_btn.bind(on_press=self.refresh_data)
        main_layout.add_widget(refresh_btn)
        
        self.add_widget(main_layout)
        
        # Start auto-refresh
        Clock.schedule_interval(self.auto_refresh, 10)  # Every 10 seconds
    
    def create_quick_stat(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية سريعة"""
        card_layout = BoxLayout(orientation='vertical', padding=dp(10))
        
        icon_label = Label(
            text=icon,
            font_size=dp(24),
            size_hint_y=None,
            height=dp(30)
        )
        
        value_label = Label(
            text=value,
            font_size=dp(20),
            bold=True,
            color=color,
            size_hint_y=None,
            height=dp(25)
        )
        
        title_label = Label(
            text=title,
            font_size=dp(12),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(20)
        )
        
        card_layout.add_widget(icon_label)
        card_layout.add_widget(value_label)
        card_layout.add_widget(title_label)
        
        # Wrap in styled container
        container = Widget()
        with container.canvas.before:
            Color(1, 1, 1, 1)
            container.rect = RoundedRectangle(pos=container.pos, size=container.size, radius=[8])
            Color(0.9, 0.9, 0.9, 1)
            container.border = Line(rounded_rectangle=(container.x, container.y, container.width, container.height, 8), width=1)
        
        container.bind(pos=lambda instance, value: setattr(container.rect, 'pos', value))
        container.bind(size=lambda instance, value: setattr(container.rect, 'size', value))
        container.bind(pos=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 8)))
        container.bind(size=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 8)))
        
        container.add_widget(card_layout)
        return container
    
    def auto_refresh(self, dt):
        """تحديث تلقائي للبيانات"""
        self.check_connection()
        self.load_notifications()
    
    def check_connection(self):
        """فحص الاتصال بخادم الاستقبال"""
        def check():
            try:
                response = requests.get(f"{self.server_url}/api/health", timeout=3)
                if response.status_code == 200:
                    Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', '🟢 متصل'))
                    Clock.schedule_once(lambda dt: setattr(self.status_label, 'color', (0, 1, 0, 1)))
                else:
                    Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', '🔴 خطأ'))
                    Clock.schedule_once(lambda dt: setattr(self.status_label, 'color', (1, 0, 0, 1)))
            except:
                Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', '🔴 غير متصل'))
                Clock.schedule_once(lambda dt: setattr(self.status_label, 'color', (1, 0, 0, 1)))
        
        threading.Thread(target=check, daemon=True).start()
    
    def load_notifications(self):
        """تحميل التنبيهات"""
        def load():
            try:
                # Load from local file for demo
                if os.path.exists('clinineo_data.json'):
                    with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        notifications = data.get('notifications', [])[:3]  # Last 3 notifications
                        
                        Clock.schedule_once(lambda dt: self.update_notifications_ui(notifications))
            except:
                pass
        
        threading.Thread(target=load, daemon=True).start()
    
    def update_notifications_ui(self, notifications):
        """تحديث واجهة التنبيهات"""
        self.notifications_layout.clear_widgets()
        
        if notifications:
            for notification in notifications:
                if not notification.get('is_read', False):
                    card = NotificationCard(notification)
                    self.notifications_layout.add_widget(card)
        else:
            no_notifications = Label(
                text='لا توجد تنبيهات جديدة',
                font_size=dp(14),
                color=(0.6, 0.6, 0.6, 1),
                size_hint_y=None,
                height=dp(50)
            )
            self.notifications_layout.add_widget(no_notifications)
    
    def refresh_data(self, instance):
        """تحديث البيانات يدوياً"""
        self.check_connection()
        self.load_notifications()
        
        # Show refresh animation
        original_text = instance.text
        instance.text = '🔄 جاري التحديث...'
        Clock.schedule_once(lambda dt: setattr(instance, 'text', original_text), 2)
    
    def go_to_notifications(self, instance):
        """الانتقال لشاشة التنبيهات"""
        self.manager.current = 'notifications'
    
    def go_to_patients(self, instance):
        """الانتقال لشاشة المرضى"""
        self.manager.current = 'patients'

    def go_to_prescriptions(self, instance):
        """الانتقال لشاشة الروشتات"""
        self.manager.current = 'prescription'

    def go_to_reports(self, instance):
        """الانتقال لشاشة التقارير"""
        self.manager.current = 'reports'

    def go_to_radiology(self, instance):
        """الانتقال لشاشة الأشعة"""
        self.manager.current = 'radiology'

    def go_to_appointments(self, instance):
        """الانتقال لشاشة المواعيد"""
        self.manager.current = 'appointments'

    def go_to_settings(self, instance):
        """الانتقال لشاشة الإعدادات"""
        self.manager.current = 'doctor_settings'

class NotificationsScreen(Screen):
    """شاشة التنبيهات"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'notifications'
        self.build_ui()

    def build_ui(self):
        """بناء واجهة التنبيهات"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='🔔 التنبيهات', font_size=dp(20), bold=True, color=(1, 0.6, 0, 1))

        clear_btn = Button(text='مسح الكل', font_size=dp(14), size_hint_x=None, width=dp(100))
        clear_btn.bind(on_press=self.clear_all_notifications)

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(clear_btn)

        main_layout.add_widget(header)

        # Notifications ScrollView
        self.notifications_scroll = ScrollView()
        self.notifications_layout = BoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None)
        self.notifications_layout.bind(minimum_height=self.notifications_layout.setter('height'))

        self.notifications_scroll.add_widget(self.notifications_layout)
        main_layout.add_widget(self.notifications_scroll)

        self.add_widget(main_layout)

        # Load notifications when screen is created
        Clock.schedule_once(self.load_notifications, 0.1)

    def load_notifications(self, dt=None):
        """تحميل التنبيهات"""
        self.notifications_layout.clear_widgets()

        try:
            # Load from local file
            if os.path.exists('clinineo_data.json'):
                with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    notifications = data.get('notifications', [])

                    if notifications:
                        for notification in notifications:
                            notification_card = NotificationCard(notification)
                            self.notifications_layout.add_widget(notification_card)
                    else:
                        no_notifications = Label(
                            text='لا توجد تنبيهات',
                            font_size=dp(16),
                            color=(0.6, 0.6, 0.6, 1),
                            size_hint_y=None,
                            height=dp(100)
                        )
                        self.notifications_layout.add_widget(no_notifications)
            else:
                no_data = Label(
                    text='لا توجد بيانات',
                    font_size=dp(16),
                    color=(0.6, 0.6, 0.6, 1),
                    size_hint_y=None,
                    height=dp(100)
                )
                self.notifications_layout.add_widget(no_data)

        except Exception as e:
            error_label = Label(
                text=f'خطأ في تحميل التنبيهات: {str(e)}',
                font_size=dp(14),
                color=(1, 0, 0, 1),
                size_hint_y=None,
                height=dp(100)
            )
            self.notifications_layout.add_widget(error_label)

    def clear_all_notifications(self, instance):
        """مسح جميع التنبيهات"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text='هل تريد مسح جميع التنبيهات؟', font_size=dp(16)))

        buttons_layout = BoxLayout(spacing=dp(10), size_hint_y=None, height=dp(50))

        confirm_btn = Button(text='نعم', background_color=(1, 0.6, 0, 1))
        cancel_btn = Button(text='إلغاء')

        buttons_layout.add_widget(confirm_btn)
        buttons_layout.add_widget(cancel_btn)
        content.add_widget(buttons_layout)

        popup = Popup(title='تأكيد المسح', content=content, size_hint=(0.8, 0.4))

        def confirm_clear(instance):
            try:
                if os.path.exists('clinineo_data.json'):
                    with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    data['notifications'] = []

                    with open('clinineo_data.json', 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)

                popup.dismiss()
                self.load_notifications()

            except Exception as e:
                popup.dismiss()
                self.show_popup('خطأ', f'فشل في مسح التنبيهات: {str(e)}')

        confirm_btn.bind(on_press=confirm_clear)
        cancel_btn.bind(on_press=popup.dismiss)
        popup.open()

    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))

        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title=title, content=content, size_hint=(0.8, 0.5))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'doctor_home'

class PrescriptionScreen(Screen):
    """شاشة الروشتات"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'prescription'
        self.current_patient = None
        self.build_ui()

    def build_ui(self):
        """بناء واجهة الروشتات"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='📝 كتابة روشتة', font_size=dp(20), bold=True, color=(0.8, 0.2, 0.8, 1))

        save_btn = Button(text='💾 حفظ', font_size=dp(16), size_hint_x=None, width=dp(100))
        save_btn.bind(on_press=self.save_prescription)

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(save_btn)

        main_layout.add_widget(header)

        # Doctor Info Card
        doctor_card = self.create_doctor_info_card()
        main_layout.add_widget(doctor_card)

        # Patient Selection
        patient_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))

        patient_label = Label(text='المريض:', font_size=dp(16), size_hint_x=None, width=dp(80))

        self.patient_spinner = Spinner(
            text='اختر مريض',
            values=[],
            size_hint_y=None,
            height=dp(40)
        )
        self.patient_spinner.bind(text=self.on_patient_selected)

        patient_layout.add_widget(patient_label)
        patient_layout.add_widget(self.patient_spinner)

        main_layout.add_widget(patient_layout)

        # Prescription Form
        form_scroll = ScrollView()
        form_layout = GridLayout(cols=1, spacing=dp(15), size_hint_y=None, padding=dp(10))
        form_layout.bind(minimum_height=form_layout.setter('height'))

        # Chief Complaint
        complaint_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(100))
        complaint_label = Label(text='الشكوى الرئيسية:', font_size=dp(16), size_hint_y=None, height=dp(30))
        self.complaint_input = TextInput(
            hint_text='اكتب الشكوى الرئيسية...',
            multiline=True,
            size_hint_y=None,
            height=dp(60)
        )
        complaint_layout.add_widget(complaint_label)
        complaint_layout.add_widget(self.complaint_input)
        form_layout.add_widget(complaint_layout)

        # Diagnosis
        diagnosis_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(100))
        diagnosis_label = Label(text='التشخيص:', font_size=dp(16), size_hint_y=None, height=dp(30))
        self.diagnosis_input = TextInput(
            hint_text='اكتب التشخيص...',
            multiline=True,
            size_hint_y=None,
            height=dp(60)
        )
        diagnosis_layout.add_widget(diagnosis_label)
        diagnosis_layout.add_widget(self.diagnosis_input)
        form_layout.add_widget(diagnosis_layout)

        # Disease Code
        code_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))
        code_label = Label(text='كود المرض (ICD-10):', font_size=dp(16), size_hint_x=None, width=dp(150))
        self.disease_code_input = TextInput(
            hint_text='مثال: J06.9',
            multiline=False,
            size_hint_y=None,
            height=dp(40)
        )
        code_layout.add_widget(code_label)
        code_layout.add_widget(self.disease_code_input)
        form_layout.add_widget(code_layout)

        # Medications
        medications_label = Label(text='الأدوية:', font_size=dp(16), size_hint_y=None, height=dp(30))
        form_layout.add_widget(medications_label)

        self.medications_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(10))
        self.medications_layout.bind(minimum_height=self.medications_layout.setter('height'))

        # Add first medication row
        self.add_medication_row()

        add_med_btn = Button(
            text='+ إضافة دواء',
            size_hint_y=None,
            height=dp(40),
            background_color=(0.3, 0.7, 0.3, 1)
        )
        add_med_btn.bind(on_press=self.add_medication_row)

        form_layout.add_widget(self.medications_layout)
        form_layout.add_widget(add_med_btn)

        # Instructions
        instructions_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(100))
        instructions_label = Label(text='تعليمات إضافية:', font_size=dp(16), size_hint_y=None, height=dp(30))
        self.instructions_input = TextInput(
            hint_text='تعليمات للمريض...',
            multiline=True,
            size_hint_y=None,
            height=dp(60)
        )
        instructions_layout.add_widget(instructions_label)
        instructions_layout.add_widget(self.instructions_input)
        form_layout.add_widget(instructions_layout)

        form_scroll.add_widget(form_layout)
        main_layout.add_widget(form_scroll)

        self.add_widget(main_layout)

        # Load patients when screen is created
        Clock.schedule_once(self.load_patients, 0.1)

    def create_doctor_info_card(self):
        """إنشاء بطاقة معلومات الطبيب"""
        card_layout = BoxLayout(orientation='vertical', padding=dp(15), size_hint_y=None, height=dp(120))

        # Doctor name and info
        doctor_name = Label(
            text='د. أحمد محمد - طب عام',
            font_size=dp(18),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(30)
        )

        clinic_info = Label(
            text='عيادة Clinineo الطبية',
            font_size=dp(14),
            color=(0.4, 0.4, 0.4, 1),
            size_hint_y=None,
            height=dp(25)
        )

        date_label = Label(
            text=f'التاريخ: {datetime.now().strftime("%Y-%m-%d")}',
            font_size=dp(14),
            color=(0.4, 0.4, 0.4, 1),
            size_hint_y=None,
            height=dp(25)
        )

        card_layout.add_widget(doctor_name)
        card_layout.add_widget(clinic_info)
        card_layout.add_widget(date_label)

        # Wrap in styled card
        container = Widget()
        container.size_hint_y = None
        container.height = dp(120)

        with container.canvas.before:
            Color(0.95, 0.98, 1, 1)
            container.rect = RoundedRectangle(pos=container.pos, size=container.size, radius=[10])
            Color(0.8, 0.2, 0.8, 1)
            container.border = Line(rounded_rectangle=(container.x, container.y, container.width, container.height, 10), width=2)

        container.bind(pos=lambda instance, value: setattr(container.rect, 'pos', value))
        container.bind(size=lambda instance, value: setattr(container.rect, 'size', value))
        container.bind(pos=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))
        container.bind(size=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))

        container.add_widget(card_layout)
        return container

    def add_medication_row(self, instance=None):
        """إضافة صف دواء جديد"""
        med_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))

        # Medication name
        med_name = TextInput(
            hint_text='اسم الدواء',
            multiline=False,
            size_hint_y=None,
            height=dp(40)
        )

        # Dosage
        dosage = TextInput(
            hint_text='الجرعة',
            multiline=False,
            size_hint_x=None,
            width=dp(100),
            size_hint_y=None,
            height=dp(40)
        )

        # Frequency
        frequency = TextInput(
            hint_text='مرات/يوم',
            multiline=False,
            size_hint_x=None,
            width=dp(80),
            size_hint_y=None,
            height=dp(40)
        )

        # Duration
        duration = TextInput(
            hint_text='المدة',
            multiline=False,
            size_hint_x=None,
            width=dp(80),
            size_hint_y=None,
            height=dp(40)
        )

        # Remove button
        remove_btn = Button(
            text='✕',
            size_hint_x=None,
            width=dp(40),
            size_hint_y=None,
            height=dp(40),
            background_color=(1, 0.3, 0.3, 1)
        )
        remove_btn.bind(on_press=lambda x: self.remove_medication_row(med_layout))

        med_layout.add_widget(med_name)
        med_layout.add_widget(dosage)
        med_layout.add_widget(frequency)
        med_layout.add_widget(duration)
        med_layout.add_widget(remove_btn)

        self.medications_layout.add_widget(med_layout)

    def remove_medication_row(self, med_layout):
        """حذف صف دواء"""
        if len(self.medications_layout.children) > 1:  # Keep at least one row
            self.medications_layout.remove_widget(med_layout)

    def load_patients(self, dt=None):
        """تحميل قائمة المرضى"""
        try:
            if os.path.exists('clinineo_data.json'):
                with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    patients = data.get('patients', [])

                    patient_names = []
                    for patient in patients:
                        name = f"{patient.get('first_name', '')} {patient.get('last_name', '')}"
                        patient_names.append(name)

                    self.patient_spinner.values = patient_names
        except:
            pass

    def on_patient_selected(self, spinner, text):
        """عند اختيار مريض"""
        if text != 'اختر مريض':
            # Find patient data
            try:
                if os.path.exists('clinineo_data.json'):
                    with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        patients = data.get('patients', [])

                        for patient in patients:
                            name = f"{patient.get('first_name', '')} {patient.get('last_name', '')}"
                            if name == text:
                                self.current_patient = patient
                                break
            except:
                pass

    def save_prescription(self, instance):
        """حفظ الروشتة"""
        if not self.current_patient:
            self.show_popup('خطأ', 'يرجى اختيار مريض أولاً')
            return

        # Collect medications
        medications = []
        for med_layout in self.medications_layout.children:
            if isinstance(med_layout, BoxLayout):
                med_inputs = [child for child in med_layout.children if isinstance(child, TextInput)]
                if len(med_inputs) >= 4:
                    med_name = med_inputs[3].text.strip()  # Reversed order due to Kivy layout
                    dosage = med_inputs[2].text.strip()
                    frequency = med_inputs[1].text.strip()
                    duration = med_inputs[0].text.strip()

                    if med_name:  # Only add if medication name is provided
                        medications.append({
                            'name': med_name,
                            'dosage': dosage,
                            'frequency': frequency,
                            'duration': duration
                        })

        # Create prescription data
        prescription = {
            'id': str(uuid.uuid4())[:8],
            'patient_id': self.current_patient.get('id'),
            'patient_name': f"{self.current_patient.get('first_name', '')} {self.current_patient.get('last_name', '')}",
            'doctor_name': 'د. أحمد محمد',
            'doctor_specialty': 'طب عام',
            'clinic_name': 'عيادة Clinineo الطبية',
            'date': datetime.now().isoformat(),
            'chief_complaint': self.complaint_input.text.strip(),
            'diagnosis': self.diagnosis_input.text.strip(),
            'disease_code': self.disease_code_input.text.strip(),
            'medications': medications,
            'instructions': self.instructions_input.text.strip(),
            'created_at': datetime.now().isoformat()
        }

        # Save to file
        try:
            prescriptions_file = 'prescriptions.json'
            prescriptions = []

            if os.path.exists(prescriptions_file):
                with open(prescriptions_file, 'r', encoding='utf-8') as f:
                    prescriptions = json.load(f)

            prescriptions.insert(0, prescription)

            with open(prescriptions_file, 'w', encoding='utf-8') as f:
                json.dump(prescriptions, f, ensure_ascii=False, indent=2)

            self.show_popup('نجح', 'تم حفظ الروشتة بنجاح!')
            self.clear_form()

        except Exception as e:
            self.show_popup('خطأ', f'فشل في حفظ الروشتة: {str(e)}')

    def clear_form(self):
        """مسح النموذج"""
        self.complaint_input.text = ''
        self.diagnosis_input.text = ''
        self.disease_code_input.text = ''
        self.instructions_input.text = ''
        self.patient_spinner.text = 'اختر مريض'
        self.current_patient = None

        # Clear medications except first row
        while len(self.medications_layout.children) > 1:
            self.medications_layout.remove_widget(self.medications_layout.children[0])

        # Clear first row
        if self.medications_layout.children:
            first_row = self.medications_layout.children[0]
            for child in first_row.children:
                if isinstance(child, TextInput):
                    child.text = ''

    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))

        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title=title, content=content, size_hint=(0.8, 0.5))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'doctor_home'

class ReportsScreen(Screen):
    """شاشة التقارير والإحصائيات"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'reports'
        self.build_ui()

    def build_ui(self):
        """بناء واجهة التقارير"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='📊 التقارير والإحصائيات', font_size=dp(20), bold=True, color=(0.9, 0.5, 0.1, 1))

        refresh_btn = Button(text='🔄', font_size=dp(16), size_hint_x=None, width=dp(60))
        refresh_btn.bind(on_press=self.refresh_reports)

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(refresh_btn)

        main_layout.add_widget(header)

        # Reports ScrollView
        reports_scroll = ScrollView()
        reports_layout = BoxLayout(orientation='vertical', spacing=dp(15), size_hint_y=None)
        reports_layout.bind(minimum_height=reports_layout.setter('height'))

        # Statistics Cards
        stats_section = self.create_statistics_section()
        reports_layout.add_widget(stats_section)

        # Disease Statistics
        disease_section = self.create_disease_statistics()
        reports_layout.add_widget(disease_section)

        # Prescription Statistics
        prescription_section = self.create_prescription_statistics()
        reports_layout.add_widget(prescription_section)

        # Monthly Report
        monthly_section = self.create_monthly_report()
        reports_layout.add_widget(monthly_section)

        reports_scroll.add_widget(reports_layout)
        main_layout.add_widget(reports_scroll)

        self.add_widget(main_layout)

        # Load data when screen is created
        Clock.schedule_once(self.load_reports_data, 0.1)

    def create_statistics_section(self):
        """إنشاء قسم الإحصائيات العامة"""
        section_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(200), spacing=dp(10))

        section_title = Label(
            text='📈 الإحصائيات العامة',
            font_size=dp(18),
            bold=True,
            size_hint_y=None,
            height=dp(30),
            color=(0.2, 0.2, 0.2, 1)
        )
        section_layout.add_widget(section_title)

        # Stats grid
        stats_grid = GridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(150))

        # Total Patients
        self.total_patients_card = self.create_stat_card("👥", "إجمالي المرضى", "0", (0.2, 0.6, 1, 1))
        stats_grid.add_widget(self.total_patients_card)

        # Total Prescriptions
        self.total_prescriptions_card = self.create_stat_card("📝", "إجمالي الروشتات", "0", (0.8, 0.2, 0.8, 1))
        stats_grid.add_widget(self.total_prescriptions_card)

        # This Month Patients
        self.month_patients_card = self.create_stat_card("📅", "مرضى هذا الشهر", "0", (0.3, 0.7, 0.3, 1))
        stats_grid.add_widget(self.month_patients_card)

        # Today Prescriptions
        self.today_prescriptions_card = self.create_stat_card("📋", "روشتات اليوم", "0", (0.9, 0.5, 0.1, 1))
        stats_grid.add_widget(self.today_prescriptions_card)

        section_layout.add_widget(stats_grid)

        return section_layout

    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card_layout = BoxLayout(orientation='vertical', padding=dp(10))

        icon_label = Label(
            text=icon,
            font_size=dp(24),
            size_hint_y=None,
            height=dp(30)
        )

        value_label = Label(
            text=value,
            font_size=dp(20),
            bold=True,
            color=color,
            size_hint_y=None,
            height=dp(25)
        )

        title_label = Label(
            text=title,
            font_size=dp(12),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(20)
        )

        card_layout.add_widget(icon_label)
        card_layout.add_widget(value_label)
        card_layout.add_widget(title_label)

        # Wrap in styled container
        container = Widget()
        container.size_hint_y = None
        container.height = dp(75)

        with container.canvas.before:
            Color(1, 1, 1, 1)
            container.rect = RoundedRectangle(pos=container.pos, size=container.size, radius=[8])
            Color(0.9, 0.9, 0.9, 1)
            container.border = Line(rounded_rectangle=(container.x, container.y, container.width, container.height, 8), width=1)

        container.bind(pos=lambda instance, value: setattr(container.rect, 'pos', value))
        container.bind(size=lambda instance, value: setattr(container.rect, 'size', value))
        container.bind(pos=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 8)))
        container.bind(size=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 8)))

        container.add_widget(card_layout)
        return container

    def create_disease_statistics(self):
        """إنشاء إحصائيات الأمراض"""
        section_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(10))

        section_title = Label(
            text='🦠 إحصائيات الأمراض الأكثر شيوعاً',
            font_size=dp(16),
            bold=True,
            size_hint_y=None,
            height=dp(30),
            color=(0.2, 0.2, 0.2, 1)
        )
        section_layout.add_widget(section_title)

        # Disease list
        self.disease_list_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(5))
        self.disease_list_layout.bind(minimum_height=self.disease_list_layout.setter('height'))

        section_layout.add_widget(self.disease_list_layout)

        return section_layout

    def create_prescription_statistics(self):
        """إنشاء إحصائيات الروشتات"""
        section_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(10))

        section_title = Label(
            text='💊 إحصائيات الأدوية الأكثر وصفاً',
            font_size=dp(16),
            bold=True,
            size_hint_y=None,
            height=dp(30),
            color=(0.2, 0.2, 0.2, 1)
        )
        section_layout.add_widget(section_title)

        # Medications list
        self.medications_list_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(5))
        self.medications_list_layout.bind(minimum_height=self.medications_list_layout.setter('height'))

        section_layout.add_widget(self.medications_list_layout)

        return section_layout

    def create_monthly_report(self):
        """إنشاء التقرير الشهري"""
        section_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(10))

        section_title = Label(
            text='📅 التقرير الشهري',
            font_size=dp(16),
            bold=True,
            size_hint_y=None,
            height=dp(30),
            color=(0.2, 0.2, 0.2, 1)
        )
        section_layout.add_widget(section_title)

        # Monthly data
        self.monthly_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(5))
        self.monthly_layout.bind(minimum_height=self.monthly_layout.setter('height'))

        section_layout.add_widget(self.monthly_layout)

        # Export button
        export_btn = Button(
            text='📄 تصدير التقرير',
            size_hint_y=None,
            height=dp(50),
            background_color=(0.2, 0.6, 1, 1)
        )
        export_btn.bind(on_press=self.export_report)
        section_layout.add_widget(export_btn)

        return section_layout

    def load_reports_data(self, dt=None):
        """تحميل بيانات التقارير"""
        try:
            # Load patients data
            patients = []
            if os.path.exists('clinineo_data.json'):
                with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    patients = data.get('patients', [])

            # Load prescriptions data
            prescriptions = []
            if os.path.exists('prescriptions.json'):
                with open('prescriptions.json', 'r', encoding='utf-8') as f:
                    prescriptions = json.load(f)

            # Update statistics
            self.update_statistics(patients, prescriptions)
            self.update_disease_statistics(prescriptions)
            self.update_medication_statistics(prescriptions)
            self.update_monthly_report(patients, prescriptions)

        except Exception as e:
            print(f"خطأ في تحميل بيانات التقارير: {e}")

    def update_statistics(self, patients, prescriptions):
        """تحديث الإحصائيات العامة"""
        # Total patients
        total_patients = len(patients)

        # Total prescriptions
        total_prescriptions = len(prescriptions)

        # This month patients
        current_month = datetime.now().strftime('%Y-%m')
        month_patients = len([p for p in patients if p.get('created_at', '').startswith(current_month)])

        # Today prescriptions
        today = datetime.now().strftime('%Y-%m-%d')
        today_prescriptions = len([p for p in prescriptions if p.get('date', '').startswith(today)])

        # Update UI
        self.update_stat_card_value(self.total_patients_card, str(total_patients))
        self.update_stat_card_value(self.total_prescriptions_card, str(total_prescriptions))
        self.update_stat_card_value(self.month_patients_card, str(month_patients))
        self.update_stat_card_value(self.today_prescriptions_card, str(today_prescriptions))

    def update_stat_card_value(self, card, new_value):
        """تحديث قيمة بطاقة إحصائية"""
        try:
            # Find the value label in the card
            for child in card.children:
                if hasattr(child, 'children'):
                    for subchild in child.children:
                        if hasattr(subchild, 'children'):
                            labels = [c for c in subchild.children if isinstance(c, Label)]
                            if len(labels) >= 2:  # icon, value, title
                                value_label = labels[1]  # Middle label is the value
                                value_label.text = new_value
                                break
        except:
            pass

    def update_disease_statistics(self, prescriptions):
        """تحديث إحصائيات الأمراض"""
        self.disease_list_layout.clear_widgets()

        # Count diseases
        disease_count = {}
        for prescription in prescriptions:
            diagnosis = prescription.get('diagnosis', '').strip()
            disease_code = prescription.get('disease_code', '').strip()

            if diagnosis:
                key = f"{diagnosis} ({disease_code})" if disease_code else diagnosis
                disease_count[key] = disease_count.get(key, 0) + 1

        # Sort by frequency
        sorted_diseases = sorted(disease_count.items(), key=lambda x: x[1], reverse=True)[:10]

        if sorted_diseases:
            for disease, count in sorted_diseases:
                disease_item = self.create_disease_item(disease, count)
                self.disease_list_layout.add_widget(disease_item)
        else:
            no_data = Label(
                text='لا توجد بيانات أمراض',
                font_size=dp(14),
                color=(0.6, 0.6, 0.6, 1),
                size_hint_y=None,
                height=dp(30)
            )
            self.disease_list_layout.add_widget(no_data)

    def create_disease_item(self, disease, count):
        """إنشاء عنصر مرض"""
        item_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(40), spacing=dp(10))

        disease_label = Label(
            text=disease,
            font_size=dp(14),
            color=(0.2, 0.2, 0.2, 1),
            halign='right'
        )
        disease_label.bind(size=disease_label.setter('text_size'))

        count_label = Label(
            text=str(count),
            font_size=dp(14),
            bold=True,
            color=(0.8, 0.2, 0.2, 1),
            size_hint_x=None,
            width=dp(50)
        )

        item_layout.add_widget(disease_label)
        item_layout.add_widget(count_label)

        return item_layout

    def update_medication_statistics(self, prescriptions):
        """تحديث إحصائيات الأدوية"""
        self.medications_list_layout.clear_widgets()

        # Count medications
        medication_count = {}
        for prescription in prescriptions:
            medications = prescription.get('medications', [])
            for med in medications:
                med_name = med.get('name', '').strip()
                if med_name:
                    medication_count[med_name] = medication_count.get(med_name, 0) + 1

        # Sort by frequency
        sorted_medications = sorted(medication_count.items(), key=lambda x: x[1], reverse=True)[:10]

        if sorted_medications:
            for medication, count in sorted_medications:
                med_item = self.create_medication_item(medication, count)
                self.medications_list_layout.add_widget(med_item)
        else:
            no_data = Label(
                text='لا توجد بيانات أدوية',
                font_size=dp(14),
                color=(0.6, 0.6, 0.6, 1),
                size_hint_y=None,
                height=dp(30)
            )
            self.medications_list_layout.add_widget(no_data)

    def create_medication_item(self, medication, count):
        """إنشاء عنصر دواء"""
        item_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(40), spacing=dp(10))

        med_label = Label(
            text=medication,
            font_size=dp(14),
            color=(0.2, 0.2, 0.2, 1),
            halign='right'
        )
        med_label.bind(size=med_label.setter('text_size'))

        count_label = Label(
            text=str(count),
            font_size=dp(14),
            bold=True,
            color=(0.8, 0.2, 0.8, 1),
            size_hint_x=None,
            width=dp(50)
        )

        item_layout.add_widget(med_label)
        item_layout.add_widget(count_label)

        return item_layout

    def update_monthly_report(self, patients, prescriptions):
        """تحديث التقرير الشهري"""
        self.monthly_layout.clear_widgets()

        current_month = datetime.now().strftime('%Y-%m')
        month_name = datetime.now().strftime('%B %Y')

        # Monthly statistics
        month_patients = [p for p in patients if p.get('created_at', '').startswith(current_month)]
        month_prescriptions = [p for p in prescriptions if p.get('date', '').startswith(current_month)]

        # Create monthly summary
        summary_items = [
            f"📅 شهر: {month_name}",
            f"👥 مرضى جدد: {len(month_patients)}",
            f"📝 روشتات مكتوبة: {len(month_prescriptions)}",
            f"📊 متوسط الروشتات/يوم: {len(month_prescriptions) // max(1, datetime.now().day)}",
        ]

        for item in summary_items:
            item_label = Label(
                text=item,
                font_size=dp(14),
                color=(0.2, 0.2, 0.2, 1),
                size_hint_y=None,
                height=dp(30),
                halign='right'
            )
            item_label.bind(size=item_label.setter('text_size'))
            self.monthly_layout.add_widget(item_label)

    def refresh_reports(self, instance):
        """تحديث التقارير"""
        self.load_reports_data()
        self.show_popup('تم التحديث', 'تم تحديث التقارير بنجاح')

    def export_report(self, instance):
        """تصدير التقرير"""
        try:
            # Create report data
            report_data = {
                'date': datetime.now().isoformat(),
                'month': datetime.now().strftime('%Y-%m'),
                'generated_by': 'د. أحمد محمد',
                'clinic': 'عيادة Clinineo الطبية'
            }

            # Save report
            report_filename = f"monthly_report_{datetime.now().strftime('%Y_%m')}.json"
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.show_popup('تم التصدير', f'تم حفظ التقرير: {report_filename}')

        except Exception as e:
            self.show_popup('خطأ', f'فشل في تصدير التقرير: {str(e)}')

    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))

        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title=title, content=content, size_hint=(0.8, 0.5))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'doctor_home'

# Import RadiologyScreen
from radiology_screen import RadiologyScreen

class PatientDetailsScreen(Screen):
    """شاشة تفاصيل المريض"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'patient_details'
        self.patient_data = None
        self.build_ui()

    def build_ui(self):
        """بناء واجهة تفاصيل المريض"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        self.title_label = Label(text='👤 تفاصيل المريض', font_size=dp(20), bold=True, color=(0.2, 0.6, 1, 1))

        header.add_widget(back_btn)
        header.add_widget(self.title_label)
        header.add_widget(Widget())  # Spacer

        main_layout.add_widget(header)

        # Patient info will be added dynamically
        self.content_layout = BoxLayout(orientation='vertical', spacing=dp(15))
        main_layout.add_widget(self.content_layout)

        self.add_widget(main_layout)

    def set_patient_data(self, patient_data):
        """تعيين بيانات المريض"""
        self.patient_data = patient_data
        self.update_ui()

    def update_ui(self):
        """تحديث واجهة المستخدم"""
        if not self.patient_data:
            return

        self.content_layout.clear_widgets()

        # Patient name in title
        patient_name = f"{self.patient_data.get('first_name', '')} {self.patient_data.get('last_name', '')}"
        self.title_label.text = f'👤 {patient_name}'

        # Patient details card
        details_card = self.create_patient_details_card()
        self.content_layout.add_widget(details_card)

        # Action buttons
        actions_card = self.create_actions_card()
        self.content_layout.add_widget(actions_card)

    def create_patient_details_card(self):
        """إنشاء بطاقة تفاصيل المريض"""
        card_layout = BoxLayout(orientation='vertical', padding=dp(15), spacing=dp(10), size_hint_y=None)

        # Patient info items
        info_items = [
            ('👤', 'الاسم', f"{self.patient_data.get('first_name', '')} {self.patient_data.get('last_name', '')}"),
            ('📞', 'الهاتف', self.patient_data.get('phone', '')),
            ('🎂', 'تاريخ الميلاد', self.patient_data.get('date_of_birth', '')),
            ('⚧', 'الجنس', 'ذكر' if self.patient_data.get('gender') == 'male' else 'أنثى'),
            ('📧', 'البريد الإلكتروني', self.patient_data.get('email', 'غير محدد')),
            ('🏠', 'العنوان', self.patient_data.get('address', 'غير محدد')),
            ('🩸', 'فصيلة الدم', self.patient_data.get('blood_type', 'غير محدد')),
            ('⚠️', 'الحساسية', self.patient_data.get('allergies', 'لا توجد')),
            ('📅', 'تاريخ التسجيل', self.patient_data.get('created_at', '')[:10])
        ]

        for icon, label, value in info_items:
            item_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(40), spacing=dp(10))

            icon_label = Label(
                text=icon,
                font_size=dp(20),
                size_hint_x=None,
                width=dp(40)
            )

            label_text = Label(
                text=f"{label}:",
                font_size=dp(14),
                bold=True,
                color=(0.3, 0.3, 0.3, 1),
                size_hint_x=None,
                width=dp(120),
                halign='right'
            )
            label_text.bind(size=label_text.setter('text_size'))

            value_label = Label(
                text=str(value),
                font_size=dp(14),
                color=(0.2, 0.2, 0.2, 1),
                halign='right'
            )
            value_label.bind(size=value_label.setter('text_size'))

            item_layout.add_widget(icon_label)
            item_layout.add_widget(label_text)
            item_layout.add_widget(value_label)

            card_layout.add_widget(item_layout)

        # Calculate height
        card_layout.height = len(info_items) * dp(40) + dp(30)  # items + padding

        # Wrap in styled container
        container = Widget()
        container.size_hint_y = None
        container.height = card_layout.height + dp(30)

        with container.canvas.before:
            Color(0.95, 0.98, 1, 1)
            container.rect = RoundedRectangle(pos=container.pos, size=container.size, radius=[10])
            Color(0.2, 0.6, 1, 1)
            container.border = Line(rounded_rectangle=(container.x, container.y, container.width, container.height, 10), width=2)

        container.bind(pos=lambda instance, value: setattr(container.rect, 'pos', value))
        container.bind(size=lambda instance, value: setattr(container.rect, 'size', value))
        container.bind(pos=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))
        container.bind(size=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))

        container.add_widget(card_layout)
        return container

    def create_actions_card(self):
        """إنشاء بطاقة الإجراءات"""
        card_layout = BoxLayout(orientation='vertical', padding=dp(15), spacing=dp(10), size_hint_y=None, height=dp(200))

        title_label = Label(
            text='🔧 الإجراءات المتاحة',
            font_size=dp(16),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(30)
        )
        card_layout.add_widget(title_label)

        # Action buttons
        buttons_layout = GridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(140))

        # Prescription button
        prescription_btn = StyledButton(
            text='📝\nكتابة روشتة',
            font_size=dp(14),
            bg_color=(0.8, 0.2, 0.8, 1)
        )
        prescription_btn.bind(on_press=self.write_prescription)

        # Radiology button
        radiology_btn = StyledButton(
            text='🩻\nطلب أشعة',
            font_size=dp(14),
            bg_color=(0.1, 0.8, 0.8, 1)
        )
        radiology_btn.bind(on_press=self.request_radiology)

        # History button
        history_btn = StyledButton(
            text='📚\nالسجل الطبي',
            font_size=dp(14),
            bg_color=(0.9, 0.5, 0.1, 1)
        )
        history_btn.bind(on_press=self.view_medical_history)

        # Edit button
        edit_btn = StyledButton(
            text='✏️\nتعديل البيانات',
            font_size=dp(14),
            bg_color=(0.3, 0.7, 0.3, 1)
        )
        edit_btn.bind(on_press=self.edit_patient)

        buttons_layout.add_widget(prescription_btn)
        buttons_layout.add_widget(radiology_btn)
        buttons_layout.add_widget(history_btn)
        buttons_layout.add_widget(edit_btn)

        card_layout.add_widget(buttons_layout)

        # Wrap in styled container
        container = Widget()
        container.size_hint_y = None
        container.height = dp(200)

        with container.canvas.before:
            Color(1, 1, 1, 1)
            container.rect = RoundedRectangle(pos=container.pos, size=container.size, radius=[10])
            Color(0.9, 0.9, 0.9, 1)
            container.border = Line(rounded_rectangle=(container.x, container.y, container.width, container.height, 10), width=1)

        container.bind(pos=lambda instance, value: setattr(container.rect, 'pos', value))
        container.bind(size=lambda instance, value: setattr(container.rect, 'size', value))
        container.bind(pos=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))
        container.bind(size=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))

        container.add_widget(card_layout)
        return container

    def write_prescription(self, instance):
        """كتابة روشتة للمريض"""
        # Go to prescription screen and set patient
        prescription_screen = self.manager.get_screen('prescription')
        prescription_screen.current_patient = self.patient_data

        # Update patient spinner
        patient_name = f"{self.patient_data.get('first_name', '')} {self.patient_data.get('last_name', '')}"
        prescription_screen.patient_spinner.text = patient_name

        self.manager.current = 'prescription'

    def request_radiology(self, instance):
        """طلب أشعة للمريض"""
        # Go to radiology screen and set patient
        radiology_screen = self.manager.get_screen('radiology')
        radiology_screen.current_patient = self.patient_data

        # Update patient spinner
        patient_name = f"{self.patient_data.get('first_name', '')} {self.patient_data.get('last_name', '')}"
        radiology_screen.patient_spinner.text = patient_name

        self.manager.current = 'radiology'

    def view_medical_history(self, instance):
        """عرض السجل الطبي"""
        self.show_popup('قريباً', 'ستتوفر ميزة السجل الطبي قريباً')

    def edit_patient(self, instance):
        """تعديل بيانات المريض"""
        self.show_popup('قريباً', 'ستتوفر ميزة تعديل البيانات قريباً')

    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))

        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title=title, content=content, size_hint=(0.8, 0.5))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def go_back(self, instance):
        """العودة للشاشة السابقة"""
        self.manager.current = 'patients'

class PatientsScreen(Screen):
    """شاشة المرضى"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'patients'
        self.build_ui()

    def build_ui(self):
        """بناء واجهة المرضى"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='👥 المرضى', font_size=dp(20), bold=True, color=(0.2, 0.6, 1, 1))

        refresh_btn = Button(text='🔄', font_size=dp(16), size_hint_x=None, width=dp(60))
        refresh_btn.bind(on_press=self.refresh_patients)

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(refresh_btn)

        main_layout.add_widget(header)

        # Search Bar
        search_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))

        self.search_input = TextInput(
            hint_text='البحث عن مريض...',
            multiline=False,
            size_hint_y=None,
            height=dp(40)
        )
        self.search_input.bind(text=self.filter_patients)

        search_btn = Button(
            text='🔍',
            font_size=dp(16),
            size_hint_x=None,
            width=dp(60),
            size_hint_y=None,
            height=dp(40)
        )

        search_layout.add_widget(self.search_input)
        search_layout.add_widget(search_btn)

        main_layout.add_widget(search_layout)

        # Patients ScrollView
        self.patients_scroll = ScrollView()
        self.patients_layout = BoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None)
        self.patients_layout.bind(minimum_height=self.patients_layout.setter('height'))

        self.patients_scroll.add_widget(self.patients_layout)
        main_layout.add_widget(self.patients_scroll)

        self.add_widget(main_layout)

        # Store all patients for filtering
        self.all_patients = []

        # Load patients when screen is created
        Clock.schedule_once(self.load_patients, 0.1)

    def load_patients(self, dt=None):
        """تحميل قائمة المرضى"""
        self.patients_layout.clear_widgets()

        try:
            # Load from local file
            if os.path.exists('clinineo_data.json'):
                with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.all_patients = data.get('patients', [])

                    if self.all_patients:
                        for patient in self.all_patients:
                            patient_card = PatientCard(patient)
                            self.patients_layout.add_widget(patient_card)
                    else:
                        no_patients = Label(
                            text='لا توجد مرضى مسجلين',
                            font_size=dp(16),
                            color=(0.6, 0.6, 0.6, 1),
                            size_hint_y=None,
                            height=dp(100)
                        )
                        self.patients_layout.add_widget(no_patients)
            else:
                no_data = Label(
                    text='لا توجد بيانات',
                    font_size=dp(16),
                    color=(0.6, 0.6, 0.6, 1),
                    size_hint_y=None,
                    height=dp(100)
                )
                self.patients_layout.add_widget(no_data)

        except Exception as e:
            error_label = Label(
                text=f'خطأ في تحميل المرضى: {str(e)}',
                font_size=dp(14),
                color=(1, 0, 0, 1),
                size_hint_y=None,
                height=dp(100)
            )
            self.patients_layout.add_widget(error_label)

    def filter_patients(self, instance, text):
        """تصفية المرضى حسب النص المدخل"""
        self.patients_layout.clear_widgets()

        if not text.strip():
            # Show all patients if search is empty
            for patient in self.all_patients:
                patient_card = PatientCard(patient)
                self.patients_layout.add_widget(patient_card)
        else:
            # Filter patients
            filtered_patients = []
            search_text = text.lower()

            for patient in self.all_patients:
                name = f"{patient.get('first_name', '')} {patient.get('last_name', '')}".lower()
                phone = patient.get('phone', '').lower()

                if search_text in name or search_text in phone:
                    filtered_patients.append(patient)

            if filtered_patients:
                for patient in filtered_patients:
                    patient_card = PatientCard(patient)
                    self.patients_layout.add_widget(patient_card)
            else:
                no_results = Label(
                    text='لا توجد نتائج للبحث',
                    font_size=dp(16),
                    color=(0.6, 0.6, 0.6, 1),
                    size_hint_y=None,
                    height=dp(100)
                )
                self.patients_layout.add_widget(no_results)

    def refresh_patients(self, instance):
        """تحديث قائمة المرضى"""
        self.search_input.text = ''
        self.load_patients()

    def view_patient_details(self, patient_data):
        """عرض تفاصيل المريض"""
        details_screen = self.manager.get_screen('patient_details')
        details_screen.set_patient_data(patient_data)
        self.manager.current = 'patient_details'

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'doctor_home'

class DoctorSettingsScreen(Screen):
    """شاشة إعدادات الطبيب"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'doctor_settings'
        self.build_ui()

    def build_ui(self):
        """بناء واجهة الإعدادات"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='⚙️ إعدادات الطبيب', font_size=dp(20), bold=True, color=(0.3, 0.7, 0.3, 1))

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(Widget())  # Spacer

        main_layout.add_widget(header)

        # Settings Options
        settings_layout = BoxLayout(orientation='vertical', spacing=dp(15))

        # Connection Settings
        connection_card = self.create_setting_card(
            "🌐 إعدادات الاتصال",
            "تغيير عنوان خادم الاستقبال",
            self.show_connection_settings
        )
        settings_layout.add_widget(connection_card)

        # Notification Settings
        notification_card = self.create_setting_card(
            "🔔 إعدادات التنبيهات",
            "تخصيص التنبيهات والأصوات",
            self.show_notification_settings
        )
        settings_layout.add_widget(notification_card)

        # Profile Settings
        profile_card = self.create_setting_card(
            "👨‍⚕️ الملف الشخصي",
            "تحديث معلومات الطبيب",
            self.show_profile_settings
        )
        settings_layout.add_widget(profile_card)

        # About
        about_card = self.create_setting_card(
            "ℹ️ حول التطبيق",
            "معلومات عن Clinineo للطبيب",
            self.show_about
        )
        settings_layout.add_widget(about_card)

        main_layout.add_widget(settings_layout)
        self.add_widget(main_layout)

    def create_setting_card(self, title, description, callback):
        """إنشاء بطاقة إعداد"""
        card_layout = BoxLayout(orientation='horizontal', padding=dp(15), spacing=dp(15))

        # Info
        info_layout = BoxLayout(orientation='vertical')

        title_label = Label(
            text=title,
            font_size=dp(16),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(30),
            halign='right'
        )
        title_label.bind(size=title_label.setter('text_size'))

        desc_label = Label(
            text=description,
            font_size=dp(14),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        desc_label.bind(size=desc_label.setter('text_size'))

        info_layout.add_widget(title_label)
        info_layout.add_widget(desc_label)

        # Action Button
        action_btn = StyledButton(
            text='→',
            font_size=dp(18),
            size_hint_x=None,
            width=dp(60),
            bg_color=(0.3, 0.7, 0.3, 1)
        )
        action_btn.bind(on_press=callback)

        card_layout.add_widget(info_layout)
        card_layout.add_widget(action_btn)

        # Wrap in styled container
        container = Widget()
        container.size_hint_y = None
        container.height = dp(80)

        with container.canvas.before:
            Color(1, 1, 1, 1)
            container.rect = RoundedRectangle(pos=container.pos, size=container.size, radius=[10])
            Color(0.9, 0.9, 0.9, 1)
            container.border = Line(rounded_rectangle=(container.x, container.y, container.width, container.height, 10), width=1)

        container.bind(pos=lambda instance, value: setattr(container.rect, 'pos', value))
        container.bind(size=lambda instance, value: setattr(container.rect, 'size', value))
        container.bind(pos=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))
        container.bind(size=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 10)))

        container.add_widget(card_layout)
        return container

    def show_connection_settings(self, instance):
        """عرض إعدادات الاتصال"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))

        content.add_widget(Label(text='عنوان خادم الاستقبال:', font_size=dp(16)))

        server_input = TextInput(
            text='http://192.168.1.100:8080',
            hint_text='مثال: http://192.168.1.100:8080',
            multiline=False,
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(server_input)

        buttons_layout = BoxLayout(spacing=dp(10), size_hint_y=None, height=dp(50))

        test_btn = Button(text='اختبار الاتصال')
        save_btn = Button(text='حفظ')
        cancel_btn = Button(text='إلغاء')

        buttons_layout.add_widget(test_btn)
        buttons_layout.add_widget(save_btn)
        buttons_layout.add_widget(cancel_btn)
        content.add_widget(buttons_layout)

        popup = Popup(title='إعدادات الاتصال', content=content, size_hint=(0.9, 0.6))

        def test_connection(instance):
            # Test connection logic here
            self.show_popup('اختبار الاتصال', 'تم اختبار الاتصال بنجاح!')

        def save_settings(instance):
            popup.dismiss()
            self.show_popup('نجح', 'تم حفظ إعدادات الاتصال')

        test_btn.bind(on_press=test_connection)
        save_btn.bind(on_press=save_settings)
        cancel_btn.bind(on_press=popup.dismiss)
        popup.open()

    def show_notification_settings(self, instance):
        """عرض إعدادات التنبيهات"""
        self.show_popup('قريباً', 'ستتوفر إعدادات التنبيهات قريباً')

    def show_profile_settings(self, instance):
        """عرض إعدادات الملف الشخصي"""
        self.show_popup('قريباً', 'ستتوفر إعدادات الملف الشخصي قريباً')

    def show_about(self, instance):
        """عرض معلومات التطبيق"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text='🩺 Clinineo للطبيب', font_size=dp(24), bold=True))
        content.add_widget(Label(text='تطبيق إدارة العيادات للأطباء', font_size=dp(16)))
        content.add_widget(Label(text='الإصدار: 1.0.0', font_size=dp(14)))
        content.add_widget(Label(text='© 2024 Clinineo Team', font_size=dp(12)))

        close_btn = Button(text='إغلاق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title='حول التطبيق', content=content, size_hint=(0.8, 0.6))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))

        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title=title, content=content, size_hint=(0.8, 0.5))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'doctor_home'

class ClinicoeDoctorApp(App):
    """التطبيق الرئيسي للطبيب"""
    
    def build(self):
        """بناء التطبيق"""
        self.title = 'Clinineo - الطبيب'
        
        # Screen Manager
        sm = ScreenManager()
        
        # Add Screens
        sm.add_widget(DoctorHomeScreen())
        sm.add_widget(NotificationsScreen())
        sm.add_widget(PatientsScreen())
        sm.add_widget(PrescriptionScreen())
        sm.add_widget(ReportsScreen())
        sm.add_widget(RadiologyScreen())
        sm.add_widget(PatientDetailsScreen())
        sm.add_widget(DoctorSettingsScreen())
        
        return sm

if __name__ == '__main__':
    ClinicoeDoctorApp().run()
