#!/usr/bin/env python3
"""
Direct launcher for Doctor App
مشغل مباشر لتطبيق الطبيب
"""

import sys
import os

print("🚀 تشغيل تطبيق الطبيب...")

# التحقق من وجود الملف
if not os.path.exists('doctor_app.py'):
    print("❌ ملف doctor_app.py غير موجود")
    input("اضغط Enter للخروج...")
    sys.exit(1)

try:
    # تشغيل تطبيق الطبيب
    exec(open('doctor_app.py').read())
except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    input("اضغط Enter للخروج...")
