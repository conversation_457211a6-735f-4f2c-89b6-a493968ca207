"""
Appointment management routes
مسارات إدارة المواعيد
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from datetime import date, time

from ...core.database import get_db, Appointment, Patient, User
from ...core.security import get_current_active_user, require_registrar_or_admin, require_any_role
from ...schemas.appointment import AppointmentCreate, AppointmentUpdate, AppointmentResponse

router = APIRouter()

@router.post("/", response_model=AppointmentResponse)
async def create_appointment(
    appointment: AppointmentCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_registrar_or_admin)
):
    """Create a new appointment"""
    # Check if patient exists
    patient = db.query(Patient).filter(Patient.id == appointment.patient_id).first()
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )
    
    # Check if doctor exists
    doctor = db.query(User).filter(
        User.id == appointment.doctor_id,
        User.role == "doctor",
        User.is_active == True
    ).first()
    if not doctor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Doctor not found"
        )
    
    # Check for conflicting appointments
    existing_appointment = db.query(Appointment).filter(
        Appointment.doctor_id == appointment.doctor_id,
        Appointment.appointment_date == appointment.appointment_date,
        Appointment.appointment_time == appointment.appointment_time,
        Appointment.status != "cancelled"
    ).first()
    
    if existing_appointment:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Doctor already has an appointment at this time"
        )
    
    # Create appointment
    db_appointment = Appointment(
        **appointment.dict(),
        created_by=current_user.id
    )
    db.add(db_appointment)
    db.commit()
    db.refresh(db_appointment)
    
    # Send real-time notification
    if hasattr(request.app.state, 'websocket_manager'):
        websocket_manager = request.app.state.websocket_manager
        appointment_data = {
            "id": db_appointment.id,
            "date": str(db_appointment.appointment_date),
            "time": str(db_appointment.appointment_time),
            "type": db_appointment.type,
            "status": db_appointment.status
        }
        patient_data = {
            "id": patient.id,
            "first_name": patient.first_name,
            "last_name": patient.last_name,
            "phone": patient.phone
        }
        await websocket_manager.notify_appointment_booked(
            appointment_data, patient_data, appointment.doctor_id
        )
    
    return AppointmentResponse.from_orm(db_appointment)

@router.get("/", response_model=List[AppointmentResponse])
async def get_appointments(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    date_filter: Optional[date] = Query(None, alias="date"),
    doctor_id: Optional[str] = Query(None),
    patient_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """Get appointments with filtering"""
    query = db.query(Appointment)
    
    # Apply filters
    if date_filter:
        query = query.filter(Appointment.appointment_date == date_filter)
    
    if doctor_id:
        query = query.filter(Appointment.doctor_id == doctor_id)
    
    if patient_id:
        query = query.filter(Appointment.patient_id == patient_id)
    
    if status:
        query = query.filter(Appointment.status == status)
    
    # If user is a doctor, only show their appointments
    if current_user.role == "doctor":
        query = query.filter(Appointment.doctor_id == current_user.id)
    
    # Apply pagination and ordering
    appointments = query.order_by(
        Appointment.appointment_date,
        Appointment.appointment_time
    ).offset(skip).limit(limit).all()
    
    return [AppointmentResponse.from_orm(appointment) for appointment in appointments]

@router.get("/{appointment_id}", response_model=AppointmentResponse)
async def get_appointment(
    appointment_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """Get appointment by ID"""
    query = db.query(Appointment).filter(Appointment.id == appointment_id)
    
    # If user is a doctor, only show their appointments
    if current_user.role == "doctor":
        query = query.filter(Appointment.doctor_id == current_user.id)
    
    appointment = query.first()
    if not appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Appointment not found"
        )
    
    return AppointmentResponse.from_orm(appointment)

@router.put("/{appointment_id}", response_model=AppointmentResponse)
async def update_appointment(
    appointment_id: str,
    appointment_update: AppointmentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_registrar_or_admin)
):
    """Update appointment"""
    appointment = db.query(Appointment).filter(Appointment.id == appointment_id).first()
    if not appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Appointment not found"
        )
    
    # Update appointment fields
    update_data = appointment_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(appointment, field, value)
    
    db.commit()
    db.refresh(appointment)
    
    return AppointmentResponse.from_orm(appointment)

@router.delete("/{appointment_id}")
async def cancel_appointment(
    appointment_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_registrar_or_admin)
):
    """Cancel appointment"""
    appointment = db.query(Appointment).filter(Appointment.id == appointment_id).first()
    if not appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Appointment not found"
        )
    
    # Set status to cancelled instead of deleting
    appointment.status = "cancelled"
    db.commit()
    
    return {"message": "Appointment cancelled successfully"}
