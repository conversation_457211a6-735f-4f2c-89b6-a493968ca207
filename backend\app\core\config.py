"""
Configuration settings for Clinineo Backend
إعدادات التكوين للخادم الخلفي
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import validator

class Settings(BaseSettings):
    """Application settings"""
    
    # Server settings
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Database settings
    DATABASE_URL: str = "sqlite:///./clinineo.db"
    
    # Security settings
    SECRET_KEY: str = "clinineo-super-secret-key-2024-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440  # 24 hours
    
    # File upload settings
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: list = [
        "image/jpeg", "image/png", "image/gif", 
        "application/pdf", "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ]
    
    # CORS settings
    CORS_ORIGINS: list = ["*"]
    
    # Pagination settings
    DEFAULT_PAGE_SIZE: int = 10
    MAX_PAGE_SIZE: int = 100
    
    # Notification settings
    ENABLE_NOTIFICATIONS: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = True

    @validator("UPLOAD_DIR")
    def create_upload_dir(cls, v):
        """Create upload directory if it doesn't exist"""
        if not os.path.exists(v):
            os.makedirs(v)
        return v

# Create settings instance
settings = Settings()

# Application constants
class AppConstants:
    """Application constants"""
    
    # User roles
    ADMIN_ROLE = "admin"
    DOCTOR_ROLE = "doctor"
    REGISTRAR_ROLE = "registrar"
    
    # Appointment types
    CONSULTATION = "consultation"
    FOLLOW_UP = "follow_up"
    EMERGENCY = "emergency"
    CHECKUP = "checkup"
    
    # Appointment statuses
    SCHEDULED = "scheduled"
    CONFIRMED = "confirmed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    
    # Gender options
    MALE = "male"
    FEMALE = "female"
    
    # Blood types
    BLOOD_TYPES = ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"]
    
    # File types
    XRAY = "xray"
    LAB_RESULT = "lab_result"
    PRESCRIPTION = "prescription"
    REPORT = "report"
    OTHER = "other"
    
    # Notification types
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    
    # WebSocket events
    PATIENT_REGISTERED = "patient_registered"
    APPOINTMENT_BOOKED = "appointment_booked"
    NEW_NOTIFICATION = "new_notification"
