# Clinineo - نظام إدارة العيادة

## نظرة عامة
نظام شامل لإدارة العيادة يتضمن تطبيقات موبايل وسطح مكتب مع نظام تنبيهات فوري وتقارير شاملة.

## الأدوار
- **الأدمن**: إدارة النظام والمستخدمين والإعدادات
- **المسجل**: تسجيل المرضى والمواعيد وإدارة الاستقبال
- **الطبيب**: استقبال التنبيهات والفحص والتشخيص والعلاج

## الميزات الأساسية
- ✅ نظام مصادقة متعدد الأدوار
- ✅ تسجيل المرضى والمواعيد
- ✅ نظام تنبيهات فوري للأطباء
- ✅ إدارة الفحوصات والتحاليل والأشعة
- ✅ رفع وإدارة الملفات الطبية
- ✅ تقارير وإحصائيات الأمراض والعلاجات
- ✅ أمان قوي وحفظ البيانات

## التقنيات المستخدمة
- **Backend**: Node.js + Express.js + Socket.io (للتنبيهات الفورية)
- **Database**: PostgreSQL + Supabase
- **Mobile**: Flutter (Android/iOS)
- **Desktop**: Flutter Desktop (Windows/Mac/Linux)
- **File Storage**: Supabase Storage
- **Real-time**: Socket.io للتنبيهات الفورية

## هيكل المشروع
```
clinineo/
├── backend/          # خادم API ونظام التنبيهات
├── mobile-app/       # تطبيق Flutter للموبايل
├── desktop-app/      # تطبيق Flutter لسطح المكتب
├── shared/           # مكونات مشتركة
└── docs/            # التوثيق
```

## سير العمل
1. المسجل يسجل مريض جديد أو يحدث موعد
2. النظام يرسل تنبيه فوري للطبيب المختص
3. الطبيب يستقبل التنبيه مع بيانات المريض كاملة
4. الطبيب يقوم بالفحص ويسجل التشخيص والعلاج
5. النظام يحدث الإحصائيات والتقارير تلقائياً

## البدء
```bash
# تشغيل الخادم
cd backend
npm install
npm run dev

# تشغيل تطبيق الموبايل
cd mobile-app
flutter pub get
flutter run

# تشغيل تطبيق سطح المكتب
cd desktop-app
flutter pub get
flutter run -d windows
```
