# Clinineo - نظام إدارة العيادة

## نظرة عامة
نظام شامل لإدارة العيادة مبني بـ Python مع تطبيق سطح مكتب، يتضمن نظام تنبيهات فوري وتقارير شاملة.

## الأدوار
- **الأدمن**: إدارة النظام والمستخدمين والإعدادات
- **المسجل**: تسجيل المرضى والمواعيد وإدارة الاستقبال
- **الطبيب**: استقبال التنبيهات والفحص والتشخيص والعلاج

## الميزات الأساسية
- ✅ نظام مصادقة متعدد الأدوار
- ✅ تسجيل المرضى والمواعيد
- ✅ نظام تنبيهات فوري للأطباء
- ✅ إدارة الفحوصات والتحاليل والأشعة
- ✅ رفع وإدارة الملفات الطبية
- ✅ تقارير وإحصائيات الأمراض والعلاجات
- ✅ أمان قوي وحفظ البيانات

## التقنيات المستخدمة
- **Backend**: Python + FastAPI + WebSockets (للتنبيهات الفورية)
- **Database**: SQLite/PostgreSQL + SQLAlchemy
- **Desktop App**: Python + Tkinter/PyQt + Requests
- **Authentication**: JWT + bcrypt
- **File Storage**: Local Storage + Base64
- **Real-time**: WebSockets للتنبيهات الفورية

## هيكل المشروع
```
clinineo/
├── backend/          # خادم API Python FastAPI
├── desktop-app/      # تطبيق سطح المكتب Python
├── database/         # ملفات قاعدة البيانات
├── shared/           # مكونات مشتركة
└── docs/            # التوثيق
```

## سير العمل
1. المسجل يسجل مريض جديد أو يحدث موعد
2. النظام يرسل تنبيه فوري للطبيب المختص
3. الطبيب يستقبل التنبيه مع بيانات المريض كاملة
4. الطبيب يقوم بالفحص ويسجل التشخيص والعلاج
5. النظام يحدث الإحصائيات والتقارير تلقائياً

## البدء
```bash
# تشغيل الخادم
cd backend
pip install -r requirements.txt
python main.py

# تشغيل تطبيق سطح المكتب
cd desktop-app
pip install -r requirements.txt
python main.py
```
