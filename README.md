# 🏥 Clinineo - نظام إدارة العيادات المتطور

## 📱 تطبيقين أندرويد مميزين بواجهات احترافية

![Clinineo](https://img.shields.io/badge/Clinineo-v1.0.0-blue)
![Python](https://img.shields.io/badge/Python-3.7+-green)
![Kivy](https://img.shields.io/badge/Kivy-2.0+-orange)
![License](https://img.shields.io/badge/License-MIT-yellow)

---

## 🎯 **المشروع:**

**Clinineo** هو نظام إدارة عيادات متطور يتكون من تطبيقين أندرويد:

### 🏥 **تطبيق الاستقبال (Registrar)**
- واجهة زرقاء احترافية
- إضافة وإدارة المرضى
- خادم مدمج للاتصال
- نسخ احتياطي للبيانات

### 🩺 **تطبيق الطبيب (Doctor)**
- واجهة خضراء طبية
- تنبيهات فورية
- بحث متقدم في المرضى
- تحديث تلقائي

---

## 🚀 **التشغيل السريع:**

### **الطريقة الأسهل:**
```bash
python start_clinineo.py
```

### **التشغيل المباشر:**
```bash
# تثبيت المتطلبات
pip install kivy requests

# تشغيل تطبيق الاستقبال
python clinineo_registrar.py

# تشغيل تطبيق الطبيب (في نافذة أخرى)
python clinineo_doctor.py
```

---

## 📋 **المتطلبات:**

- **Python 3.7+**
- **Kivy 2.0+** - للواجهات
- **Requests** - للاتصال بالشبكة

---

## 🎨 **الميزات:**

### ✨ **واجهات مميزة:**
- تصميم Material Design
- ألوان طبية احترافية
- رسوم متحركة ناعمة
- تخطيط متجاوب

### 🔔 **تنبيهات فورية:**
- إشعارات عند إضافة مرضى
- تحديث تلقائي كل 10 ثواني
- رسوم متحركة للتفاعل

### 💾 **إدارة البيانات:**
- حفظ محلي بصيغة JSON
- نسخ احتياطي تلقائي
- تحقق من صحة البيانات

### 🌐 **الشبكة:**
- خادم HTTP مدمج
- عمل على الشبكة المحلية
- بدون حاجة للإنترنت

---

## 📁 **هيكل المشروع:**

```
Clinineo/
├── clinineo_registrar.py    # تطبيق الاستقبال
├── clinineo_doctor.py       # تطبيق الطبيب
├── start_clinineo.py        # مشغل التطبيقات
├── CLININEO_GUIDE.md        # دليل مفصل
├── README.md                # هذا الملف
└── clinineo_data.json       # قاعدة البيانات (تُنشأ تلقائياً)
```

---

## 🔄 **سير العمل:**

```
1. 🏥 موظف الاستقبال يشغل التطبيق
   ├── يصبح خادم على المنفذ 8080
   └── يضيف مريض جديد

2. 🩺 الطبيب يشغل التطبيق
   ├── يتصل بخادم الاستقبال
   └── يستقبل تنبيه فوري

3. 🔄 التحديث التلقائي
   ├── كل 10 ثواني
   └── مزامنة البيانات
```

---

## 🎯 **الاختبار:**

### **اختبار سريع:**
1. شغل `python clinineo_registrar.py`
2. انتظر "🟢 الخادم يعمل"
3. شغل `python clinineo_doctor.py` في نافذة أخرى
4. انتظر "🟢 متصل"
5. أضف مريض في تطبيق الاستقبال
6. شاهد التنبيه في تطبيق الطبيب!

---

## 📱 **لقطات الشاشة:**

### 🏥 تطبيق الاستقبال:
- شاشة رئيسية بإحصائيات
- نموذج إضافة مريض شامل
- قائمة مرضى مع بطاقات مميزة
- إعدادات متقدمة

### 🩺 تطبيق الطبيب:
- لوحة معلومات طبية
- تنبيهات مع رسوم متحركة
- بحث متقدم في المرضى
- إعدادات الاتصال

---

## 🔧 **التخصيص:**

### **تغيير الألوان:**
```python
# في clinineo_registrar.py
bg_color = (0.2, 0.6, 1, 1)  # أزرق

# في clinineo_doctor.py
bg_color = (0.3, 0.7, 0.3, 1)  # أخضر
```

### **تغيير المنفذ:**
```python
# في clinineo_registrar.py
self.server = HTTPServer(('', 8080), ClinicAPIHandler)
```

---

## 🐛 **حل المشاكل:**

### **❌ "ModuleNotFoundError: No module named 'kivy'"**
```bash
pip install kivy
```

### **❌ "غير متصل بتطبيق الاستقبال"**
- تأكد من تشغيل تطبيق الاستقبال أولاً
- تحقق من عنوان IP في الإعدادات

### **❌ "الخادم متوقف"**
- أعد تشغيل تطبيق الاستقبال
- تأكد من عدم استخدام المنفذ 8080

---

## 🤝 **المساهمة:**

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. إرسال Pull Request

---

## 📄 **الترخيص:**

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

---

## 👥 **الفريق:**

- **المطور الرئيسي:** Clinineo Team
- **التصميم:** Material Design
- **التقنية:** Python + Kivy

---

## 📞 **الدعم:**

للدعم والاستفسارات:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +**********
- 🌐 الموقع: www.clinineo.com

---

## 🎉 **شكر خاص:**

- **Kivy Team** - إطار العمل الرائع
- **Python Community** - الدعم المستمر
- **Material Design** - مبادئ التصميم

---

## 🔮 **المستقبل:**

### **الإصدارات القادمة:**
- 📊 تقارير وإحصائيات متقدمة
- 🔐 نظام مصادقة محسن
- 📱 تطبيق موبايل أصلي
- ☁️ مزامنة سحابية
- 🎨 ثيمات متعددة

---

**🏥 Clinineo - نظام إدارة العيادات المتطور**

*"تطوير الرعاية الصحية من خلال التكنولوجيا"*

---

[![Made with ❤️](https://img.shields.io/badge/Made%20with-❤️-red)](https://github.com/clinineo)
[![Python](https://img.shields.io/badge/Made%20with-Python-blue)](https://python.org)
[![Kivy](https://img.shields.io/badge/UI%20with-Kivy-orange)](https://kivy.org)
