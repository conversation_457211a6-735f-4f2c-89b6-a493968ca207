#!/usr/bin/env python3
"""
Clinineo Local System Launcher
مشغل نظام Clinineo المحلي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
import threading
import time
import socket

class ClinicLauncher:
    """مشغل نظام العيادة"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Clinineo - مشغل النظام")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # متغيرات العمليات
        self.server_process = None
        self.server_running = False
        
        self.setup_ui()
        self.check_requirements()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = ttk.Frame(self.root, padding="20")
        title_frame.pack(fill=tk.X)
        
        ttk.Label(title_frame, text="🏥 نظام Clinineo لإدارة العيادة", 
                 font=('Arial', 16, 'bold')).pack()
        
        ttk.Label(title_frame, text="نظام محلي - لا يحتاج إنترنت", 
                 font=('Arial', 10), foreground="green").pack(pady=5)
        
        # معلومات النظام
        info_frame = ttk.LabelFrame(self.root, text="معلومات النظام", padding="10")
        info_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.server_status_label = ttk.Label(info_frame, text="الخادم: غير مشغل", foreground="red")
        self.server_status_label.pack(anchor='w')
        
        self.ip_label = ttk.Label(info_frame, text=f"عنوان IP: {self.get_local_ip()}")
        self.ip_label.pack(anchor='w')
        
        self.port_label = ttk.Label(info_frame, text="المنفذ: 8080")
        self.port_label.pack(anchor='w')
        
        # أزرار التحكم
        control_frame = ttk.LabelFrame(self.root, text="التحكم في النظام", padding="10")
        control_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.start_server_btn = ttk.Button(control_frame, text="🚀 تشغيل الخادم", 
                                          command=self.start_server, width=20)
        self.start_server_btn.pack(pady=5)
        
        self.stop_server_btn = ttk.Button(control_frame, text="🛑 إيقاف الخادم", 
                                         command=self.stop_server, width=20, state='disabled')
        self.stop_server_btn.pack(pady=5)
        
        # أزرار التطبيقات
        apps_frame = ttk.LabelFrame(self.root, text="التطبيقات", padding="10")
        apps_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Button(apps_frame, text="👨‍⚕️ تطبيق الطبيب", 
                  command=self.launch_doctor_app, width=20).pack(pady=2)
        
        ttk.Button(apps_frame, text="👩‍💼 تطبيق موظف الاستقبال", 
                  command=self.launch_registrar_app, width=20).pack(pady=2)
        
        # معلومات تسجيل الدخول
        login_frame = ttk.LabelFrame(self.root, text="بيانات تسجيل الدخول", padding="10")
        login_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Label(login_frame, text="الطبيب: <EMAIL> / doctor123", 
                 font=('Arial', 9)).pack(anchor='w')
        ttk.Label(login_frame, text="موظف الاستقبال: <EMAIL> / registrar123", 
                 font=('Arial', 9)).pack(anchor='w')
        ttk.Label(login_frame, text="المدير: <EMAIL> / admin123", 
                 font=('Arial', 9)).pack(anchor='w')
        
        # شريط الحالة
        self.status_label = ttk.Label(self.root, text="جاهز للتشغيل", 
                                     relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(fill=tk.X, side=tk.BOTTOM)
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def check_requirements(self):
        """فحص المتطلبات"""
        try:
            import requests
            self.update_status("✅ جميع المتطلبات متوفرة")
        except ImportError:
            self.update_status("❌ مكتبة requests غير مثبتة")
            messagebox.showwarning("تحذير", "مكتبة requests غير مثبتة\nسيتم تثبيتها تلقائياً")
            self.install_requirements()
    
    def install_requirements(self):
        """تثبيت المتطلبات"""
        def install():
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'requests'])
                self.root.after(0, lambda: self.update_status("✅ تم تثبيت المتطلبات بنجاح"))
            except:
                self.root.after(0, lambda: self.update_status("❌ فشل في تثبيت المتطلبات"))
        
        threading.Thread(target=install, daemon=True).start()
    
    def start_server(self):
        """تشغيل الخادم"""
        if self.server_running:
            return
        
        def run_server():
            try:
                self.server_process = subprocess.Popen([
                    sys.executable, 'local_server.py'
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                
                self.server_running = True
                self.root.after(0, self.on_server_started)
                
                # انتظار انتهاء العملية
                self.server_process.wait()
                
            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"❌ خطأ في تشغيل الخادم: {str(e)}"))
            finally:
                self.server_running = False
                self.root.after(0, self.on_server_stopped)
        
        threading.Thread(target=run_server, daemon=True).start()
        self.update_status("🚀 جاري تشغيل الخادم...")
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_process:
            self.server_process.terminate()
            self.server_running = False
            self.on_server_stopped()
    
    def on_server_started(self):
        """عند بدء تشغيل الخادم"""
        self.server_status_label.config(text="الخادم: يعمل", foreground="green")
        self.start_server_btn.config(state='disabled')
        self.stop_server_btn.config(state='normal')
        self.update_status("✅ الخادم يعمل بنجاح")
        
        # إظهار رسالة نجاح
        local_ip = self.get_local_ip()
        messagebox.showinfo("نجح", 
                           f"تم تشغيل الخادم بنجاح!\n\n"
                           f"العنوان المحلي: http://127.0.0.1:8080\n"
                           f"عنوان الشبكة: http://{local_ip}:8080\n\n"
                           f"يمكن الآن تشغيل التطبيقات")
    
    def on_server_stopped(self):
        """عند إيقاف الخادم"""
        self.server_status_label.config(text="الخادم: غير مشغل", foreground="red")
        self.start_server_btn.config(state='normal')
        self.stop_server_btn.config(state='disabled')
        self.update_status("🛑 تم إيقاف الخادم")
    
    def launch_doctor_app(self):
        """تشغيل تطبيق الطبيب"""
        if not self.server_running:
            messagebox.showwarning("تحذير", "يجب تشغيل الخادم أولاً")
            return
        
        try:
            subprocess.Popen([sys.executable, 'doctor_app.py'])
            self.update_status("🚀 تم تشغيل تطبيق الطبيب")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل تطبيق الطبيب: {str(e)}")
    
    def launch_registrar_app(self):
        """تشغيل تطبيق موظف الاستقبال"""
        if not self.server_running:
            messagebox.showwarning("تحذير", "يجب تشغيل الخادم أولاً")
            return
        
        try:
            subprocess.Popen([sys.executable, 'registrar_app.py'])
            self.update_status("🚀 تم تشغيل تطبيق موظف الاستقبال")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل تطبيق موظف الاستقبال: {str(e)}")
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
    
    def on_closing(self):
        """عند إغلاق التطبيق"""
        if self.server_running:
            if messagebox.askyesno("تأكيد", "الخادم ما زال يعمل. هل تريد إيقافه وإغلاق التطبيق؟"):
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    print("🏥 Clinineo Local System Launcher")
    print("=" * 40)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ['local_server.py', 'doctor_app.py', 'registrar_app.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    print("✅ جميع الملفات موجودة")
    print("🚀 تشغيل مشغل النظام...")
    
    app = ClinicLauncher()
    app.run()
