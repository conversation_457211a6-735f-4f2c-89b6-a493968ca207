#!/usr/bin/env python3
"""
Simple Doctor App - No Server Required
تطبيق الطبيب البسيط - بدون خادم
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, date
import os
import sys

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from network_shared_data import network_shared_data as shared_data
except ImportError:
    try:
        from shared_data import shared_data
    except ImportError:
        messagebox.showerror("خطأ", "ملف shared_data.py أو network_shared_data.py غير موجود")
        sys.exit(1)

class SimpleDoctorApp:
    """تطبيق الطبيب البسيط"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Clinineo - الطبيب (بدون خادم)")
        self.root.geometry("1200x800")
        self.root.minsize(900, 600)
        
        self.current_user = None
        self.auto_refresh = True
        self.last_notification_count = 0
        
        # إعداد الواجهة
        self.setup_ui()
        self.start_auto_refresh()
        
        # محاولة تسجيل دخول تلقائي
        self.auto_login()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة المرضى
        patients_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المرضى", menu=patients_menu)
        patients_menu.add_command(label="قائمة المرضى", command=self.show_patients_list)
        
        # قائمة التنبيهات
        notifications_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التنبيهات", menu=notifications_menu)
        notifications_menu.add_command(label="التنبيهات الجديدة", command=self.show_notifications)
        notifications_menu.add_command(label="جميع التنبيهات", command=self.show_all_notifications)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="لوحة المعلومات", command=self.show_dashboard)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="تسجيل دخول", command=self.show_login)
        settings_menu.add_command(label="تحديث البيانات", command=self.refresh_data)
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط الحالة والتنبيهات
        self.status_frame = ttk.Frame(main_frame)
        self.status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(self.status_frame, text="جاهز", foreground="green")
        self.status_label.pack(side=tk.LEFT)
        
        # منطقة التنبيهات
        self.notification_frame = ttk.Frame(self.status_frame)
        self.notification_frame.pack(side=tk.RIGHT)
        
        self.notification_label = ttk.Label(self.notification_frame, text="", foreground="blue")
        self.notification_label.pack(side=tk.RIGHT, padx=10)
        
        self.user_label = ttk.Label(self.status_frame, text="")
        self.user_label.pack(side=tk.RIGHT)
        
        # منطقة المحتوى
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # الصفحة الرئيسية
        self.show_home_page()
    
    def auto_login(self):
        """تسجيل دخول تلقائي للاختبار"""
        user = shared_data.authenticate_user("<EMAIL>", "doctor123")
        if user:
            self.current_user = user
            user_info = f"د. {user['first_name']} {user['last_name']}"
            if user.get('specialization'):
                user_info += f" - {user['specialization']}"
            self.user_label.config(text=user_info)
            self.status_label.config(text="تم تسجيل الدخول تلقائياً", foreground="blue")
            self.check_notifications()
    
    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        def refresh_loop():
            while self.auto_refresh:
                try:
                    if self.current_user:
                        # فحص التنبيهات كل 5 ثواني
                        self.root.after(0, self.check_notifications)
                    time.sleep(5)
                except:
                    break
        
        threading.Thread(target=refresh_loop, daemon=True).start()
    
    def check_notifications(self):
        """فحص التنبيهات الجديدة"""
        if not self.current_user:
            return
        
        try:
            notifications = shared_data.get_notifications(self.current_user['id'], unread_only=True)
            count = len(notifications)
            
            if count > 0:
                self.notification_label.config(
                    text=f"🔔 {count} تنبيه جديد",
                    foreground="red"
                )
                
                # إذا كان هناك تنبيهات جديدة، أظهر رسالة
                if count > self.last_notification_count:
                    self.show_notification_popup(notifications[0])
                
            else:
                self.notification_label.config(
                    text="لا توجد تنبيهات جديدة",
                    foreground="green"
                )
            
            self.last_notification_count = count
            
        except Exception as e:
            print(f"خطأ في فحص التنبيهات: {e}")
    
    def show_notification_popup(self, notification):
        """إظهار نافذة تنبيه منبثقة"""
        popup = tk.Toplevel(self.root)
        popup.title("تنبيه جديد")
        popup.geometry("300x150")
        popup.resizable(False, False)
        
        # جعل النافذة في المقدمة
        popup.lift()
        popup.attributes('-topmost', True)
        
        ttk.Label(popup, text=notification.get('title', ''), 
                 font=('Arial', 12, 'bold')).pack(pady=10)
        
        ttk.Label(popup, text=notification.get('message', ''), 
                 wraplength=250).pack(pady=5)
        
        def close_popup():
            # تمييز التنبيه كمقروء
            shared_data.mark_notification_read(notification['id'])
            popup.destroy()
            self.check_notifications()
        
        ttk.Button(popup, text="موافق", command=close_popup).pack(pady=10)
        
        # إغلاق تلقائي بعد 10 ثواني
        popup.after(10000, close_popup)
    
    def show_login(self):
        """إظهار نافذة تسجيل الدخول"""
        dialog = tk.Toplevel(self.root)
        dialog.title("تسجيل الدخول")
        dialog.geometry("300x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="البريد الإلكتروني:").pack(pady=5)
        email_entry = ttk.Entry(dialog, width=25)
        email_entry.pack(pady=5)
        email_entry.insert(0, "<EMAIL>")
        
        ttk.Label(dialog, text="كلمة المرور:").pack(pady=5)
        password_entry = ttk.Entry(dialog, width=25, show="*")
        password_entry.pack(pady=5)
        password_entry.insert(0, "doctor123")
        
        def login():
            email = email_entry.get().strip()
            password = password_entry.get().strip()
            
            user = shared_data.authenticate_user(email, password)
            if user and user.get('role') == 'doctor':
                self.current_user = user
                user_info = f"د. {user['first_name']} {user['last_name']}"
                if user.get('specialization'):
                    user_info += f" - {user['specialization']}"
                self.user_label.config(text=user_info)
                dialog.destroy()
                messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح")
                self.check_notifications()
                self.show_dashboard()
            else:
                messagebox.showerror("خطأ", "بيانات تسجيل الدخول غير صحيحة أو ليس لديك صلاحية طبيب")
        
        ttk.Button(dialog, text="تسجيل دخول", command=login).pack(pady=10)
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.config(text="تم تحديث البيانات", foreground="green")
        self.check_notifications()
        # إعادة تحميل الصفحة الحالية
        if hasattr(self, 'current_page'):
            if self.current_page == 'home':
                self.show_home_page()
            elif self.current_page == 'dashboard':
                self.show_dashboard()
            elif self.current_page == 'patients':
                self.show_patients_list()
    
    def show_home_page(self):
        """إظهار الصفحة الرئيسية"""
        self.current_page = 'home'
        
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # العنوان
        title_label = ttk.Label(self.content_frame, text="نظام إدارة العيادة - الطبيب", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # الأزرار الرئيسية
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(pady=20)
        
        ttk.Button(buttons_frame, text="لوحة المعلومات", 
                  command=self.show_dashboard, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="التنبيهات الجديدة", 
                  command=self.show_notifications, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="قائمة المرضى", 
                  command=self.show_patients_list, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="تحديث البيانات", 
                  command=self.refresh_data, width=20).pack(pady=5)
        
        # رسالة ترحيب
        if not self.current_user:
            welcome_label = ttk.Label(self.content_frame, 
                                    text="يرجى تسجيل الدخول للوصول إلى جميع الميزات",
                                    font=('Arial', 12), foreground="orange")
            welcome_label.pack(pady=20)
        else:
            welcome_label = ttk.Label(self.content_frame, 
                                    text=f"مرحباً د. {self.current_user['first_name']} {self.current_user['last_name']}",
                                    font=('Arial', 14), foreground="blue")
            welcome_label.pack(pady=20)
    
    def show_dashboard(self):
        """إظهار لوحة المعلومات"""
        self.current_page = 'dashboard'
        
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        ttk.Label(self.content_frame, text="لوحة المعلومات", 
                 font=('Arial', 14, 'bold')).pack(pady=10)
        
        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(self.content_frame, text="الإحصائيات", padding="10")
        stats_frame.pack(fill=tk.X, pady=10, padx=20)
        
        try:
            stats = shared_data.get_dashboard_stats()
            
            # عرض الإحصائيات في شبكة
            stats_grid = ttk.Frame(stats_frame)
            stats_grid.pack(fill=tk.X)
            
            # الصف الأول
            ttk.Label(stats_grid, text=f"إجمالي المرضى: {stats.get('total_patients', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=0, column=0, padx=20, pady=5, sticky='w')
            
            ttk.Label(stats_grid, text=f"مواعيد اليوم: {stats.get('today_appointments', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=0, column=1, padx=20, pady=5, sticky='w')
            
            # الصف الثاني
            ttk.Label(stats_grid, text=f"الأطباء النشطين: {stats.get('active_doctors', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=1, column=0, padx=20, pady=5, sticky='w')
            
        except Exception as e:
            ttk.Label(stats_frame, text=f"فشل في تحميل الإحصائيات: {str(e)}", 
                     foreground="red").pack()
        
        # إطار التنبيهات الحديثة
        notifications_frame = ttk.LabelFrame(self.content_frame, text="التنبيهات الحديثة", padding="10")
        notifications_frame.pack(fill=tk.BOTH, expand=True, pady=10, padx=20)
        
        # قائمة التنبيهات
        notifications_text = tk.Text(notifications_frame, height=10, wrap=tk.WORD)
        notifications_text.pack(fill=tk.BOTH, expand=True)
        
        if self.current_user:
            try:
                notifications = shared_data.get_notifications(self.current_user['id'])[:5]  # آخر 5 تنبيهات
                
                if notifications:
                    for notification in notifications:
                        status = "✅" if notification.get('is_read') else "🔔"
                        time_str = notification.get('created_at', '')[:16].replace('T', ' ')
                        notifications_text.insert(tk.END, 
                            f"{status} {time_str} - {notification.get('title', '')}\n"
                            f"   {notification.get('message', '')}\n\n")
                else:
                    notifications_text.insert(tk.END, "لا توجد تنبيهات")
                    
            except Exception as e:
                notifications_text.insert(tk.END, f"خطأ في تحميل التنبيهات: {str(e)}")
        
        notifications_text.config(state=tk.DISABLED)
        
        # زر العودة
        ttk.Button(self.content_frame, text="العودة للرئيسية", 
                  command=self.show_home_page).pack(pady=10)
    
    def show_notifications(self):
        """إظهار التنبيهات الجديدة"""
        if not self.current_user:
            messagebox.showwarning("تحذير", "يرجى تسجيل الدخول أولاً")
            return
        
        dialog = tk.Toplevel(self.root)
        dialog.title("التنبيهات الجديدة")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        
        ttk.Label(dialog, text="التنبيهات الجديدة", font=('Arial', 14, 'bold')).pack(pady=10)
        
        # قائمة التنبيهات
        listbox = tk.Listbox(dialog, height=15)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        try:
            notifications = shared_data.get_notifications(self.current_user['id'], unread_only=True)
            
            if notifications:
                for notification in notifications:
                    time_str = notification.get('created_at', '')[:16].replace('T', ' ')
                    listbox.insert(tk.END, f"{time_str} - {notification.get('title', '')}")
            else:
                listbox.insert(tk.END, "لا توجد تنبيهات جديدة")
                
        except Exception as e:
            listbox.insert(tk.END, f"خطأ في تحميل التنبيهات: {str(e)}")
        
        ttk.Button(dialog, text="إغلاق", command=dialog.destroy).pack(pady=10)
    
    def show_all_notifications(self):
        """إظهار جميع التنبيهات"""
        if not self.current_user:
            messagebox.showwarning("تحذير", "يرجى تسجيل الدخول أولاً")
            return
        
        dialog = tk.Toplevel(self.root)
        dialog.title("جميع التنبيهات")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        
        ttk.Label(dialog, text="جميع التنبيهات", font=('Arial', 14, 'bold')).pack(pady=10)
        
        # جدول التنبيهات
        columns = ('الوقت', 'العنوان', 'الحالة')
        tree = ttk.Treeview(dialog, columns=columns, show='headings', height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        try:
            notifications = shared_data.get_notifications(self.current_user['id'])
            
            for notification in notifications:
                time_str = notification.get('created_at', '')[:16].replace('T', ' ')
                status = "مقروء" if notification.get('is_read') else "جديد"
                tree.insert('', tk.END, values=(
                    time_str,
                    notification.get('title', ''),
                    status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل التنبيهات: {str(e)}")
        
        tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Button(dialog, text="إغلاق", command=dialog.destroy).pack(pady=10)
    
    def show_patients_list(self):
        """إظهار قائمة المرضى"""
        self.current_page = 'patients'
        
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        ttk.Label(self.content_frame, text="قائمة المرضى", 
                 font=('Arial', 14, 'bold')).pack(pady=10)
        
        # جدول المرضى
        columns = ('ID', 'الاسم', 'الهاتف', 'الجنس', 'العمر')
        tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        # تحميل البيانات
        try:
            patients = shared_data.get_patients()
            for patient in patients:
                # حساب العمر
                try:
                    birth_date = datetime.strptime(patient.get('date_of_birth', ''), '%Y-%m-%d').date()
                    age = (date.today() - birth_date).days // 365
                except:
                    age = "غير محدد"
                
                tree.insert('', tk.END, values=(
                    patient.get('id', ''),
                    f"{patient.get('first_name', '')} {patient.get('last_name', '')}",
                    patient.get('phone', ''),
                    'ذكر' if patient.get('gender') == 'male' else 'أنثى',
                    age
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
        
        tree.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(pady=10)
        
        ttk.Button(buttons_frame, text="تحديث", command=self.show_patients_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="العودة للرئيسية", command=self.show_home_page).pack(side=tk.LEFT, padx=5)
    
    def on_closing(self):
        """عند إغلاق التطبيق"""
        self.auto_refresh = False
        self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    print("🚀 تشغيل تطبيق الطبيب البسيط...")
    app = SimpleDoctorApp()
    app.run()
