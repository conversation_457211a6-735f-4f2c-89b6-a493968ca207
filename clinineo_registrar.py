#!/usr/bin/env python3
"""
Clinineo Registrar App - Premium Android Interface
تطبيق Clinineo للاستقبال - واجهة أندرويد مميزة
"""

from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.floatlayout import FloatLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.popup import Popup
from kivy.uix.spinner import Spinner
from kivy.uix.scrollview import ScrollView
from kivy.uix.card import MDCard
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.graphics import Color, RoundedRectangle, Line
from kivy.uix.widget import Widget
import json
import os
import uuid
from datetime import datetime
import socket
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading

class ClinicData:
    """مدير البيانات المحلي"""
    
    def __init__(self):
        self.data_file = "clinineo_data.json"
        self.data = self.load_data()
    
    def load_data(self):
        """تحميل البيانات"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "patients": [],
            "appointments": [],
            "notifications": [],
            "stats": {"total_patients": 0, "today_appointments": 0},
            "last_updated": datetime.now().isoformat()
        }
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            self.data["last_updated"] = datetime.now().isoformat()
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def add_patient(self, patient_data):
        """إضافة مريض جديد"""
        patient_id = str(uuid.uuid4())[:8]
        patient = {
            "id": patient_id,
            "first_name": patient_data.get("first_name", ""),
            "last_name": patient_data.get("last_name", ""),
            "date_of_birth": patient_data.get("date_of_birth", ""),
            "gender": patient_data.get("gender", ""),
            "phone": patient_data.get("phone", ""),
            "email": patient_data.get("email", ""),
            "address": patient_data.get("address", ""),
            "blood_type": patient_data.get("blood_type", ""),
            "allergies": patient_data.get("allergies", ""),
            "created_at": datetime.now().isoformat()
        }
        
        self.data["patients"].insert(0, patient)
        self.data["stats"]["total_patients"] = len(self.data["patients"])
        
        # إضافة تنبيه للطبيب
        notification = {
            "id": str(uuid.uuid4())[:8],
            "title": "مريض جديد",
            "message": f"تم تسجيل مريض جديد: {patient['first_name']} {patient['last_name']}",
            "type": "new_patient",
            "patient_id": patient_id,
            "is_read": False,
            "created_at": datetime.now().isoformat()
        }
        
        self.data["notifications"].insert(0, notification)
        self.save_data()
        return patient_id

class StyledButton(Button):
    """زر مخصص بتصميم مميز"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.background_color = (0, 0, 0, 0)  # شفاف
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        
        with self.canvas.before:
            Color(0.2, 0.6, 1, 1)  # أزرق مميز
            self.rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[15])
    
    def update_graphics(self, *args):
        self.rect.pos = self.pos
        self.rect.size = self.size

class StyledCard(Widget):
    """بطاقة مخصصة بتصميم مميز"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        with self.canvas.before:
            Color(1, 1, 1, 1)  # أبيض
            self.rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[10])
            Color(0.9, 0.9, 0.9, 1)  # حدود رمادية
            self.border = Line(rounded_rectangle=(self.x, self.y, self.width, self.height, 10), width=1)
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
    
    def update_graphics(self, *args):
        self.rect.pos = self.pos
        self.rect.size = self.size
        self.border.rounded_rectangle = (self.x, self.y, self.width, self.height, 10)

class HomeScreen(Screen):
    """الشاشة الرئيسية"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'home'
        self.build_ui()
    
    def build_ui(self):
        """بناء واجهة الشاشة الرئيسية"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))
        
        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(80))
        
        # Logo and Title
        title_layout = BoxLayout(orientation='vertical')
        title_label = Label(
            text='🏥 Clinineo',
            font_size=dp(28),
            bold=True,
            color=(0.2, 0.6, 1, 1),
            size_hint_y=None,
            height=dp(40)
        )
        subtitle_label = Label(
            text='نظام إدارة العيادة - الاستقبال',
            font_size=dp(16),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(30)
        )
        
        title_layout.add_widget(title_label)
        title_layout.add_widget(subtitle_label)
        header.add_widget(title_layout)
        
        # Server Status
        self.status_label = Label(
            text='🔴 الخادم متوقف',
            font_size=dp(14),
            color=(1, 0, 0, 1),
            size_hint_x=None,
            width=dp(150)
        )
        header.add_widget(self.status_label)
        
        main_layout.add_widget(header)
        
        # Stats Cards
        stats_layout = GridLayout(cols=2, spacing=dp(15), size_hint_y=None, height=dp(120))
        
        # Total Patients Card
        patients_card = self.create_stat_card("👥", "إجمالي المرضى", "0", (0.2, 0.6, 1, 1))
        stats_layout.add_widget(patients_card)
        
        # Today Appointments Card
        appointments_card = self.create_stat_card("📅", "مواعيد اليوم", "0", (0.3, 0.7, 0.3, 1))
        stats_layout.add_widget(appointments_card)
        
        main_layout.add_widget(stats_layout)
        
        # Action Buttons
        buttons_layout = GridLayout(cols=1, spacing=dp(15), size_hint_y=None, height=dp(300))
        
        # Add Patient Button
        add_patient_btn = StyledButton(
            text='➕ إضافة مريض جديد',
            font_size=dp(18),
            size_hint_y=None,
            height=dp(60)
        )
        add_patient_btn.bind(on_press=self.go_to_add_patient)
        buttons_layout.add_widget(add_patient_btn)
        
        # Patients List Button
        patients_list_btn = StyledButton(
            text='📋 قائمة المرضى',
            font_size=dp(18),
            size_hint_y=None,
            height=dp(60)
        )
        patients_list_btn.bind(on_press=self.go_to_patients_list)
        buttons_layout.add_widget(patients_list_btn)
        
        # Appointments Button
        appointments_btn = StyledButton(
            text='🗓️ إدارة المواعيد',
            font_size=dp(18),
            size_hint_y=None,
            height=dp(60)
        )
        appointments_btn.bind(on_press=self.go_to_appointments)
        buttons_layout.add_widget(appointments_btn)
        
        # Settings Button
        settings_btn = StyledButton(
            text='⚙️ الإعدادات',
            font_size=dp(18),
            size_hint_y=None,
            height=dp(60)
        )
        settings_btn.bind(on_press=self.go_to_settings)
        buttons_layout.add_widget(settings_btn)
        
        main_layout.add_widget(buttons_layout)
        
        # Footer
        footer = Label(
            text='Clinineo © 2024 - نظام إدارة العيادات المتطور',
            font_size=dp(12),
            color=(0.7, 0.7, 0.7, 1),
            size_hint_y=None,
            height=dp(30)
        )
        main_layout.add_widget(footer)
        
        self.add_widget(main_layout)
    
    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card_layout = BoxLayout(orientation='vertical', padding=dp(15))
        
        # Icon
        icon_label = Label(
            text=icon,
            font_size=dp(32),
            size_hint_y=None,
            height=dp(40)
        )
        
        # Value
        value_label = Label(
            text=value,
            font_size=dp(24),
            bold=True,
            color=color,
            size_hint_y=None,
            height=dp(30)
        )
        
        # Title
        title_label = Label(
            text=title,
            font_size=dp(14),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(20)
        )
        
        card_layout.add_widget(icon_label)
        card_layout.add_widget(value_label)
        card_layout.add_widget(title_label)
        
        # Wrap in styled card
        card = StyledCard()
        card.add_widget(card_layout)
        
        return card
    
    def go_to_add_patient(self, instance):
        """الانتقال لشاشة إضافة مريض"""
        self.manager.current = 'add_patient'
    
    def go_to_patients_list(self, instance):
        """الانتقال لشاشة قائمة المرضى"""
        self.manager.current = 'patients_list'
    
    def go_to_appointments(self, instance):
        """الانتقال لشاشة المواعيد"""
        self.manager.current = 'appointments'
    
    def go_to_settings(self, instance):
        """الانتقال لشاشة الإعدادات"""
        self.manager.current = 'settings'

class AddPatientScreen(Screen):
    """شاشة إضافة مريض"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'add_patient'
        self.build_ui()
    
    def build_ui(self):
        """بناء واجهة إضافة مريض"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))
        
        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))
        
        back_btn = Button(
            text='← رجوع',
            font_size=dp(16),
            size_hint_x=None,
            width=dp(100)
        )
        back_btn.bind(on_press=self.go_back)
        
        title = Label(
            text='➕ إضافة مريض جديد',
            font_size=dp(20),
            bold=True,
            color=(0.2, 0.6, 1, 1)
        )
        
        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(Widget())  # Spacer
        
        main_layout.add_widget(header)
        
        # Form in ScrollView
        scroll = ScrollView()
        form_layout = GridLayout(cols=1, spacing=dp(15), size_hint_y=None, padding=dp(10))
        form_layout.bind(minimum_height=form_layout.setter('height'))
        
        # Form Fields
        self.fields = {}
        
        # First Name
        self.fields['first_name'] = self.create_input_field('الاسم الأول *', 'أدخل الاسم الأول')
        form_layout.add_widget(self.fields['first_name'])
        
        # Last Name
        self.fields['last_name'] = self.create_input_field('الاسم الأخير *', 'أدخل الاسم الأخير')
        form_layout.add_widget(self.fields['last_name'])
        
        # Date of Birth
        self.fields['date_of_birth'] = self.create_input_field('تاريخ الميلاد *', 'YYYY-MM-DD')
        form_layout.add_widget(self.fields['date_of_birth'])
        
        # Gender
        gender_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(80))
        gender_label = Label(text='الجنس *', font_size=dp(16), size_hint_y=None, height=dp(30))
        self.gender_spinner = Spinner(
            text='اختر الجنس',
            values=['ذكر', 'أنثى'],
            size_hint_y=None,
            height=dp(50)
        )
        gender_layout.add_widget(gender_label)
        gender_layout.add_widget(self.gender_spinner)
        form_layout.add_widget(gender_layout)
        
        # Phone
        self.fields['phone'] = self.create_input_field('رقم الهاتف *', 'أدخل رقم الهاتف')
        form_layout.add_widget(self.fields['phone'])
        
        # Email
        self.fields['email'] = self.create_input_field('البريد الإلكتروني', 'أدخل البريد الإلكتروني')
        form_layout.add_widget(self.fields['email'])
        
        # Address
        self.fields['address'] = self.create_input_field('العنوان', 'أدخل العنوان')
        form_layout.add_widget(self.fields['address'])
        
        # Blood Type
        blood_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(80))
        blood_label = Label(text='فصيلة الدم', font_size=dp(16), size_hint_y=None, height=dp(30))
        self.blood_spinner = Spinner(
            text='اختر فصيلة الدم',
            values=['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
            size_hint_y=None,
            height=dp(50)
        )
        blood_layout.add_widget(blood_label)
        blood_layout.add_widget(self.blood_spinner)
        form_layout.add_widget(blood_layout)
        
        # Allergies
        self.fields['allergies'] = self.create_input_field('الحساسية', 'أدخل أي حساسية معروفة')
        form_layout.add_widget(self.fields['allergies'])
        
        # Save Button
        save_btn = StyledButton(
            text='💾 حفظ المريض',
            font_size=dp(18),
            size_hint_y=None,
            height=dp(60)
        )
        save_btn.bind(on_press=self.save_patient)
        form_layout.add_widget(save_btn)
        
        scroll.add_widget(form_layout)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
    
    def create_input_field(self, label_text, hint_text):
        """إنشاء حقل إدخال مخصص"""
        field_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(80))
        
        label = Label(
            text=label_text,
            font_size=dp(16),
            size_hint_y=None,
            height=dp(30),
            halign='right'
        )
        label.bind(size=label.setter('text_size'))
        
        text_input = TextInput(
            hint_text=hint_text,
            multiline=False,
            size_hint_y=None,
            height=dp(50)
        )
        
        field_layout.add_widget(label)
        field_layout.add_widget(text_input)
        
        return field_layout
    
    def save_patient(self, instance):
        """حفظ بيانات المريض"""
        # جمع البيانات
        patient_data = {}
        
        for field_name, field_widget in self.fields.items():
            text_input = field_widget.children[0]  # TextInput is the first child
            patient_data[field_name] = text_input.text.strip()
        
        # Gender and Blood Type from spinners
        patient_data['gender'] = 'male' if self.gender_spinner.text == 'ذكر' else 'female'
        patient_data['blood_type'] = self.blood_spinner.text if self.blood_spinner.text != 'اختر فصيلة الدم' else ''
        
        # Validation
        required_fields = ['first_name', 'last_name', 'date_of_birth', 'phone']
        for field in required_fields:
            if not patient_data.get(field):
                self.show_popup('خطأ', f'يرجى ملء حقل {field}')
                return
        
        if self.gender_spinner.text == 'اختر الجنس':
            self.show_popup('خطأ', 'يرجى اختيار الجنس')
            return
        
        # Save patient
        try:
            clinic_data = App.get_running_app().clinic_data
            patient_id = clinic_data.add_patient(patient_data)
            
            self.show_popup('نجح', 'تم إضافة المريض بنجاح!\nسيتم إرسال تنبيه للطبيب')
            self.clear_form()
            
        except Exception as e:
            self.show_popup('خطأ', f'فشل في حفظ البيانات: {str(e)}')
    
    def clear_form(self):
        """مسح النموذج"""
        for field_widget in self.fields.values():
            text_input = field_widget.children[0]
            text_input.text = ''
        
        self.gender_spinner.text = 'اختر الجنس'
        self.blood_spinner.text = 'اختر فصيلة الدم'
    
    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))
        
        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)
        
        popup = Popup(
            title=title,
            content=content,
            size_hint=(0.8, 0.6)
        )
        
        close_btn.bind(on_press=popup.dismiss)
        popup.open()
    
    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'home'

class PatientsListScreen(Screen):
    """شاشة قائمة المرضى"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'patients_list'
        self.build_ui()

    def build_ui(self):
        """بناء واجهة قائمة المرضى"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='📋 قائمة المرضى', font_size=dp(20), bold=True, color=(0.2, 0.6, 1, 1))

        refresh_btn = Button(text='🔄', font_size=dp(16), size_hint_x=None, width=dp(60))
        refresh_btn.bind(on_press=self.refresh_patients)

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(refresh_btn)

        main_layout.add_widget(header)

        # Patients ScrollView
        self.patients_scroll = ScrollView()
        self.patients_layout = BoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None)
        self.patients_layout.bind(minimum_height=self.patients_layout.setter('height'))

        self.patients_scroll.add_widget(self.patients_layout)
        main_layout.add_widget(self.patients_scroll)

        self.add_widget(main_layout)

        # Load patients when screen is created
        Clock.schedule_once(self.load_patients, 0.1)

    def load_patients(self, dt=None):
        """تحميل قائمة المرضى"""
        self.patients_layout.clear_widgets()

        try:
            clinic_data = App.get_running_app().clinic_data
            patients = clinic_data.data.get('patients', [])

            if patients:
                for patient in patients:
                    patient_card = self.create_patient_card(patient)
                    self.patients_layout.add_widget(patient_card)
            else:
                no_patients = Label(
                    text='لا توجد مرضى مسجلين',
                    font_size=dp(16),
                    color=(0.6, 0.6, 0.6, 1),
                    size_hint_y=None,
                    height=dp(100)
                )
                self.patients_layout.add_widget(no_patients)

        except Exception as e:
            error_label = Label(
                text=f'خطأ في تحميل البيانات: {str(e)}',
                font_size=dp(14),
                color=(1, 0, 0, 1),
                size_hint_y=None,
                height=dp(100)
            )
            self.patients_layout.add_widget(error_label)

    def create_patient_card(self, patient):
        """إنشاء بطاقة مريض"""
        card_layout = BoxLayout(orientation='horizontal', padding=dp(15), spacing=dp(15),
                               size_hint_y=None, height=dp(100))

        # Patient Avatar
        avatar = Label(
            text='👤' if patient.get('gender') == 'male' else '👩',
            font_size=dp(32),
            size_hint_x=None,
            width=dp(60)
        )

        # Patient Info
        info_layout = BoxLayout(orientation='vertical', spacing=dp(5))

        name_label = Label(
            text=f"{patient.get('first_name', '')} {patient.get('last_name', '')}",
            font_size=dp(16),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        name_label.bind(size=name_label.setter('text_size'))

        phone_label = Label(
            text=f"📞 {patient.get('phone', '')}",
            font_size=dp(14),
            color=(0.4, 0.4, 0.4, 1),
            size_hint_y=None,
            height=dp(20),
            halign='right'
        )
        phone_label.bind(size=phone_label.setter('text_size'))

        date_label = Label(
            text=f"📅 {patient.get('created_at', '')[:10]}",
            font_size=dp(12),
            color=(0.6, 0.6, 0.6, 1),
            size_hint_y=None,
            height=dp(20),
            halign='right'
        )
        date_label.bind(size=date_label.setter('text_size'))

        info_layout.add_widget(name_label)
        info_layout.add_widget(phone_label)
        info_layout.add_widget(date_label)

        card_layout.add_widget(avatar)
        card_layout.add_widget(info_layout)

        # Wrap in styled card
        card = StyledCard()
        card.size_hint_y = None
        card.height = dp(100)
        card.add_widget(card_layout)

        return card

    def refresh_patients(self, instance):
        """تحديث قائمة المرضى"""
        self.load_patients()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'home'

class SettingsScreen(Screen):
    """شاشة الإعدادات"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'settings'
        self.build_ui()

    def build_ui(self):
        """بناء واجهة الإعدادات"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))

        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))

        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)

        title = Label(text='⚙️ الإعدادات', font_size=dp(20), bold=True, color=(0.2, 0.6, 1, 1))

        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(Widget())  # Spacer

        main_layout.add_widget(header)

        # Settings Options
        settings_layout = BoxLayout(orientation='vertical', spacing=dp(15))

        # Server Info
        server_card = self.create_setting_card(
            "🌐 معلومات الخادم",
            "عرض معلومات الاتصال",
            self.show_server_info
        )
        settings_layout.add_widget(server_card)

        # Backup Data
        backup_card = self.create_setting_card(
            "💾 نسخ احتياطي",
            "حفظ نسخة من البيانات",
            self.backup_data
        )
        settings_layout.add_widget(backup_card)

        # Clear Data
        clear_card = self.create_setting_card(
            "🗑️ مسح البيانات",
            "حذف جميع البيانات",
            self.clear_data
        )
        settings_layout.add_widget(clear_card)

        # About
        about_card = self.create_setting_card(
            "ℹ️ حول التطبيق",
            "معلومات عن Clinineo",
            self.show_about
        )
        settings_layout.add_widget(about_card)

        main_layout.add_widget(settings_layout)
        self.add_widget(main_layout)

    def create_setting_card(self, title, description, callback):
        """إنشاء بطاقة إعداد"""
        card_layout = BoxLayout(orientation='horizontal', padding=dp(15), spacing=dp(15))

        # Info
        info_layout = BoxLayout(orientation='vertical')

        title_label = Label(
            text=title,
            font_size=dp(16),
            bold=True,
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(30),
            halign='right'
        )
        title_label.bind(size=title_label.setter('text_size'))

        desc_label = Label(
            text=description,
            font_size=dp(14),
            color=(0.5, 0.5, 0.5, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        desc_label.bind(size=desc_label.setter('text_size'))

        info_layout.add_widget(title_label)
        info_layout.add_widget(desc_label)

        # Action Button
        action_btn = Button(
            text='→',
            font_size=dp(18),
            size_hint_x=None,
            width=dp(60)
        )
        action_btn.bind(on_press=callback)

        card_layout.add_widget(info_layout)
        card_layout.add_widget(action_btn)

        # Wrap in styled card
        card = StyledCard()
        card.size_hint_y = None
        card.height = dp(80)
        card.add_widget(card_layout)

        return card

    def show_server_info(self, instance):
        """عرض معلومات الخادم"""
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)

            content = BoxLayout(orientation='vertical', spacing=dp(10))
            content.add_widget(Label(text=f'اسم الجهاز: {hostname}', font_size=dp(14)))
            content.add_widget(Label(text=f'عنوان IP: {local_ip}', font_size=dp(14)))
            content.add_widget(Label(text='المنفذ: 8080', font_size=dp(14)))
            content.add_widget(Label(text=f'رابط الخادم: http://{local_ip}:8080', font_size=dp(12)))

            close_btn = Button(text='إغلاق', size_hint_y=None, height=dp(50))
            content.add_widget(close_btn)

            popup = Popup(title='معلومات الخادم', content=content, size_hint=(0.8, 0.6))
            close_btn.bind(on_press=popup.dismiss)
            popup.open()

        except Exception as e:
            self.show_popup('خطأ', f'فشل في الحصول على معلومات الخادم: {str(e)}')

    def backup_data(self, instance):
        """نسخ احتياطي للبيانات"""
        try:
            clinic_data = App.get_running_app().clinic_data
            backup_file = f"clinineo_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(clinic_data.data, f, ensure_ascii=False, indent=2)

            self.show_popup('نجح', f'تم إنشاء نسخة احتياطية: {backup_file}')

        except Exception as e:
            self.show_popup('خطأ', f'فشل في إنشاء النسخة الاحتياطية: {str(e)}')

    def clear_data(self, instance):
        """مسح البيانات"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text='هل أنت متأكد من حذف جميع البيانات؟', font_size=dp(16)))
        content.add_widget(Label(text='هذا الإجراء لا يمكن التراجع عنه!', font_size=dp(14), color=(1, 0, 0, 1)))

        buttons_layout = BoxLayout(spacing=dp(10), size_hint_y=None, height=dp(50))

        confirm_btn = Button(text='نعم، احذف', background_color=(1, 0, 0, 1))
        cancel_btn = Button(text='إلغاء')

        buttons_layout.add_widget(confirm_btn)
        buttons_layout.add_widget(cancel_btn)
        content.add_widget(buttons_layout)

        popup = Popup(title='تأكيد الحذف', content=content, size_hint=(0.8, 0.5))

        def confirm_clear(instance):
            try:
                clinic_data = App.get_running_app().clinic_data
                clinic_data.data = {
                    "patients": [],
                    "appointments": [],
                    "notifications": [],
                    "stats": {"total_patients": 0, "today_appointments": 0},
                    "last_updated": datetime.now().isoformat()
                }
                clinic_data.save_data()
                popup.dismiss()
                self.show_popup('نجح', 'تم حذف جميع البيانات')
            except Exception as e:
                popup.dismiss()
                self.show_popup('خطأ', f'فشل في حذف البيانات: {str(e)}')

        confirm_btn.bind(on_press=confirm_clear)
        cancel_btn.bind(on_press=popup.dismiss)
        popup.open()

    def show_about(self, instance):
        """عرض معلومات التطبيق"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text='🏥 Clinineo', font_size=dp(24), bold=True))
        content.add_widget(Label(text='نظام إدارة العيادات المتطور', font_size=dp(16)))
        content.add_widget(Label(text='الإصدار: 1.0.0', font_size=dp(14)))
        content.add_widget(Label(text='© 2024 Clinineo Team', font_size=dp(12)))

        close_btn = Button(text='إغلاق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title='حول التطبيق', content=content, size_hint=(0.8, 0.6))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))

        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title=title, content=content, size_hint=(0.8, 0.5))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'home'

class CliniceoRegistrarApp(App):
    """التطبيق الرئيسي لموظف الاستقبال"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.clinic_data = ClinicData()
        self.server = None
        self.server_thread = None
    
    def build(self):
        """بناء التطبيق"""
        self.title = 'Clinineo - الاستقبال'
        
        # Screen Manager
        sm = ScreenManager()
        
        # Add Screens
        sm.add_widget(HomeScreen())
        sm.add_widget(AddPatientScreen())
        sm.add_widget(PatientsListScreen())
        sm.add_widget(SettingsScreen())
        
        # Start server
        Clock.schedule_once(self.start_server, 1)
        
        return sm
    
    def start_server(self, dt):
        """بدء الخادم المدمج"""
        try:
            from http.server import HTTPServer, BaseHTTPRequestHandler
            import json

            class ClinicAPIHandler(BaseHTTPRequestHandler):
                def do_GET(self):
                    if self.path == '/api/health':
                        self.send_json_response({"status": "healthy", "message": "خادم Clinineo يعمل"})
                    elif self.path == '/api/patients':
                        patients = App.get_running_app().clinic_data.data.get('patients', [])
                        self.send_json_response({"success": True, "data": patients})
                    elif self.path == '/api/notifications':
                        notifications = App.get_running_app().clinic_data.data.get('notifications', [])
                        self.send_json_response({"success": True, "data": notifications})
                    else:
                        self.send_error(404)

                def send_json_response(self, data):
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    json_data = json.dumps(data, ensure_ascii=False)
                    self.wfile.write(json_data.encode('utf-8'))

                def log_message(self, format, *args):
                    pass  # Silent logging

            self.server = HTTPServer(('', 8080), ClinicAPIHandler)

            def run_server():
                self.server.serve_forever()

            self.server_thread = threading.Thread(target=run_server, daemon=True)
            self.server_thread.start()

            # Update status in home screen
            home_screen = self.root.get_screen('home')
            home_screen.status_label.text = '🟢 الخادم يعمل'
            home_screen.status_label.color = (0, 1, 0, 1)

        except Exception as e:
            print(f"خطأ في تشغيل الخادم: {e}")

if __name__ == '__main__':
    CliniceoRegistrarApp().run()
