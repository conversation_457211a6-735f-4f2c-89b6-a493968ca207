"""
Medical records management routes
مسارات إدارة السجلات الطبية
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ...core.database import get_db, Medical<PERSON><PERSON><PERSON>, Patient, User, Appointment
from ...core.security import get_current_active_user, require_doctor_or_admin, require_any_role
from ...schemas.medical_record import MedicalR<PERSON>ord<PERSON><PERSON>, MedicalRecordUpdate, MedicalRecordResponse

router = APIRouter()

@router.post("/", response_model=MedicalRecordResponse)
async def create_medical_record(
    record: MedicalRecordCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_doctor_or_admin)
):
    """Create a new medical record"""
    # Check if patient exists
    patient = db.query(Patient).filter(Patient.id == record.patient_id).first()
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )
    
    # Check if appointment exists (if provided)
    if record.appointment_id:
        appointment = db.query(Appointment).filter(Appointment.id == record.appointment_id).first()
        if not appointment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Appointment not found"
            )
    
    # Create medical record
    db_record = MedicalRecord(
        **record.dict(),
        doctor_id=current_user.id
    )
    db.add(db_record)
    db.commit()
    db.refresh(db_record)
    
    # Update appointment status if provided
    if record.appointment_id:
        appointment = db.query(Appointment).filter(Appointment.id == record.appointment_id).first()
        if appointment:
            appointment.status = "completed"
            db.commit()
    
    return MedicalRecordResponse.from_orm(db_record)

@router.get("/", response_model=List[MedicalRecordResponse])
async def get_medical_records(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    patient_id: Optional[str] = Query(None),
    doctor_id: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """Get medical records with filtering"""
    query = db.query(MedicalRecord)
    
    # Apply filters
    if patient_id:
        query = query.filter(MedicalRecord.patient_id == patient_id)
    
    if doctor_id:
        query = query.filter(MedicalRecord.doctor_id == doctor_id)
    
    # If user is a doctor, only show their records
    if current_user.role == "doctor":
        query = query.filter(MedicalRecord.doctor_id == current_user.id)
    
    # Apply pagination and ordering
    records = query.order_by(MedicalRecord.created_at.desc()).offset(skip).limit(limit).all()
    
    return [MedicalRecordResponse.from_orm(record) for record in records]

@router.get("/{record_id}", response_model=MedicalRecordResponse)
async def get_medical_record(
    record_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """Get medical record by ID"""
    query = db.query(MedicalRecord).filter(MedicalRecord.id == record_id)
    
    # If user is a doctor, only show their records
    if current_user.role == "doctor":
        query = query.filter(MedicalRecord.doctor_id == current_user.id)
    
    record = query.first()
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Medical record not found"
        )
    
    return MedicalRecordResponse.from_orm(record)

@router.put("/{record_id}", response_model=MedicalRecordResponse)
async def update_medical_record(
    record_id: str,
    record_update: MedicalRecordUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_doctor_or_admin)
):
    """Update medical record"""
    query = db.query(MedicalRecord).filter(MedicalRecord.id == record_id)
    
    # If user is a doctor, only allow updating their own records
    if current_user.role == "doctor":
        query = query.filter(MedicalRecord.doctor_id == current_user.id)
    
    record = query.first()
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Medical record not found"
        )
    
    # Update record fields
    update_data = record_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(record, field, value)
    
    db.commit()
    db.refresh(record)
    
    return MedicalRecordResponse.from_orm(record)

@router.delete("/{record_id}")
async def delete_medical_record(
    record_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_doctor_or_admin)
):
    """Delete medical record"""
    query = db.query(MedicalRecord).filter(MedicalRecord.id == record_id)
    
    # If user is a doctor, only allow deleting their own records
    if current_user.role == "doctor":
        query = query.filter(MedicalRecord.doctor_id == current_user.id)
    
    record = query.first()
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Medical record not found"
        )
    
    db.delete(record)
    db.commit()
    
    return {"message": "Medical record deleted successfully"}
