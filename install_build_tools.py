#!/usr/bin/env python3
"""
Build Tools Installer for Clinineo APK
مثبت أدوات البناء لـ APK تطبيقات Clinineo
"""

import os
import sys
import subprocess
import platform

def print_banner():
    """طباعة شعار المثبت"""
    print("=" * 60)
    print("🛠️  Clinineo Build Tools Installer")
    print("=" * 60)
    print("📦 تثبيت أدوات بناء APK")
    print("🔧 إعداد بيئة التطوير")
    print("=" * 60)
    print()

def detect_system():
    """اكتشاف نظام التشغيل"""
    system = platform.system().lower()
    print(f"🖥️  نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version}")
    print()
    return system

def install_python_packages():
    """تثبيت حزم Python المطلوبة"""
    print("📦 تثبيت حزم Python...")
    
    packages = [
        'buildozer',
        'cython',
        'virtualenv',
        'kivy[base]',
        'requests'
    ]
    
    for package in packages:
        print(f"📥 تثبيت {package}...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', package], 
                         check=True, capture_output=True)
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت {package}: {e}")
            return False
    
    print("✅ تم تثبيت جميع حزم Python")
    print()
    return True

def install_linux_dependencies():
    """تثبيت اعتماديات Linux"""
    print("🐧 تثبيت اعتماديات Linux...")
    
    # فحص إذا كان المستخدم لديه صلاحيات sudo
    try:
        subprocess.run(['sudo', '-n', 'true'], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("⚠️  تحتاج صلاحيات sudo لتثبيت اعتماديات النظام")
        print("💡 شغل الأمر: sudo python3 install_build_tools.py")
        return False
    
    # تحديث قائمة الحزم
    print("🔄 تحديث قائمة الحزم...")
    try:
        subprocess.run(['sudo', 'apt', 'update'], check=True, capture_output=True)
        print("✅ تم تحديث قائمة الحزم")
    except subprocess.CalledProcessError:
        print("❌ فشل في تحديث قائمة الحزم")
        return False
    
    # قائمة الحزم المطلوبة
    packages = [
        'git',
        'zip',
        'unzip',
        'openjdk-8-jdk',
        'python3-pip',
        'autoconf',
        'libtool',
        'pkg-config',
        'zlib1g-dev',
        'libncurses5-dev',
        'libncursesw5-dev',
        'libtinfo5',
        'cmake',
        'libffi-dev',
        'libssl-dev',
        'build-essential',
        'ccache'
    ]
    
    print("📦 تثبيت حزم النظام...")
    try:
        cmd = ['sudo', 'apt', 'install', '-y'] + packages
        subprocess.run(cmd, check=True)
        print("✅ تم تثبيت جميع حزم النظام")
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت حزم النظام: {e}")
        return False
    
    # تعيين JAVA_HOME
    java_home = "/usr/lib/jvm/java-8-openjdk-amd64"
    if os.path.exists(java_home):
        print(f"☕ تعيين JAVA_HOME: {java_home}")
        os.environ['JAVA_HOME'] = java_home
        
        # إضافة إلى bashrc
        bashrc_line = f'export JAVA_HOME={java_home}'
        bashrc_path = os.path.expanduser('~/.bashrc')
        
        try:
            with open(bashrc_path, 'r') as f:
                content = f.read()
            
            if bashrc_line not in content:
                with open(bashrc_path, 'a') as f:
                    f.write(f'\n# Java Home for Android development\n{bashrc_line}\n')
                print("✅ تم إضافة JAVA_HOME إلى ~/.bashrc")
        except:
            print("⚠️  لم يتم إضافة JAVA_HOME إلى ~/.bashrc")
    
    print("✅ تم إعداد بيئة Linux")
    print()
    return True

def install_windows_dependencies():
    """تثبيت اعتماديات Windows"""
    print("🪟 إعداد بيئة Windows...")
    
    print("⚠️  لبناء APK على Windows، يُنصح بشدة باستخدام:")
    print("1. 🐧 WSL2 (Windows Subsystem for Linux)")
    print("2. 🐳 Docker")
    print("3. ☁️  خدمة بناء سحابية")
    print()
    
    print("📋 خطوات إعداد WSL2:")
    print("1. فعّل WSL2 في Windows Features")
    print("2. ثبت Ubuntu من Microsoft Store")
    print("3. شغل هذا السكريبت داخل Ubuntu")
    print()
    
    return True

def install_macos_dependencies():
    """تثبيت اعتماديات macOS"""
    print("🍎 إعداد بيئة macOS...")
    
    # فحص Homebrew
    try:
        subprocess.run(['brew', '--version'], check=True, capture_output=True)
        print("✅ Homebrew متوفر")
    except FileNotFoundError:
        print("❌ Homebrew غير مثبت")
        print("💡 ثبت Homebrew من: https://brew.sh")
        return False
    
    # تثبيت الحزم المطلوبة
    packages = ['git', 'python3', 'autoconf', 'libtool', 'pkg-config', 'cmake']
    
    for package in packages:
        print(f"📥 تثبيت {package}...")
        try:
            subprocess.run(['brew', 'install', package], check=True, capture_output=True)
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"⚠️  {package} قد يكون مثبت مسبقاً")
    
    print("✅ تم إعداد بيئة macOS")
    print()
    return True

def verify_installation():
    """التحقق من التثبيت"""
    print("🔍 التحقق من التثبيت...")
    
    # فحص buildozer
    try:
        result = subprocess.run(['buildozer', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Buildozer يعمل بشكل صحيح")
        else:
            print("❌ مشكلة في Buildozer")
            return False
    except FileNotFoundError:
        print("❌ Buildozer غير موجود في PATH")
        return False
    
    # فحص Python packages
    required_packages = ['kivy', 'requests', 'cython']
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} متوفر")
        except ImportError:
            print(f"❌ {package} غير متوفر")
            return False
    
    print("✅ جميع الأدوات جاهزة!")
    print()
    return True

def show_next_steps():
    """عرض الخطوات التالية"""
    print("🎯 الخطوات التالية:")
    print("=" * 40)
    print("1. 🏗️  شغل أداة البناء:")
    print("   python build_apk.py")
    print()
    print("2. ⏳ انتظر انتهاء البناء (15-30 دقيقة)")
    print()
    print("3. 📱 ستجد ملفات APK في مجلد apk_output/")
    print()
    print("4. 📲 انسخ الملفات إلى هاتفك وثبتها")
    print()
    print("💡 نصائح:")
    print("- تأكد من اتصال إنترنت مستقر")
    print("- احرص على وجود مساحة كافية (5+ GB)")
    print("- البناء الأول يستغرق وقت أطول")
    print("=" * 40)

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    system = detect_system()
    
    # تثبيت حزم Python
    if not install_python_packages():
        print("❌ فشل في تثبيت حزم Python")
        return
    
    # تثبيت اعتماديات النظام
    if system == 'linux':
        if not install_linux_dependencies():
            print("❌ فشل في إعداد بيئة Linux")
            return
    elif system == 'windows':
        install_windows_dependencies()
        print("⚠️  يُنصح بإكمال الإعداد في WSL2")
        return
    elif system == 'darwin':  # macOS
        if not install_macos_dependencies():
            print("❌ فشل في إعداد بيئة macOS")
            return
    else:
        print(f"⚠️  نظام التشغيل {system} غير مدعوم بشكل كامل")
        print("💡 جرب استخدام Linux أو WSL2")
    
    # التحقق من التثبيت
    if verify_installation():
        print("🎉 تم إعداد بيئة البناء بنجاح!")
        show_next_steps()
    else:
        print("❌ هناك مشاكل في الإعداد")
        print("💡 راجع الأخطاء أعلاه وحاول مرة أخرى")

if __name__ == "__main__":
    main()
