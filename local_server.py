#!/usr/bin/env python3
"""
Local Clinineo Server - No Internet Required
خادم Clinineo المحلي - لا يحتاج إنترنت
"""

import sqlite3
import json
from datetime import datetime, date
import socket
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import os

class ClinicDatabase:
    """قاعدة بيانات العيادة المحلية"""
    
    def __init__(self, db_path="clinic.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                role TEXT NOT NULL,
                phone TEXT,
                specialization TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المرضى
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                date_of_birth DATE NOT NULL,
                gender TEXT NOT NULL,
                phone TEXT NOT NULL,
                email TEXT,
                address TEXT,
                emergency_contact TEXT,
                blood_type TEXT,
                allergies TEXT,
                medical_history TEXT,
                insurance_number TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المواعيد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS appointments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER NOT NULL,
                doctor_id INTEGER NOT NULL,
                appointment_date DATE NOT NULL,
                appointment_time TIME NOT NULL,
                type TEXT NOT NULL,
                status TEXT DEFAULT 'scheduled',
                notes TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id),
                FOREIGN KEY (doctor_id) REFERENCES users (id)
            )
        ''')
        
        # جدول السجلات الطبية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS medical_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER NOT NULL,
                appointment_id INTEGER,
                doctor_id INTEGER NOT NULL,
                diagnosis TEXT NOT NULL,
                symptoms TEXT,
                treatment TEXT NOT NULL,
                medications TEXT,
                lab_tests TEXT,
                follow_up_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id),
                FOREIGN KEY (doctor_id) REFERENCES users (id)
            )
        ''')
        
        # جدول التنبيهات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                type TEXT DEFAULT 'info',
                is_read INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # إدراج مستخدم افتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO users (email, password, first_name, last_name, role)
            VALUES ('<EMAIL>', 'admin123', 'مدير', 'النظام', 'admin')
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO users (email, password, first_name, last_name, role, specialization)
            VALUES ('<EMAIL>', 'doctor123', 'د. أحمد', 'محمد', 'doctor', 'طب عام')
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO users (email, password, first_name, last_name, role)
            VALUES ('<EMAIL>', 'registrar123', 'سارة', 'أحمد', 'registrar')
        ''')
        
        conn.commit()
        conn.close()
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
    
    def execute_query(self, query, params=None, fetch=False):
        """تنفيذ استعلام قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch:
                result = cursor.fetchall()
                return [dict(row) for row in result]
            else:
                conn.commit()
                return cursor.lastrowid
        finally:
            conn.close()

class ClinicServer(BaseHTTPRequestHandler):
    """خادم العيادة المحلي"""
    
    def __init__(self, *args, **kwargs):
        self.db = ClinicDatabase()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """معالجة طلبات GET"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            response = {
                "message": "مرحباً بك في نظام Clinineo المحلي",
                "status": "working",
                "server_ip": self.get_server_ip()
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        
        elif path == '/health':
            self.send_health_response()
        
        elif path == '/api/patients':
            self.get_patients()
        
        elif path == '/api/appointments':
            self.get_appointments()
        
        elif path == '/api/users/doctors':
            self.get_doctors()
        
        elif path == '/api/reports/dashboard':
            self.get_dashboard_stats()
        
        else:
            self.send_error(404, "Not Found")
    
    def do_POST(self):
        """معالجة طلبات POST"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # قراءة البيانات
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
        except:
            data = {}
        
        if path == '/api/auth/login':
            self.handle_login(data)
        
        elif path == '/api/patients':
            self.create_patient(data)
        
        elif path == '/api/appointments':
            self.create_appointment(data)
        
        elif path == '/api/medical-records':
            self.create_medical_record(data)
        
        else:
            self.send_error(404, "Not Found")
    
    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def send_health_response(self):
        """إرسال استجابة فحص الصحة"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        response = {
            "status": "healthy",
            "message": "الخادم المحلي يعمل بشكل طبيعي",
            "timestamp": datetime.now().isoformat()
        }
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def handle_login(self, data):
        """معالجة تسجيل الدخول"""
        email = data.get('email', '')
        password = data.get('password', '')
        
        users = self.db.execute_query(
            "SELECT * FROM users WHERE email = ? AND password = ? AND is_active = 1",
            (email, password),
            fetch=True
        )
        
        if users:
            user = users[0]
            response = {
                "success": True,
                "access_token": f"local-token-{user['id']}",
                "token_type": "bearer",
                "user": {
                    "id": user['id'],
                    "email": user['email'],
                    "first_name": user['first_name'],
                    "last_name": user['last_name'],
                    "role": user['role'],
                    "specialization": user['specialization']
                }
            }
        else:
            response = {
                "success": False,
                "message": "بيانات تسجيل الدخول غير صحيحة"
            }
        
        self.send_json_response(response)
    
    def get_patients(self):
        """الحصول على قائمة المرضى"""
        patients = self.db.execute_query(
            "SELECT * FROM patients ORDER BY created_at DESC LIMIT 50",
            fetch=True
        )
        self.send_json_response({"success": True, "data": patients})
    
    def create_patient(self, data):
        """إنشاء مريض جديد"""
        try:
            patient_id = self.db.execute_query('''
                INSERT INTO patients (first_name, last_name, date_of_birth, gender, phone, 
                                    email, address, emergency_contact, blood_type, allergies, 
                                    medical_history, insurance_number, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data.get('first_name'), data.get('last_name'), data.get('date_of_birth'),
                data.get('gender'), data.get('phone'), data.get('email'),
                data.get('address'), data.get('emergency_contact'), data.get('blood_type'),
                data.get('allergies'), data.get('medical_history'), data.get('insurance_number'),
                1  # created_by
            ))
            
            # إرسال تنبيه للأطباء
            self.notify_doctors(f"تم تسجيل مريض جديد: {data.get('first_name')} {data.get('last_name')}")
            
            response = {"success": True, "message": "تم إضافة المريض بنجاح", "patient_id": patient_id}
        except Exception as e:
            response = {"success": False, "message": f"خطأ في إضافة المريض: {str(e)}"}
        
        self.send_json_response(response)
    
    def get_doctors(self):
        """الحصول على قائمة الأطباء"""
        doctors = self.db.execute_query(
            "SELECT id, first_name, last_name, specialization FROM users WHERE role = 'doctor' AND is_active = 1",
            fetch=True
        )
        self.send_json_response({"success": True, "data": doctors})
    
    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة المعلومات"""
        today = date.today().isoformat()
        
        # إجمالي المرضى
        total_patients = self.db.execute_query("SELECT COUNT(*) as count FROM patients", fetch=True)[0]['count']
        
        # مواعيد اليوم
        today_appointments = self.db.execute_query(
            "SELECT COUNT(*) as count FROM appointments WHERE appointment_date = ?",
            (today,), fetch=True
        )[0]['count']
        
        # الأطباء النشطين
        active_doctors = self.db.execute_query(
            "SELECT COUNT(*) as count FROM users WHERE role = 'doctor' AND is_active = 1",
            fetch=True
        )[0]['count']
        
        stats = {
            "total_patients": total_patients,
            "today_appointments": today_appointments,
            "active_doctors": active_doctors,
            "pending_appointments": 0,
            "completed_appointments": 0,
            "month_appointments": 0
        }
        
        self.send_json_response({"success": True, "data": stats})
    
    def notify_doctors(self, message):
        """إرسال تنبيه للأطباء"""
        doctors = self.db.execute_query(
            "SELECT id FROM users WHERE role = 'doctor' AND is_active = 1",
            fetch=True
        )
        
        for doctor in doctors:
            self.db.execute_query('''
                INSERT INTO notifications (user_id, title, message, type)
                VALUES (?, ?, ?, ?)
            ''', (doctor['id'], "مريض جديد", message, "info"))
    
    def send_json_response(self, data):
        """إرسال استجابة JSON"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
    
    def get_server_ip(self):
        """الحصول على عنوان IP للخادم"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"

def start_server():
    """بدء تشغيل الخادم"""
    server_address = ('', 8080)  # استخدام منفذ 8080
    httpd = HTTPServer(server_address, ClinicServer)
    
    # الحصول على عنوان IP
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    
    print("🏥 Clinineo Local Server")
    print("=" * 50)
    print(f"✅ الخادم يعمل على:")
    print(f"   📍 المحلي: http://127.0.0.1:8080")
    print(f"   🌐 الشبكة: http://{local_ip}:8080")
    print(f"   🔍 فحص الصحة: http://{local_ip}:8080/health")
    print("=" * 50)
    print("💡 استخدم عنوان الشبكة للوصول من أجهزة أخرى")
    print("🛑 اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
        httpd.shutdown()

if __name__ == "__main__":
    start_server()
