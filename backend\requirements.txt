# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
sqlite3

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Environment variables
python-dotenv==1.0.0

# HTTP client
httpx==0.25.2
requests==2.31.0

# WebSockets for real-time notifications
websockets==12.0

# File handling
aiofiles==23.2.1
pillow==10.1.0

# Data validation
pydantic==2.5.0
email-validator==2.1.0

# Date and time
python-dateutil==2.8.2

# CORS
python-cors==1.7.0

# Logging
loguru==0.7.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
