const express = require('express');
const multer = require('multer');
const { createClient } = require('@supabase/supabase-js');
const { authenticateToken, anyAuthenticated } = require('../middleware/auth');

const router = express.Router();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif,application/pdf').split(',');
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('File type not allowed'), false);
    }
  }
});

// @route   POST /api/files/upload
// @desc    Upload file
// @access  Private
router.post('/upload', authenticateToken, anyAuthenticated, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file provided'
      });
    }

    const { patientId, recordId, type, description } = req.body;

    if (!patientId) {
      return res.status(400).json({
        success: false,
        message: 'Patient ID is required'
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `${timestamp}-${req.file.originalname}`;
    const filePath = `medical-files/${patientId}/${filename}`;

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('medical-files')
      .upload(filePath, req.file.buffer, {
        contentType: req.file.mimetype,
        upsert: false
      });

    if (uploadError) {
      console.error('File upload error:', uploadError);
      return res.status(500).json({
        success: false,
        message: 'Failed to upload file'
      });
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('medical-files')
      .getPublicUrl(filePath);

    // Save file record to database
    const fileRecord = {
      patient_id: patientId,
      medical_record_id: recordId || null,
      filename: req.file.originalname,
      file_path: filePath,
      file_url: urlData.publicUrl,
      file_type: req.file.mimetype,
      file_size: req.file.size,
      type: type || 'other',
      description: description || null,
      uploaded_by: req.user.id,
      created_at: new Date().toISOString()
    };

    const { data: newFile, error: dbError } = await supabase
      .from('medical_files')
      .insert([fileRecord])
      .select(`
        *,
        patients:patient_id(id, first_name, last_name),
        uploaded_by_user:uploaded_by(id, first_name, last_name)
      `)
      .single();

    if (dbError) {
      console.error('Database error:', dbError);
      // Try to delete uploaded file if database insert fails
      await supabase.storage.from('medical-files').remove([filePath]);
      return res.status(500).json({
        success: false,
        message: 'Failed to save file record'
      });
    }

    res.status(201).json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        file: {
          id: newFile.id,
          patientId: newFile.patient_id,
          recordId: newFile.medical_record_id,
          filename: newFile.filename,
          fileUrl: newFile.file_url,
          fileType: newFile.file_type,
          fileSize: newFile.file_size,
          type: newFile.type,
          description: newFile.description,
          uploadedBy: newFile.uploaded_by_user ? {
            id: newFile.uploaded_by_user.id,
            firstName: newFile.uploaded_by_user.first_name,
            lastName: newFile.uploaded_by_user.last_name
          } : null,
          createdAt: newFile.created_at
        }
      }
    });
  } catch (error) {
    console.error('Upload file error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error'
    });
  }
});

// @route   GET /api/files
// @desc    Get files with filters
// @access  Private
router.get('/', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const patientId = req.query.patientId;
    const recordId = req.query.recordId;
    const type = req.query.type;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('medical_files')
      .select(`
        *,
        patients:patient_id(id, first_name, last_name),
        uploaded_by_user:uploaded_by(id, first_name, last_name)
      `, { count: 'exact' });

    // Apply filters
    if (patientId) {
      query = query.eq('patient_id', patientId);
    }
    if (recordId) {
      query = query.eq('medical_record_id', recordId);
    }
    if (type) {
      query = query.eq('type', type);
    }

    // Add pagination and ordering
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: files, error, count } = await query;

    if (error) {
      console.error('Get files error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch files'
      });
    }

    // Transform data
    const transformedFiles = files.map(file => ({
      id: file.id,
      patientId: file.patient_id,
      recordId: file.medical_record_id,
      filename: file.filename,
      fileUrl: file.file_url,
      fileType: file.file_type,
      fileSize: file.file_size,
      type: file.type,
      description: file.description,
      createdAt: file.created_at,
      patient: file.patients ? {
        id: file.patients.id,
        firstName: file.patients.first_name,
        lastName: file.patients.last_name
      } : null,
      uploadedBy: file.uploaded_by_user ? {
        id: file.uploaded_by_user.id,
        firstName: file.uploaded_by_user.first_name,
        lastName: file.uploaded_by_user.last_name
      } : null
    }));

    res.json({
      success: true,
      data: {
        files: transformedFiles,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: limit
        }
      }
    });
  } catch (error) {
    console.error('Get files error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/files/:id
// @desc    Delete file
// @access  Private
router.delete('/:id', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    // Get file record
    const { data: file, error: getError } = await supabase
      .from('medical_files')
      .select('*')
      .eq('id', req.params.id)
      .single();

    if (getError || !file) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    // Check permissions (only uploader or admin can delete)
    if (req.user.role !== 'admin' && file.uploaded_by !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    // Delete from storage
    const { error: storageError } = await supabase.storage
      .from('medical-files')
      .remove([file.file_path]);

    if (storageError) {
      console.error('Storage delete error:', storageError);
    }

    // Delete from database
    const { error: dbError } = await supabase
      .from('medical_files')
      .delete()
      .eq('id', req.params.id);

    if (dbError) {
      console.error('Database delete error:', dbError);
      return res.status(500).json({
        success: false,
        message: 'Failed to delete file record'
      });
    }

    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
