"""
Login window for user authentication
نافذة تسجيل الدخول للمصادقة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading

class LoginWindow:
    """Login window class"""
    
    def __init__(self, parent, auth_service, on_success_callback):
        self.parent = parent
        self.auth_service = auth_service
        self.on_success_callback = on_success_callback
        self.window = None
        
    def show(self):
        """Show login window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("تسجيل الدخول - Clinineo")
        self.window.geometry("400x300")
        self.window.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Make it modal
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Create UI
        self.create_widgets()
        
        # Focus on email field
        self.email_entry.focus()
        
        # Bind Enter key to login
        self.window.bind('<Return>', lambda e: self.login())
    
    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """Create login form widgets"""
        # Main frame
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="نظام إدارة العيادة", 
            font=('Arial', 18, 'bold')
        )
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk.Label(
            main_frame, 
            text="تسجيل الدخول", 
            font=('Arial', 12)
        )
        subtitle_label.pack(pady=(0, 20))
        
        # Email field
        ttk.Label(main_frame, text="البريد الإلكتروني:").pack(anchor=tk.W, pady=(0, 5))
        self.email_entry = ttk.Entry(main_frame, width=30, font=('Arial', 10))
        self.email_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Password field
        ttk.Label(main_frame, text="كلمة المرور:").pack(anchor=tk.W, pady=(0, 5))
        self.password_entry = ttk.Entry(main_frame, width=30, show="*", font=('Arial', 10))
        self.password_entry.pack(fill=tk.X, pady=(0, 20))
        
        # Login button
        self.login_button = ttk.Button(
            main_frame, 
            text="تسجيل الدخول", 
            command=self.login,
            width=20
        )
        self.login_button.pack(pady=(0, 10))
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="", foreground="red")
        self.status_label.pack(pady=(10, 0))
        
        # Default credentials info
        info_frame = ttk.LabelFrame(main_frame, text="بيانات تجريبية", padding="10")
        info_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Label(info_frame, text="البريد: <EMAIL>", font=('Arial', 9)).pack(anchor=tk.W)
        ttk.Label(info_frame, text="كلمة المرور: admin123", font=('Arial', 9)).pack(anchor=tk.W)
        
        # Fill default credentials button
        ttk.Button(
            info_frame, 
            text="استخدام البيانات التجريبية", 
            command=self.fill_default_credentials
        ).pack(pady=(5, 0))
    
    def fill_default_credentials(self):
        """Fill default credentials for testing"""
        self.email_entry.delete(0, tk.END)
        self.email_entry.insert(0, "<EMAIL>")
        self.password_entry.delete(0, tk.END)
        self.password_entry.insert(0, "admin123")
    
    def login(self):
        """Handle login button click"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        
        # Validate input
        if not email:
            self.show_error("يرجى إدخال البريد الإلكتروني")
            self.email_entry.focus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # Disable login button and show loading
        self.login_button.config(state='disabled', text="جاري تسجيل الدخول...")
        self.status_label.config(text="جاري التحقق من البيانات...", foreground="blue")
        
        # Perform login in background thread
        threading.Thread(target=self._perform_login, args=(email, password), daemon=True).start()
    
    def _perform_login(self, email, password):
        """Perform login in background thread"""
        try:
            response = self.auth_service.login(email, password)
            
            # Update UI in main thread
            self.window.after(0, self._handle_login_response, response)
            
        except Exception as e:
            self.window.after(0, self._handle_login_error, str(e))
    
    def _handle_login_response(self, response):
        """Handle login response in main thread"""
        # Re-enable login button
        self.login_button.config(state='normal', text="تسجيل الدخول")
        
        if response.success:
            # Login successful
            user_data = self.auth_service.current_user
            token = self.auth_service.current_token
            
            # Close login window
            self.window.destroy()
            
            # Call success callback
            self.on_success_callback(user_data, token)
            
        else:
            # Login failed
            self.show_error(response.message or "فشل في تسجيل الدخول")
            self.password_entry.delete(0, tk.END)
            self.password_entry.focus()
    
    def _handle_login_error(self, error_message):
        """Handle login error in main thread"""
        self.login_button.config(state='normal', text="تسجيل الدخول")
        self.show_error(f"خطأ في الاتصال: {error_message}")
    
    def show_error(self, message):
        """Show error message"""
        self.status_label.config(text=message, foreground="red")
        
        # Clear error message after 5 seconds
        self.window.after(5000, lambda: self.status_label.config(text=""))
