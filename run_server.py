#!/usr/bin/env python3
"""
Direct launcher for Local Server
مشغل مباشر للخادم المحلي
"""

import sys
import os

print("🚀 تشغيل الخادم المحلي...")

# التحقق من وجود الملف
if not os.path.exists('local_server.py'):
    print("❌ ملف local_server.py غير موجود")
    input("اضغط Enter للخروج...")
    sys.exit(1)

try:
    # تشغيل الخادم المحلي
    exec(open('local_server.py').read())
except Exception as e:
    print(f"❌ خطأ في تشغيل الخادم: {e}")
    input("اضغط Enter للخروج...")
