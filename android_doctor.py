#!/usr/bin/env python3
"""
Android Doctor App using Kivy
تطبيق الطبيب للأندرويد باستخدام Kivy
"""

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
import requests
import json
from datetime import datetime

class DoctorApp(App):
    """تطبيق الطبيب للأندرويد"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.server_url = "http://192.168.1.100:8080"  # عنوان الخادم
        self.current_user = None
        self.access_token = None
        self.last_notification_count = 0
    
    def build(self):
        """بناء واجهة التطبيق"""
        self.title = "Clinineo - الطبيب"
        
        # التخطيط الرئيسي
        main_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # شريط العنوان
        title_label = Label(
            text='نظام إدارة العيادة - الطبيب',
            size_hint_y=None,
            height=50,
            font_size=20
        )
        main_layout.add_widget(title_label)
        
        # شريط الحالة والتنبيهات
        status_layout = BoxLayout(size_hint_y=None, height=30)
        
        self.status_label = Label(
            text='غير متصل بالخادم',
            color=(1, 0, 0, 1)  # أحمر
        )
        status_layout.add_widget(self.status_label)
        
        self.notification_label = Label(
            text='',
            color=(0, 0, 1, 1)  # أزرق
        )
        status_layout.add_widget(self.notification_label)
        
        main_layout.add_widget(status_layout)
        
        # منطقة المحتوى
        self.content_layout = BoxLayout(orientation='vertical', spacing=10)
        main_layout.add_widget(self.content_layout)
        
        # عرض شاشة تسجيل الدخول
        self.show_login_screen()
        
        # فحص الاتصال بالخادم
        Clock.schedule_once(self.check_server_connection, 1)
        
        return main_layout
    
    def show_login_screen(self):
        """عرض شاشة تسجيل الدخول"""
        self.content_layout.clear_widgets()
        
        login_layout = GridLayout(cols=1, spacing=10, size_hint_y=None)
        login_layout.bind(minimum_height=login_layout.setter('height'))
        
        # حقول تسجيل الدخول
        login_layout.add_widget(Label(text='تسجيل الدخول', font_size=18))
        
        self.email_input = TextInput(
            hint_text='البريد الإلكتروني',
            text='<EMAIL>',
            multiline=False,
            size_hint_y=None,
            height=40
        )
        login_layout.add_widget(self.email_input)
        
        self.password_input = TextInput(
            hint_text='كلمة المرور',
            text='doctor123',
            password=True,
            multiline=False,
            size_hint_y=None,
            height=40
        )
        login_layout.add_widget(self.password_input)
        
        # أزرار
        buttons_layout = GridLayout(cols=2, spacing=10, size_hint_y=None, height=50)
        
        login_btn = Button(text='تسجيل دخول')
        login_btn.bind(on_press=self.login)
        buttons_layout.add_widget(login_btn)
        
        settings_btn = Button(text='إعدادات الخادم')
        settings_btn.bind(on_press=self.show_server_settings)
        buttons_layout.add_widget(settings_btn)
        
        login_layout.add_widget(buttons_layout)
        
        # إضافة إلى التمرير
        scroll = ScrollView()
        scroll.add_widget(login_layout)
        self.content_layout.add_widget(scroll)
    
    def show_server_settings(self, instance):
        """عرض إعدادات الخادم"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        content.add_widget(Label(text='عنوان الخادم:', size_hint_y=None, height=30))
        
        server_input = TextInput(
            text=self.server_url,
            multiline=False,
            size_hint_y=None,
            height=40
        )
        content.add_widget(server_input)
        
        buttons_layout = BoxLayout(spacing=10, size_hint_y=None, height=50)
        
        save_btn = Button(text='حفظ')
        save_btn.bind(on_press=lambda x: self.save_server_settings(server_input.text, popup))
        buttons_layout.add_widget(save_btn)
        
        cancel_btn = Button(text='إلغاء')
        cancel_btn.bind(on_press=lambda x: popup.dismiss())
        buttons_layout.add_widget(cancel_btn)
        
        content.add_widget(buttons_layout)
        
        popup = Popup(
            title='إعدادات الخادم',
            content=content,
            size_hint=(0.8, 0.6)
        )
        popup.open()
    
    def save_server_settings(self, server_url, popup):
        """حفظ إعدادات الخادم"""
        self.server_url = server_url.strip()
        popup.dismiss()
        self.show_popup('تم حفظ إعدادات الخادم')
        Clock.schedule_once(self.check_server_connection, 0.5)
    
    def check_server_connection(self, dt=None):
        """فحص الاتصال بالخادم"""
        try:
            response = requests.get(f"{self.server_url}/api/health", timeout=3)
            if response.status_code == 200:
                self.status_label.text = f'متصل بالخادم'
                self.status_label.color = (0, 1, 0, 1)  # أخضر
                
                # فحص التنبيهات إذا كان المستخدم مسجل دخول
                if self.current_user:
                    self.check_notifications()
            else:
                self.status_label.text = 'خطأ في الاتصال بالخادم'
                self.status_label.color = (1, 0, 0, 1)  # أحمر
        except:
            self.status_label.text = 'غير متصل بالخادم'
            self.status_label.color = (1, 0, 0, 1)  # أحمر
    
    def check_notifications(self):
        """فحص التنبيهات الجديدة"""
        if not self.current_user:
            return
        
        try:
            response = requests.get(
                f"{self.server_url}/api/notifications?user_id={self.current_user['id']}&unread_only=true",
                timeout=3
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    notifications = data.get("data", [])
                    count = len(notifications)
                    
                    if count > 0:
                        self.notification_label.text = f'🔔 {count} تنبيه جديد'
                        self.notification_label.color = (1, 0, 0, 1)  # أحمر
                        
                        # إذا كان هناك تنبيهات جديدة، أظهر رسالة
                        if count > self.last_notification_count:
                            self.show_notification_popup(notifications[0])
                    else:
                        self.notification_label.text = 'لا توجد تنبيهات جديدة'
                        self.notification_label.color = (0, 1, 0, 1)  # أخضر
                    
                    self.last_notification_count = count
                    
        except Exception as e:
            print(f"خطأ في فحص التنبيهات: {e}")
    
    def show_notification_popup(self, notification):
        """عرض تنبيه منبثق"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        content.add_widget(Label(text=notification.get('title', ''), font_size=16))
        content.add_widget(Label(text=notification.get('message', '')))
        
        buttons_layout = BoxLayout(spacing=10, size_hint_y=None, height=50)
        
        read_btn = Button(text='تمييز كمقروء')
        read_btn.bind(on_press=lambda x: self.mark_notification_read(notification['id'], popup))
        buttons_layout.add_widget(read_btn)
        
        close_btn = Button(text='إغلاق')
        close_btn.bind(on_press=lambda x: popup.dismiss())
        buttons_layout.add_widget(close_btn)
        
        content.add_widget(buttons_layout)
        
        popup = Popup(
            title='تنبيه جديد',
            content=content,
            size_hint=(0.8, 0.6)
        )
        popup.open()
    
    def mark_notification_read(self, notification_id, popup):
        """تمييز التنبيه كمقروء"""
        try:
            response = requests.post(
                f"{self.server_url}/api/notifications/read",
                json={"notification_id": notification_id},
                timeout=3
            )
            
            if response.status_code == 200:
                popup.dismiss()
                self.check_notifications()
                
        except Exception as e:
            print(f"خطأ في تمييز التنبيه: {e}")
    
    def login(self, instance):
        """تسجيل الدخول"""
        email = self.email_input.text.strip()
        password = self.password_input.text.strip()
        
        if not email or not password:
            self.show_popup('يرجى ملء جميع الحقول')
            return
        
        try:
            response = requests.post(
                f"{self.server_url}/api/auth/login",
                json={"email": email, "password": password},
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    user = data["user"]
                    if user.get("role") == "doctor":
                        self.current_user = user
                        self.access_token = data["access_token"]
                        self.show_main_screen()
                        # بدء فحص التنبيهات الدوري
                        Clock.schedule_interval(self.check_notifications, 10)  # كل 10 ثواني
                    else:
                        self.show_popup("هذا التطبيق مخصص للأطباء فقط")
                else:
                    self.show_popup(data.get("message", "فشل تسجيل الدخول"))
            else:
                self.show_popup("خطأ في الاتصال بالخادم")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def show_main_screen(self):
        """عرض الشاشة الرئيسية"""
        self.content_layout.clear_widgets()
        
        main_layout = GridLayout(cols=1, spacing=10, size_hint_y=None)
        main_layout.bind(minimum_height=main_layout.setter('height'))
        
        # ترحيب
        welcome_text = f"مرحباً د. {self.current_user['first_name']} {self.current_user['last_name']}"
        if self.current_user.get('specialization'):
            welcome_text += f"\n{self.current_user['specialization']}"
        
        main_layout.add_widget(Label(text=welcome_text, font_size=16))
        
        # الأزرار الرئيسية
        notifications_btn = Button(
            text='التنبيهات الجديدة',
            size_hint_y=None,
            height=60
        )
        notifications_btn.bind(on_press=self.show_notifications)
        main_layout.add_widget(notifications_btn)
        
        patients_btn = Button(
            text='قائمة المرضى',
            size_hint_y=None,
            height=60
        )
        patients_btn.bind(on_press=self.show_patients_list)
        main_layout.add_widget(patients_btn)
        
        dashboard_btn = Button(
            text='لوحة المعلومات',
            size_hint_y=None,
            height=60
        )
        dashboard_btn.bind(on_press=self.show_dashboard)
        main_layout.add_widget(dashboard_btn)
        
        refresh_btn = Button(
            text='تحديث البيانات',
            size_hint_y=None,
            height=60
        )
        refresh_btn.bind(on_press=self.refresh_data)
        main_layout.add_widget(refresh_btn)
        
        logout_btn = Button(
            text='تسجيل خروج',
            size_hint_y=None,
            height=60
        )
        logout_btn.bind(on_press=self.logout)
        main_layout.add_widget(logout_btn)
        
        # إضافة إلى التمرير
        scroll = ScrollView()
        scroll.add_widget(main_layout)
        self.content_layout.add_widget(scroll)
    
    def show_notifications(self, instance):
        """عرض التنبيهات"""
        try:
            response = requests.get(
                f"{self.server_url}/api/notifications?user_id={self.current_user['id']}",
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    notifications = data.get("data", [])
                    self.display_notifications_popup(notifications)
                else:
                    self.show_popup(data.get("message", "فشل في تحميل التنبيهات"))
            else:
                self.show_popup("خطأ في الاتصال بالخادم")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def display_notifications_popup(self, notifications):
        """عرض التنبيهات في نافذة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        notifications_layout = GridLayout(cols=1, spacing=5, size_hint_y=None)
        notifications_layout.bind(minimum_height=notifications_layout.setter('height'))
        
        notifications_layout.add_widget(Label(text=f'التنبيهات ({len(notifications)})', font_size=16, size_hint_y=None, height=40))
        
        if notifications:
            for notification in notifications[:10]:  # عرض أول 10 تنبيهات
                status = "✅" if notification.get('is_read') else "🔔"
                time_str = notification.get('created_at', '')[:16].replace('T', ' ')
                notification_text = f"{status} {time_str}\n{notification.get('title', '')}\n{notification.get('message', '')}"
                
                notification_label = Label(
                    text=notification_text,
                    size_hint_y=None,
                    height=80,
                    text_size=(None, None)
                )
                notifications_layout.add_widget(notification_label)
        else:
            notifications_layout.add_widget(Label(text='لا توجد تنبيهات', size_hint_y=None, height=40))
        
        scroll = ScrollView()
        scroll.add_widget(notifications_layout)
        content.add_widget(scroll)
        
        close_btn = Button(text='إغلاق', size_hint_y=None, height=50)
        close_btn.bind(on_press=lambda x: popup.dismiss())
        content.add_widget(close_btn)
        
        popup = Popup(
            title='التنبيهات',
            content=content,
            size_hint=(0.9, 0.8)
        )
        popup.open()
    
    def show_patients_list(self, instance):
        """عرض قائمة المرضى"""
        try:
            response = requests.get(f"{self.server_url}/api/patients", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    patients = data.get("data", [])
                    self.display_patients_popup(patients)
                else:
                    self.show_popup(data.get("message", "فشل في تحميل المرضى"))
            else:
                self.show_popup("خطأ في الاتصال بالخادم")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def display_patients_popup(self, patients):
        """عرض قائمة المرضى في نافذة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        patients_layout = GridLayout(cols=1, spacing=5, size_hint_y=None)
        patients_layout.bind(minimum_height=patients_layout.setter('height'))
        
        patients_layout.add_widget(Label(text=f'قائمة المرضى ({len(patients)})', font_size=16, size_hint_y=None, height=40))
        
        for patient in patients[:20]:  # عرض أول 20 مريض
            patient_text = f"{patient.get('first_name', '')} {patient.get('last_name', '')} - {patient.get('phone', '')}"
            patients_layout.add_widget(Label(text=patient_text, size_hint_y=None, height=30))
        
        scroll = ScrollView()
        scroll.add_widget(patients_layout)
        content.add_widget(scroll)
        
        close_btn = Button(text='إغلاق', size_hint_y=None, height=50)
        close_btn.bind(on_press=lambda x: popup.dismiss())
        content.add_widget(close_btn)
        
        popup = Popup(
            title='قائمة المرضى',
            content=content,
            size_hint=(0.9, 0.8)
        )
        popup.open()
    
    def show_dashboard(self, instance):
        """عرض لوحة المعلومات"""
        try:
            response = requests.get(f"{self.server_url}/api/dashboard", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    stats = data.get("data", {})
                    self.display_dashboard_popup(stats)
                else:
                    self.show_popup(data.get("message", "فشل في تحميل الإحصائيات"))
            else:
                self.show_popup("خطأ في الاتصال بالخادم")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def display_dashboard_popup(self, stats):
        """عرض لوحة المعلومات في نافذة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        stats_layout = GridLayout(cols=1, spacing=10, size_hint_y=None)
        stats_layout.bind(minimum_height=stats_layout.setter('height'))
        
        stats_layout.add_widget(Label(text='لوحة المعلومات', font_size=18, size_hint_y=None, height=40))
        
        stats_layout.add_widget(Label(text=f"إجمالي المرضى: {stats.get('total_patients', 0)}", size_hint_y=None, height=30))
        stats_layout.add_widget(Label(text=f"الأطباء النشطين: {stats.get('active_doctors', 0)}", size_hint_y=None, height=30))
        stats_layout.add_widget(Label(text=f"التنبيهات غير المقروءة: {stats.get('unread_notifications', 0)}", size_hint_y=None, height=30))
        stats_layout.add_widget(Label(text=f"حالة الخادم: {stats.get('server_status', 'غير معروف')}", size_hint_y=None, height=30))
        
        content.add_widget(stats_layout)
        
        close_btn = Button(text='إغلاق', size_hint_y=None, height=50)
        close_btn.bind(on_press=lambda x: popup.dismiss())
        content.add_widget(close_btn)
        
        popup = Popup(
            title='لوحة المعلومات',
            content=content,
            size_hint=(0.8, 0.6)
        )
        popup.open()
    
    def refresh_data(self, instance):
        """تحديث البيانات"""
        self.check_server_connection()
        if self.current_user:
            self.check_notifications()
        self.show_popup('تم تحديث البيانات')
    
    def logout(self, instance):
        """تسجيل الخروج"""
        Clock.unschedule(self.check_notifications)  # إيقاف فحص التنبيهات
        self.current_user = None
        self.access_token = None
        self.last_notification_count = 0
        self.notification_label.text = ''
        self.show_login_screen()
    
    def show_popup(self, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        content.add_widget(Label(text=message))
        
        close_btn = Button(text='موافق', size_hint_y=None, height=50)
        content.add_widget(close_btn)
        
        popup = Popup(
            title='رسالة',
            content=content,
            size_hint=(0.8, 0.4)
        )
        
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

if __name__ == '__main__':
    DoctorApp().run()
