"""
Database configuration and models
إعدادات قاعدة البيانات والنماذج
"""

from sqlalchemy import create_engine, Column, String, Integer, DateTime, Boolean, Text, Date, Time, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.sql import func
from datetime import datetime, date, time
import uuid

from .config import settings

# Create database engine
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Database Models

class User(Base):
    """User model - المستخدمين"""
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, unique=True, nullable=False, index=True)
    password = Column(String, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    role = Column(String, nullable=False)  # admin, doctor, registrar
    phone = Column(String)
    specialization = Column(String)  # For doctors
    is_active = Column(Boolean, default=True)
    last_login = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    created_patients = relationship("Patient", back_populates="created_by_user")
    appointments_as_doctor = relationship("Appointment", back_populates="doctor")
    medical_records = relationship("MedicalRecord", back_populates="doctor")
    uploaded_files = relationship("MedicalFile", back_populates="uploaded_by_user")
    notifications = relationship("Notification", back_populates="user")

class Patient(Base):
    """Patient model - المرضى"""
    __tablename__ = "patients"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    date_of_birth = Column(Date, nullable=False)
    gender = Column(String, nullable=False)  # male, female
    phone = Column(String, nullable=False, index=True)
    email = Column(String)
    address = Column(Text)
    emergency_contact = Column(String)
    blood_type = Column(String)
    allergies = Column(Text)
    medical_history = Column(Text)
    insurance_number = Column(String)
    created_by = Column(String, ForeignKey("users.id"))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    created_by_user = relationship("User", back_populates="created_patients")
    appointments = relationship("Appointment", back_populates="patient")
    medical_records = relationship("MedicalRecord", back_populates="patient")
    medical_files = relationship("MedicalFile", back_populates="patient")

class Appointment(Base):
    """Appointment model - المواعيد"""
    __tablename__ = "appointments"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    patient_id = Column(String, ForeignKey("patients.id"), nullable=False)
    doctor_id = Column(String, ForeignKey("users.id"), nullable=False)
    appointment_date = Column(Date, nullable=False)
    appointment_time = Column(Time, nullable=False)
    type = Column(String, nullable=False)  # consultation, follow_up, emergency, checkup
    status = Column(String, default="scheduled")  # scheduled, confirmed, in_progress, completed, cancelled
    notes = Column(Text)
    created_by = Column(String, ForeignKey("users.id"))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    patient = relationship("Patient", back_populates="appointments")
    doctor = relationship("User", back_populates="appointments_as_doctor")
    medical_records = relationship("MedicalRecord", back_populates="appointment")

class MedicalRecord(Base):
    """Medical record model - السجلات الطبية"""
    __tablename__ = "medical_records"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    patient_id = Column(String, ForeignKey("patients.id"), nullable=False)
    appointment_id = Column(String, ForeignKey("appointments.id"))
    doctor_id = Column(String, ForeignKey("users.id"), nullable=False)
    diagnosis = Column(Text, nullable=False)
    symptoms = Column(Text)
    treatment = Column(Text, nullable=False)
    medications = Column(JSON)  # List of medication objects
    lab_tests = Column(JSON)  # List of lab test names
    follow_up_date = Column(Date)
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    patient = relationship("Patient", back_populates="medical_records")
    appointment = relationship("Appointment", back_populates="medical_records")
    doctor = relationship("User", back_populates="medical_records")
    medical_files = relationship("MedicalFile", back_populates="medical_record")

class MedicalFile(Base):
    """Medical file model - الملفات الطبية"""
    __tablename__ = "medical_files"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    patient_id = Column(String, ForeignKey("patients.id"), nullable=False)
    medical_record_id = Column(String, ForeignKey("medical_records.id"))
    filename = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    file_type = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    type = Column(String, default="other")  # xray, lab_result, prescription, report, other
    description = Column(Text)
    uploaded_by = Column(String, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    patient = relationship("Patient", back_populates="medical_files")
    medical_record = relationship("MedicalRecord", back_populates="medical_files")
    uploaded_by_user = relationship("User", back_populates="uploaded_files")

class Notification(Base):
    """Notification model - التنبيهات"""
    __tablename__ = "notifications"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    title = Column(String, nullable=False)
    message = Column(Text, nullable=False)
    type = Column(String, default="info")  # info, success, warning, error
    data = Column(JSON)  # Additional data
    is_read = Column(Boolean, default=False)
    read_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="notifications")

async def create_tables():
    """Create all database tables"""
    Base.metadata.create_all(bind=engine)
    
    # Create default admin user if not exists
    db = SessionLocal()
    try:
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not admin_user:
            from .security import get_password_hash
            admin_user = User(
                email="<EMAIL>",
                password=get_password_hash("admin123"),
                first_name="System",
                last_name="Admin",
                role="admin",
                is_active=True
            )
            db.add(admin_user)
            db.commit()
            print("✅ Default admin user created: <EMAIL> / admin123")
    finally:
        db.close()
