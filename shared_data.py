#!/usr/bin/env python3
"""
Shared Data Manager - No Server Required
مدير البيانات المشتركة - بدون خادم
"""

import json
import os
import time
from datetime import datetime, date
import threading
import uuid

class SharedDataManager:
    """مدير البيانات المشتركة"""
    
    def __init__(self, data_file="clinic_data.json"):
        self.data_file = data_file
        self.lock = threading.Lock()
        self.init_data_file()
    
    def init_data_file(self):
        """إنشاء ملف البيانات إذا لم يكن موجوداً"""
        if not os.path.exists(self.data_file):
            initial_data = {
                "users": [
                    {
                        "id": "1",
                        "email": "<EMAIL>",
                        "password": "admin123",
                        "first_name": "مدير",
                        "last_name": "النظام",
                        "role": "admin",
                        "is_active": True,
                        "created_at": datetime.now().isoformat()
                    },
                    {
                        "id": "2",
                        "email": "<EMAIL>",
                        "password": "doctor123",
                        "first_name": "د. أحمد",
                        "last_name": "محمد",
                        "role": "doctor",
                        "specialization": "طب عام",
                        "is_active": True,
                        "created_at": datetime.now().isoformat()
                    },
                    {
                        "id": "3",
                        "email": "<EMAIL>",
                        "password": "registrar123",
                        "first_name": "سارة",
                        "last_name": "أحمد",
                        "role": "registrar",
                        "is_active": True,
                        "created_at": datetime.now().isoformat()
                    }
                ],
                "patients": [],
                "appointments": [],
                "medical_records": [],
                "notifications": [],
                "last_updated": datetime.now().isoformat()
            }
            self.save_data(initial_data)
            print("✅ تم إنشاء ملف البيانات المشتركة")
    
    def load_data(self):
        """تحميل البيانات من الملف"""
        try:
            with self.lock:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            return None
    
    def save_data(self, data):
        """حفظ البيانات في الملف"""
        try:
            with self.lock:
                data["last_updated"] = datetime.now().isoformat()
                with open(self.data_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                return True
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
            return False
    
    def authenticate_user(self, email, password):
        """تسجيل دخول المستخدم"""
        data = self.load_data()
        if not data:
            return None
        
        for user in data.get("users", []):
            if user.get("email") == email and user.get("password") == password:
                if user.get("is_active", True):
                    return user
        return None
    
    def add_patient(self, patient_data, created_by_id):
        """إضافة مريض جديد"""
        data = self.load_data()
        if not data:
            return False
        
        # إنشاء معرف فريد
        patient_id = str(uuid.uuid4())[:8]
        
        patient = {
            "id": patient_id,
            "first_name": patient_data.get("first_name", ""),
            "last_name": patient_data.get("last_name", ""),
            "date_of_birth": patient_data.get("date_of_birth", ""),
            "gender": patient_data.get("gender", ""),
            "phone": patient_data.get("phone", ""),
            "email": patient_data.get("email", ""),
            "address": patient_data.get("address", ""),
            "emergency_contact": patient_data.get("emergency_contact", ""),
            "blood_type": patient_data.get("blood_type", ""),
            "allergies": patient_data.get("allergies", ""),
            "medical_history": patient_data.get("medical_history", ""),
            "insurance_number": patient_data.get("insurance_number", ""),
            "created_by": created_by_id,
            "created_at": datetime.now().isoformat()
        }
        
        data["patients"].append(patient)
        
        # إضافة تنبيه للأطباء
        self.add_notification_for_doctors(
            data,
            "مريض جديد",
            f"تم تسجيل مريض جديد: {patient['first_name']} {patient['last_name']}"
        )
        
        return self.save_data(data)
    
    def get_patients(self, limit=50):
        """الحصول على قائمة المرضى"""
        data = self.load_data()
        if not data:
            return []
        
        patients = data.get("patients", [])
        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        patients.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        return patients[:limit]
    
    def add_appointment(self, appointment_data, created_by_id):
        """إضافة موعد جديد"""
        data = self.load_data()
        if not data:
            return False
        
        appointment_id = str(uuid.uuid4())[:8]
        
        appointment = {
            "id": appointment_id,
            "patient_id": appointment_data.get("patient_id", ""),
            "doctor_id": appointment_data.get("doctor_id", ""),
            "appointment_date": appointment_data.get("appointment_date", ""),
            "appointment_time": appointment_data.get("appointment_time", ""),
            "type": appointment_data.get("type", "consultation"),
            "status": appointment_data.get("status", "scheduled"),
            "notes": appointment_data.get("notes", ""),
            "created_by": created_by_id,
            "created_at": datetime.now().isoformat()
        }
        
        data["appointments"].append(appointment)
        
        # إضافة تنبيه للطبيب المحدد
        doctor_id = appointment_data.get("doctor_id")
        if doctor_id:
            self.add_notification_for_user(
                data,
                doctor_id,
                "موعد جديد",
                f"تم حجز موعد جديد في {appointment['appointment_date']} الساعة {appointment['appointment_time']}"
            )
        
        return self.save_data(data)
    
    def get_appointments(self, doctor_id=None, date_filter=None):
        """الحصول على المواعيد"""
        data = self.load_data()
        if not data:
            return []
        
        appointments = data.get("appointments", [])
        
        # تصفية حسب الطبيب
        if doctor_id:
            appointments = [a for a in appointments if a.get("doctor_id") == doctor_id]
        
        # تصفية حسب التاريخ
        if date_filter:
            appointments = [a for a in appointments if a.get("appointment_date") == date_filter]
        
        return appointments
    
    def add_notification_for_doctors(self, data, title, message):
        """إضافة تنبيه لجميع الأطباء"""
        doctors = [u for u in data.get("users", []) if u.get("role") == "doctor" and u.get("is_active")]
        
        for doctor in doctors:
            self.add_notification_for_user(data, doctor["id"], title, message)
    
    def add_notification_for_user(self, data, user_id, title, message):
        """إضافة تنبيه لمستخدم محدد"""
        notification = {
            "id": str(uuid.uuid4())[:8],
            "user_id": user_id,
            "title": title,
            "message": message,
            "type": "info",
            "is_read": False,
            "created_at": datetime.now().isoformat()
        }
        
        data["notifications"].append(notification)
    
    def get_notifications(self, user_id, unread_only=False):
        """الحصول على تنبيهات المستخدم"""
        data = self.load_data()
        if not data:
            return []
        
        notifications = data.get("notifications", [])
        user_notifications = [n for n in notifications if n.get("user_id") == user_id]
        
        if unread_only:
            user_notifications = [n for n in user_notifications if not n.get("is_read", False)]
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        user_notifications.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        return user_notifications
    
    def mark_notification_read(self, notification_id):
        """تمييز التنبيه كمقروء"""
        data = self.load_data()
        if not data:
            return False
        
        for notification in data.get("notifications", []):
            if notification.get("id") == notification_id:
                notification["is_read"] = True
                notification["read_at"] = datetime.now().isoformat()
                break
        
        return self.save_data(data)
    
    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة المعلومات"""
        data = self.load_data()
        if not data:
            return {}
        
        today = date.today().isoformat()
        
        total_patients = len(data.get("patients", []))
        today_appointments = len([a for a in data.get("appointments", []) 
                                if a.get("appointment_date") == today])
        active_doctors = len([u for u in data.get("users", []) 
                            if u.get("role") == "doctor" and u.get("is_active")])
        
        return {
            "total_patients": total_patients,
            "today_appointments": today_appointments,
            "active_doctors": active_doctors,
            "pending_appointments": 0,
            "completed_appointments": 0,
            "month_appointments": 0
        }
    
    def get_doctors(self):
        """الحصول على قائمة الأطباء"""
        data = self.load_data()
        if not data:
            return []
        
        doctors = [u for u in data.get("users", []) 
                  if u.get("role") == "doctor" and u.get("is_active")]
        return doctors

# إنشاء مثيل مشترك
shared_data = SharedDataManager()

# دوال مساعدة للاستخدام المباشر
def authenticate_user(email, password):
    return shared_data.authenticate_user(email, password)

def add_patient(patient_data, created_by_id):
    return shared_data.add_patient(patient_data, created_by_id)

def get_patients(limit=50):
    return shared_data.get_patients(limit)

def get_dashboard_stats():
    return shared_data.get_dashboard_stats()

def get_notifications(user_id, unread_only=False):
    return shared_data.get_notifications(user_id, unread_only)

def get_doctors():
    return shared_data.get_doctors()
