{"name": "clinineo-backend", "version": "1.0.0", "description": "Backend API for Clinineo clinic management system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["clinic", "management", "api", "healthcare"], "author": "Clinineo Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.39.0", "joi": "^17.11.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}