"""
Application settings and configuration
إعدادات وتكوين التطبيق
"""

import os
from typing import Optional

class Settings:
    """Application settings"""
    
    def __init__(self):
        # API Configuration
        self.API_BASE_URL = os.getenv("API_BASE_URL", "http://127.0.0.1:8000/api")
        self.API_TIMEOUT = int(os.getenv("API_TIMEOUT", "30"))
        
        # Application Configuration
        self.APP_NAME = "Clinineo"
        self.APP_VERSION = "1.0.0"
        self.APP_TITLE = "Clinineo - نظام إدارة العيادة"
        
        # Window Configuration
        self.WINDOW_WIDTH = 1200
        self.WINDOW_HEIGHT = 800
        self.MIN_WIDTH = 800
        self.MIN_HEIGHT = 600
        
        # Theme Configuration
        self.THEME = "clam"
        self.PRIMARY_COLOR = "#2196F3"
        self.SUCCESS_COLOR = "#4CAF50"
        self.ERROR_COLOR = "#F44336"
        self.WARNING_COLOR = "#FF9800"
        
        # Font Configuration
        self.FONT_FAMILY = "Arial"
        self.FONT_SIZE_SMALL = 9
        self.FONT_SIZE_NORMAL = 10
        self.FONT_SIZE_LARGE = 12
        self.FONT_SIZE_TITLE = 16
        
        # Data Configuration
        self.PAGE_SIZE = 10
        self.MAX_PAGE_SIZE = 100
        
        # File Configuration
        self.UPLOAD_DIR = "uploads"
        self.MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
        self.ALLOWED_FILE_TYPES = [
            ".jpg", ".jpeg", ".png", ".gif", ".pdf", 
            ".doc", ".docx", ".xls", ".xlsx"
        ]
        
        # Cache Configuration
        self.CACHE_ENABLED = True
        self.CACHE_TIMEOUT = 300  # 5 minutes
        
        # Notification Configuration
        self.ENABLE_NOTIFICATIONS = True
        self.NOTIFICATION_TIMEOUT = 5000  # 5 seconds
        
        # Auto-refresh Configuration
        self.AUTO_REFRESH_ENABLED = True
        self.AUTO_REFRESH_INTERVAL = 30000  # 30 seconds
        
        # Language Configuration
        self.LANGUAGE = "ar"
        self.RTL_SUPPORT = True
        
        # Debug Configuration
        self.DEBUG = os.getenv("DEBUG", "false").lower() == "true"
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        
        # Create directories
        self.create_directories()
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            self.UPLOAD_DIR,
            "logs",
            "cache",
            "exports"
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
    
    def get_api_url(self, endpoint: str) -> str:
        """Get full API URL for endpoint"""
        return f"{self.API_BASE_URL}{endpoint}"
    
    def get_font(self, size: Optional[int] = None, bold: bool = False) -> tuple:
        """Get font configuration"""
        font_size = size or self.FONT_SIZE_NORMAL
        weight = "bold" if bold else "normal"
        return (self.FONT_FAMILY, font_size, weight)
    
    def get_title_font(self) -> tuple:
        """Get title font configuration"""
        return self.get_font(self.FONT_SIZE_TITLE, bold=True)
    
    def get_heading_font(self) -> tuple:
        """Get heading font configuration"""
        return self.get_font(self.FONT_SIZE_LARGE, bold=True)
    
    def is_valid_file_type(self, filename: str) -> bool:
        """Check if file type is allowed"""
        _, ext = os.path.splitext(filename.lower())
        return ext in self.ALLOWED_FILE_TYPES
    
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
