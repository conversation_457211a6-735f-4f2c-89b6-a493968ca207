#!/usr/bin/env python3
"""
Network Shared Data Manager - For Multiple Devices
مدير البيانات المشتركة عبر الشبكة - لأجهزة متعددة
"""

import json
import os
import time
from datetime import datetime, date
import threading
import uuid
import socket
from pathlib import Path

class NetworkSharedDataManager:
    """مدير البيانات المشتركة عبر الشبكة"""
    
    def __init__(self, shared_folder=None):
        # تحديد مجلد البيانات المشتركة
        if shared_folder:
            self.shared_folder = Path(shared_folder)
        else:
            # البحث عن مجلد مشترك تلقائياً
            self.shared_folder = self.find_shared_folder()
        
        self.data_file = self.shared_folder / "clinic_data.json"
        self.lock_file = self.shared_folder / "clinic_data.lock"
        self.device_id = self.get_device_id()
        
        # إنشاء المجلد إذا لم يكن موجوداً
        self.shared_folder.mkdir(parents=True, exist_ok=True)
        
        self.init_data_file()
        self.register_device()
    
    def find_shared_folder(self):
        """البحث عن مجلد مشترك مناسب"""
        possible_paths = [
            # مجلدات Windows المشتركة
            Path("C:/ClinicData"),
            Path("D:/ClinicData"),
            Path("//SERVER/ClinicData"),  # مجلد شبكة
            
            # مجلد محلي كبديل
            Path.cwd() / "shared_clinic_data",
            
            # مجلد المستخدم
            Path.home() / "Documents" / "ClinicData"
        ]
        
        for path in possible_paths:
            try:
                # محاولة إنشاء المجلد للاختبار
                path.mkdir(parents=True, exist_ok=True)
                # اختبار الكتابة
                test_file = path / "test_write.tmp"
                test_file.write_text("test")
                test_file.unlink()
                print(f"✅ استخدام مجلد البيانات: {path}")
                return path
            except:
                continue
        
        # استخدام المجلد الحالي كبديل أخير
        fallback = Path.cwd()
        print(f"⚠️ استخدام المجلد الحالي: {fallback}")
        return fallback
    
    def get_device_id(self):
        """الحصول على معرف فريد للجهاز"""
        try:
            hostname = socket.gethostname()
            ip = socket.gethostbyname(hostname)
            return f"{hostname}_{ip}"
        except:
            return f"device_{uuid.uuid4().hex[:8]}"
    
    def acquire_lock(self, timeout=5):
        """الحصول على قفل الملف"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                if not self.lock_file.exists():
                    self.lock_file.write_text(f"{self.device_id}_{time.time()}")
                    return True
                else:
                    # فحص عمر القفل
                    lock_content = self.lock_file.read_text()
                    if "_" in lock_content:
                        lock_time = float(lock_content.split("_")[-1])
                        if time.time() - lock_time > 10:  # قفل قديم
                            self.lock_file.unlink()
                            continue
            except:
                pass
            time.sleep(0.1)
        return False
    
    def release_lock(self):
        """تحرير قفل الملف"""
        try:
            if self.lock_file.exists():
                self.lock_file.unlink()
        except:
            pass
    
    def init_data_file(self):
        """إنشاء ملف البيانات إذا لم يكن موجوداً"""
        if not self.data_file.exists():
            if self.acquire_lock():
                try:
                    initial_data = {
                        "users": [
                            {
                                "id": "1",
                                "email": "<EMAIL>",
                                "password": "admin123",
                                "first_name": "مدير",
                                "last_name": "النظام",
                                "role": "admin",
                                "is_active": True,
                                "created_at": datetime.now().isoformat()
                            },
                            {
                                "id": "2",
                                "email": "<EMAIL>",
                                "password": "doctor123",
                                "first_name": "د. أحمد",
                                "last_name": "محمد",
                                "role": "doctor",
                                "specialization": "طب عام",
                                "is_active": True,
                                "created_at": datetime.now().isoformat()
                            },
                            {
                                "id": "3",
                                "email": "<EMAIL>",
                                "password": "registrar123",
                                "first_name": "سارة",
                                "last_name": "أحمد",
                                "role": "registrar",
                                "is_active": True,
                                "created_at": datetime.now().isoformat()
                            }
                        ],
                        "patients": [],
                        "appointments": [],
                        "medical_records": [],
                        "notifications": [],
                        "devices": {},
                        "last_updated": datetime.now().isoformat(),
                        "created_by_device": self.device_id
                    }
                    self.save_data(initial_data)
                    print("✅ تم إنشاء ملف البيانات المشتركة")
                finally:
                    self.release_lock()
    
    def register_device(self):
        """تسجيل الجهاز في قائمة الأجهزة المتصلة"""
        if self.acquire_lock():
            try:
                data = self.load_data_unsafe()
                if data:
                    if "devices" not in data:
                        data["devices"] = {}
                    
                    data["devices"][self.device_id] = {
                        "hostname": socket.gethostname(),
                        "ip": self.get_local_ip(),
                        "last_seen": datetime.now().isoformat(),
                        "status": "online"
                    }
                    self.save_data_unsafe(data)
            finally:
                self.release_lock()
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def load_data_unsafe(self):
        """تحميل البيانات بدون قفل (للاستخدام الداخلي)"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
        return None
    
    def save_data_unsafe(self, data):
        """حفظ البيانات بدون قفل (للاستخدام الداخلي)"""
        try:
            data["last_updated"] = datetime.now().isoformat()
            data["last_updated_by"] = self.device_id
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
            return False
    
    def load_data(self):
        """تحميل البيانات مع الحماية"""
        if self.acquire_lock():
            try:
                return self.load_data_unsafe()
            finally:
                self.release_lock()
        return None
    
    def save_data(self, data):
        """حفظ البيانات مع الحماية"""
        if self.acquire_lock():
            try:
                return self.save_data_unsafe(data)
            finally:
                self.release_lock()
        return False
    
    def authenticate_user(self, email, password):
        """تسجيل دخول المستخدم"""
        data = self.load_data()
        if not data:
            return None
        
        for user in data.get("users", []):
            if user.get("email") == email and user.get("password") == password:
                if user.get("is_active", True):
                    return user
        return None
    
    def add_patient(self, patient_data, created_by_id):
        """إضافة مريض جديد"""
        data = self.load_data()
        if not data:
            return False
        
        # إنشاء معرف فريد
        patient_id = str(uuid.uuid4())[:8]
        
        patient = {
            "id": patient_id,
            "first_name": patient_data.get("first_name", ""),
            "last_name": patient_data.get("last_name", ""),
            "date_of_birth": patient_data.get("date_of_birth", ""),
            "gender": patient_data.get("gender", ""),
            "phone": patient_data.get("phone", ""),
            "email": patient_data.get("email", ""),
            "address": patient_data.get("address", ""),
            "emergency_contact": patient_data.get("emergency_contact", ""),
            "blood_type": patient_data.get("blood_type", ""),
            "allergies": patient_data.get("allergies", ""),
            "medical_history": patient_data.get("medical_history", ""),
            "insurance_number": patient_data.get("insurance_number", ""),
            "created_by": created_by_id,
            "created_by_device": self.device_id,
            "created_at": datetime.now().isoformat()
        }
        
        data["patients"].append(patient)
        
        # إضافة تنبيه للأطباء
        self.add_notification_for_doctors(
            data,
            "مريض جديد",
            f"تم تسجيل مريض جديد: {patient['first_name']} {patient['last_name']}\nمن الجهاز: {socket.gethostname()}"
        )
        
        return self.save_data(data)
    
    def get_patients(self, limit=50):
        """الحصول على قائمة المرضى"""
        data = self.load_data()
        if not data:
            return []
        
        patients = data.get("patients", [])
        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        patients.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        return patients[:limit]
    
    def add_notification_for_doctors(self, data, title, message):
        """إضافة تنبيه لجميع الأطباء"""
        doctors = [u for u in data.get("users", []) if u.get("role") == "doctor" and u.get("is_active")]
        
        for doctor in doctors:
            self.add_notification_for_user(data, doctor["id"], title, message)
    
    def add_notification_for_user(self, data, user_id, title, message):
        """إضافة تنبيه لمستخدم محدد"""
        notification = {
            "id": str(uuid.uuid4())[:8],
            "user_id": user_id,
            "title": title,
            "message": message,
            "type": "info",
            "is_read": False,
            "created_at": datetime.now().isoformat(),
            "created_by_device": self.device_id
        }
        
        data["notifications"].append(notification)
    
    def get_notifications(self, user_id, unread_only=False):
        """الحصول على تنبيهات المستخدم"""
        data = self.load_data()
        if not data:
            return []
        
        notifications = data.get("notifications", [])
        user_notifications = [n for n in notifications if n.get("user_id") == user_id]
        
        if unread_only:
            user_notifications = [n for n in user_notifications if not n.get("is_read", False)]
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        user_notifications.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        return user_notifications
    
    def mark_notification_read(self, notification_id):
        """تمييز التنبيه كمقروء"""
        data = self.load_data()
        if not data:
            return False
        
        for notification in data.get("notifications", []):
            if notification.get("id") == notification_id:
                notification["is_read"] = True
                notification["read_at"] = datetime.now().isoformat()
                notification["read_by_device"] = self.device_id
                break
        
        return self.save_data(data)
    
    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة المعلومات"""
        data = self.load_data()
        if not data:
            return {}
        
        today = date.today().isoformat()
        
        total_patients = len(data.get("patients", []))
        today_appointments = len([a for a in data.get("appointments", []) 
                                if a.get("appointment_date") == today])
        active_doctors = len([u for u in data.get("users", []) 
                            if u.get("role") == "doctor" and u.get("is_active")])
        
        # إحصائيات الأجهزة
        devices = data.get("devices", {})
        online_devices = len([d for d in devices.values() if d.get("status") == "online"])
        
        return {
            "total_patients": total_patients,
            "today_appointments": today_appointments,
            "active_doctors": active_doctors,
            "online_devices": online_devices,
            "pending_appointments": 0,
            "completed_appointments": 0,
            "month_appointments": 0,
            "shared_folder": str(self.shared_folder),
            "current_device": self.device_id
        }
    
    def get_doctors(self):
        """الحصول على قائمة الأطباء"""
        data = self.load_data()
        if not data:
            return []
        
        doctors = [u for u in data.get("users", []) 
                  if u.get("role") == "doctor" and u.get("is_active")]
        return doctors
    
    def get_connected_devices(self):
        """الحصول على قائمة الأجهزة المتصلة"""
        data = self.load_data()
        if not data:
            return {}
        
        return data.get("devices", {})

# إنشاء مثيل مشترك
network_shared_data = NetworkSharedDataManager()

# دوال مساعدة للاستخدام المباشر
def authenticate_user(email, password):
    return network_shared_data.authenticate_user(email, password)

def add_patient(patient_data, created_by_id):
    return network_shared_data.add_patient(patient_data, created_by_id)

def get_patients(limit=50):
    return network_shared_data.get_patients(limit)

def get_dashboard_stats():
    return network_shared_data.get_dashboard_stats()

def get_notifications(user_id, unread_only=False):
    return network_shared_data.get_notifications(user_id, unread_only)

def mark_notification_read(notification_id):
    return network_shared_data.mark_notification_read(notification_id)

def get_doctors():
    return network_shared_data.get_doctors()

def get_connected_devices():
    return network_shared_data.get_connected_devices()
