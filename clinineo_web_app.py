#!/usr/bin/env python3
"""
Clinineo Advanced Web Application
تطبيق Clinineo الويب المتطور للموبايل
"""

from flask import Flask, render_template_string, request, jsonify, redirect, url_for, send_file
import json
import os
import uuid
from datetime import datetime
import socket
import threading
import shutil

app = Flask(__name__)

class ClinicData:
    """مدير البيانات المحلي"""
    
    def __init__(self):
        self.data_file = "clinineo_web_data.json"
        self.prescriptions_file = "web_prescriptions.json"
        self.radiology_file = "web_radiology.json"
        self.data = self.load_data()
        self.prescriptions = self.load_prescriptions()
        self.radiology_records = self.load_radiology()
    
    def load_data(self):
        """تحميل بيانات المرضى"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "patients": [],
            "notifications": [],
            "stats": {"total_patients": 0, "today_appointments": 0},
            "last_updated": datetime.now().isoformat()
        }
    
    def load_prescriptions(self):
        """تحميل الروشتات"""
        if os.path.exists(self.prescriptions_file):
            try:
                with open(self.prescriptions_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return []
    
    def load_radiology(self):
        """تحميل سجلات الأشعة"""
        if os.path.exists(self.radiology_file):
            try:
                with open(self.radiology_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return []
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            self.data["last_updated"] = datetime.now().isoformat()
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def save_prescriptions(self):
        """حفظ الروشتات"""
        try:
            with open(self.prescriptions_file, 'w', encoding='utf-8') as f:
                json.dump(self.prescriptions, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def save_radiology(self):
        """حفظ سجلات الأشعة"""
        try:
            with open(self.radiology_file, 'w', encoding='utf-8') as f:
                json.dump(self.radiology_records, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def add_patient(self, patient_data):
        """إضافة مريض جديد"""
        patient_id = str(uuid.uuid4())[:8]
        patient = {
            "id": patient_id,
            "first_name": patient_data.get("first_name", ""),
            "last_name": patient_data.get("last_name", ""),
            "date_of_birth": patient_data.get("date_of_birth", ""),
            "gender": patient_data.get("gender", ""),
            "phone": patient_data.get("phone", ""),
            "email": patient_data.get("email", ""),
            "address": patient_data.get("address", ""),
            "blood_type": patient_data.get("blood_type", ""),
            "allergies": patient_data.get("allergies", ""),
            "created_at": datetime.now().isoformat()
        }
        
        self.data["patients"].insert(0, patient)
        self.data["stats"]["total_patients"] = len(self.data["patients"])
        
        # إضافة تنبيه للطبيب
        notification = {
            "id": str(uuid.uuid4())[:8],
            "title": "مريض جديد",
            "message": f"تم تسجيل مريض جديد: {patient['first_name']} {patient['last_name']}",
            "type": "new_patient",
            "patient_id": patient_id,
            "is_read": False,
            "created_at": datetime.now().isoformat()
        }
        
        self.data["notifications"].insert(0, notification)
        self.save_data()
        return patient_id
    
    def add_prescription(self, prescription_data):
        """إضافة روشتة جديدة"""
        prescription_id = str(uuid.uuid4())[:8]
        prescription = {
            "id": prescription_id,
            **prescription_data,
            "created_at": datetime.now().isoformat()
        }
        
        self.prescriptions.insert(0, prescription)
        self.save_prescriptions()
        return prescription_id
    
    def add_radiology_record(self, radiology_data):
        """إضافة سجل أشعة جديد"""
        record_id = str(uuid.uuid4())[:8]
        record = {
            "id": record_id,
            **radiology_data,
            "created_at": datetime.now().isoformat()
        }
        
        self.radiology_records.insert(0, record)
        self.save_radiology()
        return record_id

# إنشاء مثيل البيانات
clinic_data = ClinicData()

# قوالب HTML
BASE_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Clinineo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, {{ header_color }});
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 20px;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid {{ accent_color }};
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: {{ accent_color }};
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .notification {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            border-left: 4px solid #fdcb6e;
        }
        
        .notification.unread {
            background: #e3f2fd;
            border-color: #bbdefb;
            border-left-color: #2196f3;
        }
        
        .back-btn {
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 0;
                border-radius: 0;
                min-height: 100vh;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ header_title }}</h1>
            <p>{{ header_subtitle }}</p>
        </div>
        <div class="content">
            {{ content }}
        </div>
    </div>
</body>
</html>
"""

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    content = """
    <div style="text-align: center; padding: 40px 20px;">
        <h2 style="color: #333; margin-bottom: 30px;">🏥 مرحباً بك في Clinineo</h2>
        <p style="color: #666; margin-bottom: 40px;">نظام إدارة العيادات المتطور</p>
        
        <a href="/registrar" class="btn btn-primary">
            🏥 تطبيق الاستقبال
        </a>
        
        <a href="/doctor" class="btn btn-success">
            🩺 تطبيق الطبيب
        </a>
        
        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 12px;">
            <h3 style="color: #333; margin-bottom: 15px;">📱 للاستخدام على الموبايل:</h3>
            <p style="color: #666; font-size: 14px;">
                1. اختر التطبيق المطلوب<br>
                2. أضف الصفحة للشاشة الرئيسية<br>
                3. استخدم مثل تطبيق حقيقي!
            </p>
        </div>
    </div>
    """
    
    return render_template_string(BASE_TEMPLATE,
        title="الرئيسية",
        header_title="🏥 Clinineo",
        header_subtitle="نظام إدارة العيادات المتطور",
        header_color="#667eea 0%, #764ba2 100%",
        accent_color="#667eea",
        content=content
    )

@app.route('/registrar')
def registrar_home():
    """الصفحة الرئيسية للاستقبال"""
    stats = clinic_data.data.get("stats", {})
    total_patients = len(clinic_data.data.get("patients", []))

    content = f"""
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{total_patients}</div>
            <div class="stat-label">إجمالي المرضى</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{len([p for p in clinic_data.data.get('patients', []) if p.get('created_at', '').startswith(datetime.now().strftime('%Y-%m-%d'))])}</div>
            <div class="stat-label">مرضى اليوم</div>
        </div>
    </div>

    <a href="/registrar/add_patient" class="btn btn-primary">
        ➕ إضافة مريض جديد
    </a>

    <a href="/registrar/patients" class="btn btn-info">
        👥 عرض جميع المرضى
    </a>

    <a href="/registrar/search" class="btn btn-secondary">
        🔍 البحث عن مريض
    </a>

    <a href="/" class="btn btn-secondary">
        🏠 العودة للرئيسية
    </a>
    """

    return render_template_string(BASE_TEMPLATE,
        title="الاستقبال",
        header_title="🏥 الاستقبال",
        header_subtitle="إدارة المرضى والمواعيد",
        header_color="#2196f3 0%, #21cbf3 100%",
        accent_color="#2196f3",
        content=content
    )

@app.route('/registrar/add_patient', methods=['GET', 'POST'])
def add_patient():
    """إضافة مريض جديد"""
    if request.method == 'POST':
        patient_data = {
            "first_name": request.form.get("first_name", ""),
            "last_name": request.form.get("last_name", ""),
            "date_of_birth": request.form.get("date_of_birth", ""),
            "gender": request.form.get("gender", ""),
            "phone": request.form.get("phone", ""),
            "email": request.form.get("email", ""),
            "address": request.form.get("address", ""),
            "blood_type": request.form.get("blood_type", ""),
            "allergies": request.form.get("allergies", "")
        }

        patient_id = clinic_data.add_patient(patient_data)

        if patient_id:
            return redirect(url_for('registrar_home') + '?success=تم إضافة المريض بنجاح')
        else:
            error_msg = "حدث خطأ في إضافة المريض"

    success_msg = request.args.get('success', '')

    content = f"""
    <a href="/registrar" class="back-btn">← العودة</a>

    {f'<div class="success-message">{success_msg}</div>' if success_msg else ''}

    <form method="POST">
        <div class="form-group">
            <label>الاسم الأول *</label>
            <input type="text" name="first_name" class="form-control" required>
        </div>

        <div class="form-group">
            <label>اسم العائلة *</label>
            <input type="text" name="last_name" class="form-control" required>
        </div>

        <div class="form-group">
            <label>تاريخ الميلاد</label>
            <input type="date" name="date_of_birth" class="form-control">
        </div>

        <div class="form-group">
            <label>الجنس</label>
            <select name="gender" class="form-control">
                <option value="">اختر الجنس</option>
                <option value="male">ذكر</option>
                <option value="female">أنثى</option>
            </select>
        </div>

        <div class="form-group">
            <label>رقم الهاتف *</label>
            <input type="tel" name="phone" class="form-control" required>
        </div>

        <div class="form-group">
            <label>البريد الإلكتروني</label>
            <input type="email" name="email" class="form-control">
        </div>

        <div class="form-group">
            <label>العنوان</label>
            <textarea name="address" class="form-control" rows="3"></textarea>
        </div>

        <div class="form-group">
            <label>فصيلة الدم</label>
            <select name="blood_type" class="form-control">
                <option value="">اختر فصيلة الدم</option>
                <option value="A+">A+</option>
                <option value="A-">A-</option>
                <option value="B+">B+</option>
                <option value="B-">B-</option>
                <option value="AB+">AB+</option>
                <option value="AB-">AB-</option>
                <option value="O+">O+</option>
                <option value="O-">O-</option>
            </select>
        </div>

        <div class="form-group">
            <label>الحساسية المعروفة</label>
            <textarea name="allergies" class="form-control" rows="2" placeholder="مثال: حساسية من البنسلين"></textarea>
        </div>

        <button type="submit" class="btn btn-primary">
            💾 حفظ المريض
        </button>
    </form>
    """

    return render_template_string(BASE_TEMPLATE,
        title="إضافة مريض",
        header_title="➕ إضافة مريض جديد",
        header_subtitle="إدخال بيانات المريض",
        header_color="#4caf50 0%, #8bc34a 100%",
        accent_color="#4caf50",
        content=content
    )

@app.route('/registrar/patients')
def view_patients():
    """عرض جميع المرضى"""
    patients = clinic_data.data.get("patients", [])

    patients_html = ""
    for patient in patients[:20]:  # عرض آخر 20 مريض
        patients_html += f"""
        <div class="card">
            <h4 style="color: #333; margin-bottom: 10px;">
                👤 {patient.get('first_name', '')} {patient.get('last_name', '')}
            </h4>
            <p style="margin: 5px 0; color: #666;">
                📞 {patient.get('phone', 'غير محدد')}
            </p>
            <p style="margin: 5px 0; color: #666;">
                🩸 {patient.get('blood_type', 'غير محدد')}
            </p>
            <p style="margin: 5px 0; color: #666;">
                📅 {patient.get('created_at', '')[:10]}
            </p>
            <a href="/patient_details/{patient.get('id')}" class="btn btn-info" style="margin-top: 10px; padding: 8px 16px; font-size: 14px;">
                👁️ عرض التفاصيل
            </a>
        </div>
        """

    if not patients_html:
        patients_html = '<div class="card"><p style="text-align: center; color: #666;">لا توجد مرضى مسجلين</p></div>'

    content = f"""
    <a href="/registrar" class="back-btn">← العودة</a>

    <h3 style="margin-bottom: 20px; color: #333;">👥 جميع المرضى ({len(patients)})</h3>

    {patients_html}
    """

    return render_template_string(BASE_TEMPLATE,
        title="المرضى",
        header_title="👥 قائمة المرضى",
        header_subtitle=f"إجمالي {len(patients)} مريض",
        header_color="#2196f3 0%, #21cbf3 100%",
        accent_color="#2196f3",
        content=content
    )

@app.route('/doctor')
def doctor_home():
    """الصفحة الرئيسية للطبيب"""
    notifications = clinic_data.data.get("notifications", [])
    unread_count = len([n for n in notifications if not n.get("is_read", False)])
    total_patients = len(clinic_data.data.get("patients", []))
    total_prescriptions = len(clinic_data.prescriptions)

    content = f"""
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{total_patients}</div>
            <div class="stat-label">إجمالي المرضى</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{total_prescriptions}</div>
            <div class="stat-label">الروشتات</div>
        </div>
    </div>

    {f'<div class="notification unread">🔔 لديك {unread_count} تنبيه جديد</div>' if unread_count > 0 else ''}

    <a href="/doctor/notifications" class="btn btn-warning">
        🔔 التنبيهات ({unread_count})
    </a>

    <a href="/doctor/patients" class="btn btn-primary">
        👥 المرضى
    </a>

    <a href="/doctor/prescriptions" class="btn btn-success">
        📝 الروشتات
    </a>

    <a href="/doctor/radiology" class="btn btn-info">
        🩻 الأشعة والتحاليل
    </a>

    <a href="/doctor/reports" class="btn btn-warning">
        📊 التقارير والإحصائيات
    </a>

    <a href="/" class="btn btn-secondary">
        🏠 العودة للرئيسية
    </a>
    """

    return render_template_string(BASE_TEMPLATE,
        title="الطبيب",
        header_title="🩺 د. أحمد محمد",
        header_subtitle="طب عام - عيادة Clinineo",
        header_color="#4caf50 0%, #8bc34a 100%",
        accent_color="#4caf50",
        content=content
    )

@app.route('/doctor/notifications')
def doctor_notifications():
    """تنبيهات الطبيب"""
    notifications = clinic_data.data.get("notifications", [])

    # تحديد التنبيهات كمقروءة
    for notification in notifications:
        notification["is_read"] = True
    clinic_data.save_data()

    notifications_html = ""
    for notification in notifications[:10]:
        notifications_html += f"""
        <div class="card">
            <h4 style="color: #333; margin-bottom: 10px;">
                {notification.get('title', '')}
            </h4>
            <p style="margin: 5px 0; color: #666;">
                {notification.get('message', '')}
            </p>
            <p style="margin: 5px 0; color: #999; font-size: 12px;">
                📅 {notification.get('created_at', '')[:16].replace('T', ' ')}
            </p>
        </div>
        """

    if not notifications_html:
        notifications_html = '<div class="card"><p style="text-align: center; color: #666;">لا توجد تنبيهات</p></div>'

    content = f"""
    <a href="/doctor" class="back-btn">← العودة</a>

    <h3 style="margin-bottom: 20px; color: #333;">🔔 التنبيهات</h3>

    {notifications_html}
    """

    return render_template_string(BASE_TEMPLATE,
        title="التنبيهات",
        header_title="🔔 التنبيهات",
        header_subtitle="تنبيهات العيادة",
        header_color="#ff9800 0%, #ffc107 100%",
        accent_color="#ff9800",
        content=content
    )

@app.route('/doctor/patients')
def doctor_patients():
    """مرضى الطبيب"""
    patients = clinic_data.data.get("patients", [])

    patients_html = ""
    for patient in patients[:15]:
        patients_html += f"""
        <div class="card">
            <h4 style="color: #333; margin-bottom: 10px;">
                👤 {patient.get('first_name', '')} {patient.get('last_name', '')}
            </h4>
            <p style="margin: 5px 0; color: #666;">
                📞 {patient.get('phone', 'غير محدد')}
            </p>
            <p style="margin: 5px 0; color: #666;">
                🩸 {patient.get('blood_type', 'غير محدد')}
            </p>
            <div style="margin-top: 10px;">
                <a href="/patient_details/{patient.get('id')}" class="btn btn-info" style="padding: 8px 16px; font-size: 14px; margin: 2px;">
                    👁️ التفاصيل
                </a>
                <a href="/doctor/prescriptions/new?patient_id={patient.get('id')}" class="btn btn-success" style="padding: 8px 16px; font-size: 14px; margin: 2px;">
                    📝 روشتة
                </a>
            </div>
        </div>
        """

    if not patients_html:
        patients_html = '<div class="card"><p style="text-align: center; color: #666;">لا توجد مرضى</p></div>'

    content = f"""
    <a href="/doctor" class="back-btn">← العودة</a>

    <h3 style="margin-bottom: 20px; color: #333;">👥 المرضى ({len(patients)})</h3>

    {patients_html}
    """

    return render_template_string(BASE_TEMPLATE,
        title="المرضى",
        header_title="👥 مرضى الطبيب",
        header_subtitle=f"إجمالي {len(patients)} مريض",
        header_color="#2196f3 0%, #21cbf3 100%",
        accent_color="#2196f3",
        content=content
    )

@app.route('/doctor/prescriptions')
def doctor_prescriptions():
    """روشتات الطبيب"""
    prescriptions = clinic_data.prescriptions

    prescriptions_html = ""
    for prescription in prescriptions[:10]:
        medications_text = ""
        for med in prescription.get('medications', []):
            medications_text += f"• {med.get('name', '')} - {med.get('dosage', '')} - {med.get('frequency', '')} مرات/يوم<br>"

        prescriptions_html += f"""
        <div class="card">
            <h4 style="color: #333; margin-bottom: 10px;">
                👤 {prescription.get('patient_name', '')}
            </h4>
            <p style="margin: 5px 0; color: #666;">
                🩺 التشخيص: {prescription.get('diagnosis', 'غير محدد')}
            </p>
            <p style="margin: 5px 0; color: #666;">
                🔢 كود المرض: {prescription.get('disease_code', 'غير محدد')}
            </p>
            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                <strong>💊 الأدوية:</strong><br>
                {medications_text if medications_text else 'لا توجد أدوية'}
            </div>
            <p style="margin: 5px 0; color: #999; font-size: 12px;">
                📅 {prescription.get('date', '')[:10]}
            </p>
        </div>
        """

    if not prescriptions_html:
        prescriptions_html = '<div class="card"><p style="text-align: center; color: #666;">لا توجد روشتات</p></div>'

    content = f"""
    <a href="/doctor" class="back-btn">← العودة</a>

    <div style="margin-bottom: 20px;">
        <a href="/doctor/prescriptions/new" class="btn btn-primary">
            ➕ كتابة روشتة جديدة
        </a>
    </div>

    <h3 style="margin-bottom: 20px; color: #333;">📝 الروشتات ({len(prescriptions)})</h3>

    {prescriptions_html}
    """

    return render_template_string(BASE_TEMPLATE,
        title="الروشتات",
        header_title="📝 الروشتات",
        header_subtitle="روشتات الطبيب",
        header_color="#9c27b0 0%, #e91e63 100%",
        accent_color="#9c27b0",
        content=content
    )

@app.route('/doctor/prescriptions/new', methods=['GET', 'POST'])
def new_prescription():
    """كتابة روشتة جديدة"""
    patients = clinic_data.data.get("patients", [])
    selected_patient_id = request.args.get('patient_id', '')

    if request.method == 'POST':
        # جمع بيانات الأدوية
        medications = []
        med_names = request.form.getlist('med_name[]')
        med_dosages = request.form.getlist('med_dosage[]')
        med_frequencies = request.form.getlist('med_frequency[]')
        med_durations = request.form.getlist('med_duration[]')

        for i in range(len(med_names)):
            if med_names[i].strip():
                medications.append({
                    'name': med_names[i].strip(),
                    'dosage': med_dosages[i] if i < len(med_dosages) else '',
                    'frequency': med_frequencies[i] if i < len(med_frequencies) else '',
                    'duration': med_durations[i] if i < len(med_durations) else ''
                })

        # العثور على بيانات المريض
        patient_name = ""
        for patient in patients:
            if patient.get('id') == request.form.get('patient_id'):
                patient_name = f"{patient.get('first_name', '')} {patient.get('last_name', '')}"
                break

        prescription_data = {
            "patient_id": request.form.get('patient_id'),
            "patient_name": patient_name,
            "doctor_name": "د. أحمد محمد",
            "doctor_specialty": "طب عام",
            "clinic_name": "عيادة Clinineo الطبية",
            "chief_complaint": request.form.get('chief_complaint', ''),
            "diagnosis": request.form.get('diagnosis', ''),
            "disease_code": request.form.get('disease_code', ''),
            "medications": medications,
            "instructions": request.form.get('instructions', ''),
            "date": datetime.now().isoformat()
        }

        prescription_id = clinic_data.add_prescription(prescription_data)

        if prescription_id:
            return redirect('/doctor/prescriptions?success=تم حفظ الروشتة بنجاح')

    # إنشاء قائمة المرضى
    patients_options = '<option value="">اختر مريض</option>'
    for patient in patients:
        selected = 'selected' if patient.get('id') == selected_patient_id else ''
        patients_options += f'<option value="{patient.get("id")}" {selected}>{patient.get("first_name", "")} {patient.get("last_name", "")}</option>'

    content = f"""
    <a href="/doctor/prescriptions" class="back-btn">← العودة</a>

    <form method="POST" id="prescriptionForm">
        <div class="form-group">
            <label>المريض *</label>
            <select name="patient_id" class="form-control" required>
                {patients_options}
            </select>
        </div>

        <div class="form-group">
            <label>الشكوى الرئيسية</label>
            <textarea name="chief_complaint" class="form-control" rows="2" placeholder="وصف الأعراض والشكوى..."></textarea>
        </div>

        <div class="form-group">
            <label>التشخيص</label>
            <textarea name="diagnosis" class="form-control" rows="2" placeholder="التشخيص الطبي..."></textarea>
        </div>

        <div class="form-group">
            <label>كود المرض (ICD-10)</label>
            <input type="text" name="disease_code" class="form-control" placeholder="مثال: J06.9, A09, K29.7">
            <small style="color: #666;">أمثلة: J06.9 (زكام), A09 (إسهال), K29.7 (التهاب معدة)</small>
        </div>

        <div class="form-group">
            <label>الأدوية</label>
            <div id="medications">
                <div class="medication-row" style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 8px;">
                    <input type="text" name="med_name[]" class="form-control" placeholder="اسم الدواء" style="margin-bottom: 5px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 5px;">
                        <input type="text" name="med_dosage[]" class="form-control" placeholder="الجرعة">
                        <input type="text" name="med_frequency[]" class="form-control" placeholder="مرات/يوم">
                        <input type="text" name="med_duration[]" class="form-control" placeholder="المدة">
                    </div>
                </div>
            </div>
            <button type="button" onclick="addMedication()" class="btn btn-info" style="margin-top: 10px; padding: 8px 16px; font-size: 14px;">
                ➕ إضافة دواء
            </button>
        </div>

        <div class="form-group">
            <label>تعليمات إضافية</label>
            <textarea name="instructions" class="form-control" rows="3" placeholder="تعليمات للمريض، نصائح، موعد المراجعة..."></textarea>
        </div>

        <button type="submit" class="btn btn-primary">
            💾 حفظ الروشتة
        </button>
    </form>

    <script>
        function addMedication() {{
            const medicationsDiv = document.getElementById('medications');
            const newMedRow = document.createElement('div');
            newMedRow.className = 'medication-row';
            newMedRow.style.cssText = 'border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 8px;';
            newMedRow.innerHTML = `
                <input type="text" name="med_name[]" class="form-control" placeholder="اسم الدواء" style="margin-bottom: 5px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 20px; gap: 5px;">
                    <input type="text" name="med_dosage[]" class="form-control" placeholder="الجرعة">
                    <input type="text" name="med_frequency[]" class="form-control" placeholder="مرات/يوم">
                    <input type="text" name="med_duration[]" class="form-control" placeholder="المدة">
                    <button type="button" onclick="this.parentElement.parentElement.remove()" style="background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">✕</button>
                </div>
            `;
            medicationsDiv.appendChild(newMedRow);
        }}
    </script>
    """

    return render_template_string(BASE_TEMPLATE,
        title="روشتة جديدة",
        header_title="📝 كتابة روشتة جديدة",
        header_subtitle="د. أحمد محمد - طب عام",
        header_color="#9c27b0 0%, #e91e63 100%",
        accent_color="#9c27b0",
        content=content
    )

@app.route('/doctor/reports')
def doctor_reports():
    """تقارير وإحصائيات الطبيب"""
    patients = clinic_data.data.get("patients", [])
    prescriptions = clinic_data.prescriptions
    radiology_records = clinic_data.radiology_records

    # إحصائيات عامة
    total_patients = len(patients)
    total_prescriptions = len(prescriptions)

    # مرضى هذا الشهر
    current_month = datetime.now().strftime('%Y-%m')
    month_patients = len([p for p in patients if p.get('created_at', '').startswith(current_month)])

    # روشتات اليوم
    today = datetime.now().strftime('%Y-%m-%d')
    today_prescriptions = len([p for p in prescriptions if p.get('date', '').startswith(today)])

    # إحصائيات الأمراض
    disease_count = {}
    for prescription in prescriptions:
        diagnosis = prescription.get('diagnosis', '').strip()
        disease_code = prescription.get('disease_code', '').strip()
        if diagnosis:
            key = f"{diagnosis} ({disease_code})" if disease_code else diagnosis
            disease_count[key] = disease_count.get(key, 0) + 1

    top_diseases = sorted(disease_count.items(), key=lambda x: x[1], reverse=True)[:5]

    # إحصائيات الأدوية
    medication_count = {}
    for prescription in prescriptions:
        for med in prescription.get('medications', []):
            med_name = med.get('name', '').strip()
            if med_name:
                medication_count[med_name] = medication_count.get(med_name, 0) + 1

    top_medications = sorted(medication_count.items(), key=lambda x: x[1], reverse=True)[:5]

    # إنشاء HTML للأمراض
    diseases_html = ""
    for disease, count in top_diseases:
        diseases_html += f"""
        <div style="display: flex; justify-content: space-between; padding: 8px; border-bottom: 1px solid #eee;">
            <span>{disease}</span>
            <strong style="color: #dc3545;">{count}</strong>
        </div>
        """

    # إنشاء HTML للأدوية
    medications_html = ""
    for medication, count in top_medications:
        medications_html += f"""
        <div style="display: flex; justify-content: space-between; padding: 8px; border-bottom: 1px solid #eee;">
            <span>{medication}</span>
            <strong style="color: #9c27b0;">{count}</strong>
        </div>
        """

    content = f"""
    <a href="/doctor" class="back-btn">← العودة</a>

    <h3 style="margin-bottom: 20px; color: #333;">📊 التقارير والإحصائيات</h3>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{total_patients}</div>
            <div class="stat-label">إجمالي المرضى</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{total_prescriptions}</div>
            <div class="stat-label">إجمالي الروشتات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{month_patients}</div>
            <div class="stat-label">مرضى هذا الشهر</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{today_prescriptions}</div>
            <div class="stat-label">روشتات اليوم</div>
        </div>
    </div>

    <div class="card">
        <h4 style="color: #333; margin-bottom: 15px;">🦠 الأمراض الأكثر شيوعاً</h4>
        {diseases_html if diseases_html else '<p style="color: #666; text-align: center;">لا توجد بيانات</p>'}
    </div>

    <div class="card">
        <h4 style="color: #333; margin-bottom: 15px;">💊 الأدوية الأكثر وصفاً</h4>
        {medications_html if medications_html else '<p style="color: #666; text-align: center;">لا توجد بيانات</p>'}
    </div>

    <div class="card">
        <h4 style="color: #333; margin-bottom: 15px;">📅 التقرير الشهري</h4>
        <p><strong>الشهر:</strong> {datetime.now().strftime('%B %Y')}</p>
        <p><strong>مرضى جدد:</strong> {month_patients}</p>
        <p><strong>روشتات مكتوبة:</strong> {len([p for p in prescriptions if p.get('date', '').startswith(current_month)])}</p>
        <p><strong>متوسط الروشتات/يوم:</strong> {len([p for p in prescriptions if p.get('date', '').startswith(current_month)]) // max(1, datetime.now().day)}</p>
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
        title="التقارير",
        header_title="📊 التقارير والإحصائيات",
        header_subtitle="تحليل شامل للعيادة",
        header_color="#ff5722 0%, #ff9800 100%",
        accent_color="#ff5722",
        content=content
    )

@app.route('/patient_details/<patient_id>')
def patient_details(patient_id):
    """تفاصيل المريض"""
    patients = clinic_data.data.get("patients", [])
    patient = None

    for p in patients:
        if p.get('id') == patient_id:
            patient = p
            break

    if not patient:
        return redirect('/doctor/patients')

    # البحث عن روشتات المريض
    patient_prescriptions = [p for p in clinic_data.prescriptions if p.get('patient_id') == patient_id]

    # البحث عن سجلات الأشعة
    patient_radiology = [r for r in clinic_data.radiology_records if r.get('patient_id') == patient_id]

    content = f"""
    <a href="/doctor/patients" class="back-btn">← العودة</a>

    <div class="card">
        <h3 style="color: #333; margin-bottom: 20px;">👤 {patient.get('first_name', '')} {patient.get('last_name', '')}</h3>

        <div style="display: grid; gap: 10px;">
            <div><strong>📞 الهاتف:</strong> {patient.get('phone', 'غير محدد')}</div>
            <div><strong>🎂 تاريخ الميلاد:</strong> {patient.get('date_of_birth', 'غير محدد')}</div>
            <div><strong>⚧ الجنس:</strong> {'ذكر' if patient.get('gender') == 'male' else 'أنثى' if patient.get('gender') == 'female' else 'غير محدد'}</div>
            <div><strong>📧 البريد الإلكتروني:</strong> {patient.get('email', 'غير محدد')}</div>
            <div><strong>🏠 العنوان:</strong> {patient.get('address', 'غير محدد')}</div>
            <div><strong>🩸 فصيلة الدم:</strong> {patient.get('blood_type', 'غير محدد')}</div>
            <div><strong>⚠️ الحساسية:</strong> {patient.get('allergies', 'لا توجد')}</div>
            <div><strong>📅 تاريخ التسجيل:</strong> {patient.get('created_at', '')[:10]}</div>
        </div>
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 20px 0;">
        <a href="/doctor/prescriptions/new?patient_id={patient_id}" class="btn btn-success">
            📝 كتابة روشتة
        </a>
        <a href="/doctor/radiology/new?patient_id={patient_id}" class="btn btn-info">
            🩻 طلب أشعة
        </a>
    </div>

    <div class="card">
        <h4 style="color: #333; margin-bottom: 15px;">📝 الروشتات ({len(patient_prescriptions)})</h4>
        {f'<p style="color: #666;">آخر روشتة: {patient_prescriptions[0].get("date", "")[:10]}</p>' if patient_prescriptions else '<p style="color: #666;">لا توجد روشتات</p>'}
    </div>

    <div class="card">
        <h4 style="color: #333; margin-bottom: 15px;">🩻 الأشعة والتحاليل ({len(patient_radiology)})</h4>
        {f'<p style="color: #666;">آخر فحص: {patient_radiology[0].get("date", "")[:10]}</p>' if patient_radiology else '<p style="color: #666;">لا توجد فحوصات</p>'}
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
        title="تفاصيل المريض",
        header_title=f"👤 {patient.get('first_name', '')} {patient.get('last_name', '')}",
        header_subtitle="تفاصيل المريض الكاملة",
        header_color="#2196f3 0%, #21cbf3 100%",
        accent_color="#2196f3",
        content=content
    )

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # إنشاء اتصال وهمي للحصول على IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

if __name__ == '__main__':
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    port = 8080

    print("🌐" + "="*50)
    print("🏥 Clinineo Web App - تطبيق الويب المتطور")
    print("="*52)
    print(f"🔗 الروابط المتاحة:")
    print(f"   📱 الصفحة الرئيسية: http://{local_ip}:{port}/")
    print(f"   🏥 تطبيق الاستقبال: http://{local_ip}:{port}/registrar")
    print(f"   🩺 تطبيق الطبيب: http://{local_ip}:{port}/doctor")
    print("="*52)
    print(f"📱 للاستخدام على الموبايل:")
    print(f"   1. افتح المتصفح على الهاتف")
    print(f"   2. اذهب إلى: http://{local_ip}:{port}/")
    print(f"   3. اختر التطبيق المطلوب")
    print(f"   4. أضف للشاشة الرئيسية")
    print(f"   5. استخدم مثل تطبيق حقيقي!")
    print("="*52)
    print(f"🔧 الخادم يعمل على: {local_ip}:{port}")
    print("🛑 اضغط Ctrl+C للإيقاف")
    print("="*52)

    app.run(host='0.0.0.0', port=port, debug=False)
