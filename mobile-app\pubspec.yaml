name: clinineo_mobile
description: Clinineo Mobile App - Clinic Management System

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI Components
  cupertino_icons: ^1.0.2
  material_design_icons_flutter: ^7.0.7296
  
  # State Management
  provider: ^6.1.1
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.4.0
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Authentication & Security
  crypto: ^3.0.3
  
  # Navigation
  go_router: ^12.1.3
  
  # Forms & Validation
  flutter_form_builder: ^9.1.1
  form_builder_validators: ^9.1.0
  
  # Date & Time
  intl: ^0.19.0
  
  # File Handling
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  path_provider: ^2.1.1
  
  # Notifications
  flutter_local_notifications: ^16.3.2
  
  # Real-time Communication
  socket_io_client: ^2.0.3+1
  
  # UI Enhancements
  flutter_spinkit: ^5.2.0
  shimmer: ^3.0.0
  cached_network_image: ^3.3.0
  
  # Charts & Analytics
  fl_chart: ^0.65.0
  
  # PDF & Documents
  pdf: ^3.10.7
  printing: ^5.11.1
  
  # Permissions
  permission_handler: ^11.2.0
  
  # Device Info
  device_info_plus: ^9.1.1
  
  # Connectivity
  connectivity_plus: ^5.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  flutter_lints: ^3.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
