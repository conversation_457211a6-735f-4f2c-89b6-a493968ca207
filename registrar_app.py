#!/usr/bin/env python3
"""
Clinineo Registrar Application
تطبيق موظف الاستقبال - Clinineo
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import requests
import json
from datetime import datetime, date
import threading

class RegistrarApp:
    """تطبيق موظف الاستقبال"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Clinineo - موظف الاستقبال")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # إعدادات الخادم
        self.server_ip = "127.0.0.1"  # سيتم تحديثه
        self.server_port = "8080"
        self.base_url = f"http://{self.server_ip}:{self.server_port}"
        self.current_user = None
        self.token = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.check_server_connection()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة المرضى
        patients_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المرضى", menu=patients_menu)
        patients_menu.add_command(label="إضافة مريض جديد", command=self.show_add_patient)
        patients_menu.add_command(label="قائمة المرضى", command=self.show_patients_list)
        
        # قائمة المواعيد
        appointments_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المواعيد", menu=appointments_menu)
        appointments_menu.add_command(label="حجز موعد جديد", command=self.show_add_appointment)
        appointments_menu.add_command(label="مواعيد اليوم", command=self.show_today_appointments)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="إعدادات الخادم", command=self.show_server_settings)
        settings_menu.add_command(label="تسجيل دخول", command=self.show_login)
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط الحالة
        self.status_frame = ttk.Frame(main_frame)
        self.status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(self.status_frame, text="غير متصل بالخادم", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.user_label = ttk.Label(self.status_frame, text="")
        self.user_label.pack(side=tk.RIGHT)
        
        # منطقة المحتوى
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # الصفحة الرئيسية
        self.show_home_page()
    
    def check_server_connection(self):
        """فحص الاتصال بالخادم"""
        def check():
            try:
                response = requests.get(f"{self.base_url}/health", timeout=3)
                if response.status_code == 200:
                    self.root.after(0, self.on_server_connected)
                else:
                    self.root.after(0, self.on_server_disconnected)
            except:
                self.root.after(0, self.on_server_disconnected)
        
        threading.Thread(target=check, daemon=True).start()
    
    def on_server_connected(self):
        """عند الاتصال بالخادم"""
        self.status_label.config(text=f"متصل بالخادم: {self.base_url}", foreground="green")
    
    def on_server_disconnected(self):
        """عند انقطاع الاتصال بالخادم"""
        self.status_label.config(text="غير متصل بالخادم", foreground="red")
    
    def show_server_settings(self):
        """إظهار إعدادات الخادم"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إعدادات الخادم")
        dialog.geometry("300x150")
        dialog.resizable(False, False)
        
        # جعل النافذة في المقدمة
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="عنوان IP للخادم:").pack(pady=5)
        ip_entry = ttk.Entry(dialog, width=20)
        ip_entry.pack(pady=5)
        ip_entry.insert(0, self.server_ip)
        
        ttk.Label(dialog, text="المنفذ:").pack(pady=5)
        port_entry = ttk.Entry(dialog, width=20)
        port_entry.pack(pady=5)
        port_entry.insert(0, self.server_port)
        
        def save_settings():
            self.server_ip = ip_entry.get().strip()
            self.server_port = port_entry.get().strip()
            self.base_url = f"http://{self.server_ip}:{self.server_port}"
            dialog.destroy()
            self.check_server_connection()
        
        ttk.Button(dialog, text="حفظ", command=save_settings).pack(pady=10)
    
    def show_login(self):
        """إظهار نافذة تسجيل الدخول"""
        dialog = tk.Toplevel(self.root)
        dialog.title("تسجيل الدخول")
        dialog.geometry("300x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="البريد الإلكتروني:").pack(pady=5)
        email_entry = ttk.Entry(dialog, width=25)
        email_entry.pack(pady=5)
        email_entry.insert(0, "<EMAIL>")
        
        ttk.Label(dialog, text="كلمة المرور:").pack(pady=5)
        password_entry = ttk.Entry(dialog, width=25, show="*")
        password_entry.pack(pady=5)
        password_entry.insert(0, "registrar123")
        
        def login():
            email = email_entry.get().strip()
            password = password_entry.get().strip()
            
            try:
                response = requests.post(f"{self.base_url}/api/auth/login", 
                                       json={"email": email, "password": password},
                                       timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        self.current_user = data["user"]
                        self.token = data["access_token"]
                        self.user_label.config(text=f"المستخدم: {self.current_user['first_name']} {self.current_user['last_name']}")
                        dialog.destroy()
                        messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح")
                    else:
                        messagebox.showerror("خطأ", data.get("message", "فشل تسجيل الدخول"))
                else:
                    messagebox.showerror("خطأ", "فشل في الاتصال بالخادم")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")
        
        ttk.Button(dialog, text="تسجيل دخول", command=login).pack(pady=10)
    
    def show_home_page(self):
        """إظهار الصفحة الرئيسية"""
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # العنوان
        title_label = ttk.Label(self.content_frame, text="نظام إدارة العيادة - موظف الاستقبال", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # الأزرار الرئيسية
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(pady=20)
        
        ttk.Button(buttons_frame, text="إضافة مريض جديد", 
                  command=self.show_add_patient, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="قائمة المرضى", 
                  command=self.show_patients_list, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="حجز موعد جديد", 
                  command=self.show_add_appointment, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="مواعيد اليوم", 
                  command=self.show_today_appointments, width=20).pack(pady=5)
    
    def show_add_patient(self):
        """إظهار نافذة إضافة مريض جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة مريض جديد")
        dialog.geometry("400x500")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # إطار التمرير
        canvas = tk.Canvas(dialog)
        scrollbar = ttk.Scrollbar(dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # الحقول
        fields = {}
        
        ttk.Label(scrollable_frame, text="الاسم الأول:").pack(pady=2, anchor='w')
        fields['first_name'] = ttk.Entry(scrollable_frame, width=30)
        fields['first_name'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="الاسم الأخير:").pack(pady=2, anchor='w')
        fields['last_name'] = ttk.Entry(scrollable_frame, width=30)
        fields['last_name'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="تاريخ الميلاد (YYYY-MM-DD):").pack(pady=2, anchor='w')
        fields['date_of_birth'] = ttk.Entry(scrollable_frame, width=30)
        fields['date_of_birth'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="الجنس:").pack(pady=2, anchor='w')
        fields['gender'] = ttk.Combobox(scrollable_frame, values=['male', 'female'], width=27)
        fields['gender'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="رقم الهاتف:").pack(pady=2, anchor='w')
        fields['phone'] = ttk.Entry(scrollable_frame, width=30)
        fields['phone'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="البريد الإلكتروني:").pack(pady=2, anchor='w')
        fields['email'] = ttk.Entry(scrollable_frame, width=30)
        fields['email'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="العنوان:").pack(pady=2, anchor='w')
        fields['address'] = tk.Text(scrollable_frame, width=30, height=3)
        fields['address'].pack(pady=2, fill='x')
        
        def save_patient():
            # جمع البيانات
            patient_data = {}
            for field_name, widget in fields.items():
                if isinstance(widget, tk.Text):
                    patient_data[field_name] = widget.get("1.0", tk.END).strip()
                else:
                    patient_data[field_name] = widget.get().strip()
            
            # التحقق من البيانات المطلوبة
            required_fields = ['first_name', 'last_name', 'date_of_birth', 'gender', 'phone']
            for field in required_fields:
                if not patient_data.get(field):
                    messagebox.showerror("خطأ", f"يرجى ملء حقل {field}")
                    return
            
            # إرسال البيانات للخادم
            try:
                response = requests.post(f"{self.base_url}/api/patients", 
                                       json=patient_data, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        messagebox.showinfo("نجح", "تم إضافة المريض بنجاح")
                        dialog.destroy()
                    else:
                        messagebox.showerror("خطأ", data.get("message", "فشل في إضافة المريض"))
                else:
                    messagebox.showerror("خطأ", "فشل في الاتصال بالخادم")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")
        
        ttk.Button(scrollable_frame, text="حفظ", command=save_patient).pack(pady=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def show_patients_list(self):
        """إظهار قائمة المرضى"""
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        ttk.Label(self.content_frame, text="قائمة المرضى", 
                 font=('Arial', 14, 'bold')).pack(pady=10)
        
        # جدول المرضى
        columns = ('ID', 'الاسم', 'الهاتف', 'الجنس', 'تاريخ الإضافة')
        tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        # تحميل البيانات
        try:
            response = requests.get(f"{self.base_url}/api/patients", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    patients = data.get("data", [])
                    for patient in patients:
                        tree.insert('', tk.END, values=(
                            patient.get('id', ''),
                            f"{patient.get('first_name', '')} {patient.get('last_name', '')}",
                            patient.get('phone', ''),
                            'ذكر' if patient.get('gender') == 'male' else 'أنثى',
                            patient.get('created_at', '')[:10]
                        ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
        
        tree.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # زر العودة
        ttk.Button(self.content_frame, text="العودة للرئيسية", 
                  command=self.show_home_page).pack(pady=10)
    
    def show_add_appointment(self):
        """إظهار نافذة حجز موعد جديد"""
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_today_appointments(self):
        """إظهار مواعيد اليوم"""
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = RegistrarApp()
    app.run()
