const express = require('express');
const Joi = require('joi');
const { createClient } = require('@supabase/supabase-js');
const { authenticateToken, registrarOrAdmin, anyAuthenticated } = require('../middleware/auth');

const router = express.Router();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Validation schema
const appointmentSchema = Joi.object({
  patientId: Joi.string().uuid().required(),
  doctorId: Joi.string().uuid().required(),
  appointmentDate: Joi.date().required(),
  appointmentTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
  type: Joi.string().valid('consultation', 'follow-up', 'emergency', 'checkup').required(),
  notes: Joi.string().optional(),
  status: Joi.string().valid('scheduled', 'confirmed', 'in-progress', 'completed', 'cancelled').default('scheduled')
});

// @route   POST /api/appointments
// @desc    Create new appointment
// @access  Private (Registrar/Admin)
router.post('/', authenticateToken, registrarOrAdmin, async (req, res) => {
  try {
    // Validate input
    const { error } = appointmentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { patientId, doctorId, appointmentDate, appointmentTime, type, notes, status } = req.body;

    // Check if patient exists
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('*')
      .eq('id', patientId)
      .single();

    if (patientError || !patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Check if doctor exists
    const { data: doctor, error: doctorError } = await supabase
      .from('users')
      .select('*')
      .eq('id', doctorId)
      .eq('role', 'doctor')
      .single();

    if (doctorError || !doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found'
      });
    }

    // Check for conflicting appointments
    const appointmentDateTime = new Date(`${appointmentDate}T${appointmentTime}`);
    const { data: conflictingAppointment } = await supabase
      .from('appointments')
      .select('id')
      .eq('doctor_id', doctorId)
      .eq('appointment_date', appointmentDate)
      .eq('appointment_time', appointmentTime)
      .neq('status', 'cancelled')
      .single();

    if (conflictingAppointment) {
      return res.status(400).json({
        success: false,
        message: 'Doctor already has an appointment at this time'
      });
    }

    // Create appointment
    const appointmentData = {
      patient_id: patientId,
      doctor_id: doctorId,
      appointment_date: appointmentDate,
      appointment_time: appointmentTime,
      type,
      notes,
      status: status || 'scheduled',
      created_by: req.user.id,
      created_at: new Date().toISOString()
    };

    const { data: newAppointment, error: createError } = await supabase
      .from('appointments')
      .insert([appointmentData])
      .select(`
        *,
        patients:patient_id(*),
        doctors:doctor_id(*)
      `)
      .single();

    if (createError) {
      console.error('Create appointment error:', createError);
      return res.status(500).json({
        success: false,
        message: 'Failed to create appointment'
      });
    }

    // Send real-time notification to doctor
    const io = req.app.get('io');
    if (io) {
      io.to(`doctor-${doctorId}`).emit('new-appointment-notification', {
        type: 'NEW_APPOINTMENT',
        message: 'موعد جديد تم حجزه',
        appointment: {
          id: newAppointment.id,
          date: newAppointment.appointment_date,
          time: newAppointment.appointment_time,
          type: newAppointment.type,
          status: newAppointment.status
        },
        patient: {
          id: patient.id,
          firstName: patient.first_name,
          lastName: patient.last_name,
          phone: patient.phone,
          gender: patient.gender,
          dateOfBirth: patient.date_of_birth
        },
        registeredBy: {
          id: req.user.id,
          name: `${req.user.first_name} ${req.user.last_name}`
        },
        timestamp: new Date().toISOString()
      });

      // Also send general notification to all doctors
      io.to('doctor').emit('appointment-booked', {
        doctorId,
        appointment: newAppointment,
        patient
      });
    }

    res.status(201).json({
      success: true,
      message: 'Appointment created successfully',
      data: {
        appointment: {
          id: newAppointment.id,
          patientId: newAppointment.patient_id,
          doctorId: newAppointment.doctor_id,
          appointmentDate: newAppointment.appointment_date,
          appointmentTime: newAppointment.appointment_time,
          type: newAppointment.type,
          notes: newAppointment.notes,
          status: newAppointment.status,
          createdAt: newAppointment.created_at,
          patient: {
            id: patient.id,
            firstName: patient.first_name,
            lastName: patient.last_name,
            phone: patient.phone
          },
          doctor: {
            id: doctor.id,
            firstName: doctor.first_name,
            lastName: doctor.last_name,
            specialization: doctor.specialization
          }
        }
      }
    });
  } catch (error) {
    console.error('Create appointment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/appointments
// @desc    Get appointments with filters
// @access  Private
router.get('/', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const date = req.query.date;
    const doctorId = req.query.doctorId;
    const patientId = req.query.patientId;
    const status = req.query.status;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('appointments')
      .select(`
        *,
        patients:patient_id(id, first_name, last_name, phone, gender),
        doctors:doctor_id(id, first_name, last_name, specialization)
      `, { count: 'exact' });

    // Apply filters
    if (date) {
      query = query.eq('appointment_date', date);
    }
    if (doctorId) {
      query = query.eq('doctor_id', doctorId);
    }
    if (patientId) {
      query = query.eq('patient_id', patientId);
    }
    if (status) {
      query = query.eq('status', status);
    }

    // If user is a doctor, only show their appointments
    if (req.user.role === 'doctor') {
      query = query.eq('doctor_id', req.user.id);
    }

    // Add pagination and ordering
    query = query
      .order('appointment_date', { ascending: true })
      .order('appointment_time', { ascending: true })
      .range(offset, offset + limit - 1);

    const { data: appointments, error, count } = await query;

    if (error) {
      console.error('Get appointments error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch appointments'
      });
    }

    // Transform data
    const transformedAppointments = appointments.map(appointment => ({
      id: appointment.id,
      patientId: appointment.patient_id,
      doctorId: appointment.doctor_id,
      appointmentDate: appointment.appointment_date,
      appointmentTime: appointment.appointment_time,
      type: appointment.type,
      notes: appointment.notes,
      status: appointment.status,
      createdAt: appointment.created_at,
      patient: appointment.patients ? {
        id: appointment.patients.id,
        firstName: appointment.patients.first_name,
        lastName: appointment.patients.last_name,
        phone: appointment.patients.phone,
        gender: appointment.patients.gender
      } : null,
      doctor: appointment.doctors ? {
        id: appointment.doctors.id,
        firstName: appointment.doctors.first_name,
        lastName: appointment.doctors.last_name,
        specialization: appointment.doctors.specialization
      } : null
    }));

    res.json({
      success: true,
      data: {
        appointments: transformedAppointments,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: limit
        }
      }
    });
  } catch (error) {
    console.error('Get appointments error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
