#!/usr/bin/env python3
"""
Clinineo Mobile Server Launcher
مشغل خادم الهاتف مع تثبيت تلقائي
"""

import subprocess
import sys
import os
import time

def print_banner():
    """طباعة شعار التطبيق"""
    print("📱" + "="*50)
    print("🏥 Clinineo Mobile Server")
    print("="*52)
    print("📱 خادم الهاتف لنظام إدارة العيادة")
    print("🌐 يعمل بدون إنترنت - ربط محلي")
    print("🔋 توفير الكهرباء - عيادة متنقلة")
    print("="*52)
    print()

def check_python():
    """التحقق من Python"""
    try:
        version = sys.version_info
        if version.major >= 3 and version.minor >= 6:
            print(f"✅ Python {version.major}.{version.minor} متوفر")
            return True
        else:
            print(f"❌ Python قديم: {version.major}.{version.minor}")
            return False
    except:
        print("❌ Python غير متوفر")
        return False

def install_packages():
    """تثبيت المكتبات المطلوبة"""
    packages = [
        'flask',
        'flask-socketio', 
        'qrcode',
        'pillow',
        'python-socketio',
        'eventlet'
    ]
    
    print("📦 تثبيت المكتبات المطلوبة...")
    
    for package in packages:
        try:
            print(f"📥 تثبيت {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True, text=True)
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت {package}")
            print(f"الخطأ: {e.stderr}")
            return False
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            return False
    
    print("✅ تم تثبيت جميع المكتبات بنجاح!")
    return True

def check_dependencies():
    """التحقق من المكتبات"""
    required = ['flask', 'qrcode', 'PIL']
    missing = []
    
    for package in required:
        try:
            if package == 'PIL':
                __import__('PIL')
            else:
                __import__(package)
            print(f"✅ {package} متوفر")
        except ImportError:
            missing.append(package)
            print(f"❌ {package} غير متوفر")
    
    return missing

def create_mobile_server_file():
    """إنشاء ملف الخادم إذا لم يكن موجود"""
    if not os.path.exists('clinineo_mobile_server.py'):
        print("📁 إنشاء ملف الخادم...")
        
        # محتوى مبسط للخادم
        server_content = '''#!/usr/bin/env python3
from flask import Flask, render_template_string, request, redirect
import json
import os
import uuid
from datetime import datetime
import socket

app = Flask(__name__)

# بيانات مؤقتة
data = {"patients": [], "notifications": []}

def get_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.route('/')
def home():
    ip = get_ip()
    return f"""
    <html dir="rtl">
    <head><meta charset="UTF-8"><title>Clinineo Mobile</title>
    <style>body{{font-family:Arial;padding:20px;background:#f0f0f0}}
    .btn{{display:block;padding:15px;margin:10px 0;background:#007bff;color:white;text-decoration:none;border-radius:5px;text-align:center}}
    .card{{background:white;padding:15px;margin:10px 0;border-radius:5px;box-shadow:0 2px 4px rgba(0,0,0,0.1)}}
    </style></head>
    <body>
    <h1>🏥 Clinineo Mobile Server</h1>
    <div class="card">
    <h3>📊 الإحصائيات:</h3>
    <p>👥 المرضى: {len(data["patients"])}</p>
    <p>🔔 التنبيهات: {len(data["notifications"])}</p>
    </div>
    <a href="/registrar" class="btn">🏥 الاستقبال</a>
    <a href="/doctor" class="btn">🩺 الطبيب</a>
    <div class="card">
    <h3>🌐 معلومات الاتصال:</h3>
    <p><strong>IP:</strong> {ip}:5000</p>
    <p><strong>الاستقبال:</strong> http://{ip}:5000/registrar</p>
    <p><strong>الطبيب:</strong> http://{ip}:5000/doctor</p>
    </div>
    </body></html>
    """

@app.route('/registrar')
def registrar():
    return f"""
    <html dir="rtl">
    <head><meta charset="UTF-8"><title>الاستقبال</title>
    <style>body{{font-family:Arial;padding:20px;background:#e3f2fd}}
    .btn{{display:block;padding:15px;margin:10px 0;background:#2196f3;color:white;text-decoration:none;border-radius:5px;text-align:center}}
    .form-group{{margin:10px 0}} .form-control{{width:100%;padding:10px;border:1px solid #ddd;border-radius:5px}}
    </style></head>
    <body>
    <h1>🏥 الاستقبال</h1>
    <a href="/" style="background:#666" class="btn">← العودة</a>
    <a href="/add_patient" class="btn">➕ إضافة مريض</a>
    <a href="/patients" class="btn">👥 المرضى ({len(data["patients"])})</a>
    </body></html>
    """

@app.route('/doctor')
def doctor():
    unread = len([n for n in data["notifications"] if not n.get("read", False)])
    return f"""
    <html dir="rtl">
    <head><meta charset="UTF-8"><title>الطبيب</title>
    <style>body{{font-family:Arial;padding:20px;background:#e8f5e8}}
    .btn{{display:block;padding:15px;margin:10px 0;background:#4caf50;color:white;text-decoration:none;border-radius:5px;text-align:center}}
    </style></head>
    <body>
    <h1>🩺 الطبيب</h1>
    <a href="/" style="background:#666" class="btn">← العودة</a>
    <a href="/notifications" class="btn">🔔 التنبيهات ({unread})</a>
    <a href="/patients" class="btn">👥 المرضى</a>
    </body></html>
    """

@app.route('/add_patient', methods=['GET', 'POST'])
def add_patient():
    if request.method == 'POST':
        patient = {
            'id': str(uuid.uuid4())[:8],
            'name': request.form.get('name', ''),
            'phone': request.form.get('phone', ''),
            'created_at': datetime.now().isoformat()
        }
        data['patients'].insert(0, patient)
        data['notifications'].insert(0, {
            'message': f'مريض جديد: {patient["name"]}',
            'read': False,
            'created_at': datetime.now().isoformat()
        })
        return redirect('/registrar')
    
    return """
    <html dir="rtl">
    <head><meta charset="UTF-8"><title>إضافة مريض</title>
    <style>body{font-family:Arial;padding:20px;background:#e8f5e8}
    .form-group{margin:15px 0} .form-control{width:100%;padding:10px;border:1px solid #ddd;border-radius:5px}
    .btn{padding:15px;background:#4caf50;color:white;border:none;border-radius:5px;width:100%;font-size:16px}
    </style></head>
    <body>
    <h1>➕ إضافة مريض جديد</h1>
    <a href="/registrar" style="background:#666;color:white;padding:8px 15px;text-decoration:none;border-radius:5px">← العودة</a>
    <form method="POST" style="margin-top:20px">
        <div class="form-group">
            <label>الاسم الكامل:</label>
            <input type="text" name="name" class="form-control" required>
        </div>
        <div class="form-group">
            <label>رقم الهاتف:</label>
            <input type="tel" name="phone" class="form-control" required>
        </div>
        <button type="submit" class="btn">💾 حفظ المريض</button>
    </form>
    </body></html>
    """

@app.route('/patients')
def patients():
    patients_html = ""
    for p in data['patients'][:10]:
        patients_html += f'<div style="background:white;padding:10px;margin:5px 0;border-radius:5px"><strong>{p.get("name", "")}</strong><br>📞 {p.get("phone", "")}</div>'
    
    return f"""
    <html dir="rtl">
    <head><meta charset="UTF-8"><title>المرضى</title>
    <style>body{{font-family:Arial;padding:20px;background:#f0f0f0}}</style></head>
    <body>
    <h1>👥 المرضى ({len(data["patients"])})</h1>
    <a href="/registrar" style="background:#666;color:white;padding:8px 15px;text-decoration:none;border-radius:5px">← العودة</a>
    <div style="margin-top:20px">{patients_html if patients_html else '<p>لا توجد مرضى</p>'}</div>
    </body></html>
    """

@app.route('/notifications')
def notifications():
    for n in data['notifications']:
        n['read'] = True
    
    notif_html = ""
    for n in data['notifications'][:10]:
        notif_html += f'<div style="background:white;padding:10px;margin:5px 0;border-radius:5px">{n.get("message", "")}<br><small>{n.get("created_at", "")[:16]}</small></div>'
    
    return f"""
    <html dir="rtl">
    <head><meta charset="UTF-8"><title>التنبيهات</title>
    <style>body{{font-family:Arial;padding:20px;background:#fff3e0}}</style></head>
    <body>
    <h1>🔔 التنبيهات</h1>
    <a href="/doctor" style="background:#666;color:white;padding:8px 15px;text-decoration:none;border-radius:5px">← العودة</a>
    <div style="margin-top:20px">{notif_html if notif_html else '<p>لا توجد تنبيهات</p>'}</div>
    </body></html>
    """

if __name__ == '__main__':
    ip = get_ip()
    print("📱" + "="*50)
    print("🏥 Clinineo Mobile Server")
    print("="*52)
    print(f"🔗 الروابط:")
    print(f"   📱 الرئيسية: http://{ip}:5000/")
    print(f"   🏥 الاستقبال: http://{ip}:5000/registrar")
    print(f"   🩺 الطبيب: http://{ip}:5000/doctor")
    print("="*52)
    print("🔧 الخادم يعمل...")
    print("🛑 اضغط Ctrl+C للإيقاف")
    print("="*52)
    app.run(host='0.0.0.0', port=5000, debug=False)
'''
        
        try:
            with open('clinineo_mobile_server.py', 'w', encoding='utf-8') as f:
                f.write(server_content)
            print("✅ تم إنشاء ملف الخادم")
            return True
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف الخادم: {e}")
            return False
    else:
        print("✅ ملف الخادم موجود")
        return True

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # التحقق من Python
    if not check_python():
        print("❌ يرجى تثبيت Python 3.6 أو أحدث")
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من المكتبات
    missing = check_dependencies()
    
    if missing:
        print(f"📦 مكتبات مفقودة: {', '.join(missing)}")
        
        choice = input("هل تريد تثبيت المكتبات المطلوبة؟ (y/n): ").lower()
        
        if choice in ['y', 'yes', 'نعم', '']:
            if not install_packages():
                print("❌ فشل في تثبيت المكتبات")
                input("اضغط Enter للخروج...")
                return
        else:
            print("❌ لا يمكن تشغيل الخادم بدون المكتبات")
            input("اضغط Enter للخروج...")
            return
    
    # إنشاء ملف الخادم
    if not create_mobile_server_file():
        print("❌ فشل في إنشاء ملف الخادم")
        input("اضغط Enter للخروج...")
        return
    
    print("="*52)
    print("🚀 بدء تشغيل خادم الهاتف...")
    print("="*52)
    
    try:
        # تشغيل الخادم
        subprocess.run([sys.executable, "clinineo_mobile_server.py"])
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except FileNotFoundError:
        print("❌ ملف الخادم غير موجود")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
    
    print("="*52)
    print("👋 شكراً لاستخدام Clinineo Mobile!")
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
