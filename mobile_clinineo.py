#!/usr/bin/env python3
"""
Mobile Clinineo - تطبيق محلي للموبايل
تطبيق يعمل محلياً على الهاتف بدون شبكة
"""

import json
import os
import uuid
from datetime import datetime

class MobileClinineo:
    def __init__(self):
        self.data_file = "mobile_data.json"
        self.load_data()
    
    def load_data(self):
        """تحميل البيانات"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
            except:
                self.data = {"patients": [], "prescriptions": [], "notifications": []}
        else:
            self.data = {"patients": [], "prescriptions": [], "notifications": []}
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def add_patient(self, name, phone, notes=""):
        """إضافة مريض جديد"""
        patient = {
            "id": str(uuid.uuid4())[:8],
            "name": name,
            "phone": phone,
            "notes": notes,
            "created_at": datetime.now().isoformat()
        }
        
        self.data["patients"].insert(0, patient)
        
        # إضافة تنبيه
        notification = {
            "id": str(uuid.uuid4())[:8],
            "message": f"مريض جديد: {name}",
            "type": "new_patient",
            "created_at": datetime.now().isoformat()
        }
        
        self.data["notifications"].insert(0, notification)
        self.save_data()
        return patient["id"]
    
    def add_prescription(self, patient_id, diagnosis, medications, instructions=""):
        """إضافة روشتة"""
        # البحث عن المريض
        patient_name = "غير معروف"
        for patient in self.data["patients"]:
            if patient["id"] == patient_id:
                patient_name = patient["name"]
                break
        
        prescription = {
            "id": str(uuid.uuid4())[:8],
            "patient_id": patient_id,
            "patient_name": patient_name,
            "diagnosis": diagnosis,
            "medications": medications,
            "instructions": instructions,
            "doctor": "د. أحمد محمد",
            "created_at": datetime.now().isoformat()
        }
        
        self.data["prescriptions"].insert(0, prescription)
        self.save_data()
        return prescription["id"]
    
    def get_patients(self):
        """الحصول على قائمة المرضى"""
        return self.data["patients"]
    
    def get_prescriptions(self):
        """الحصول على قائمة الروشتات"""
        return self.data["prescriptions"]
    
    def get_notifications(self):
        """الحصول على التنبيهات"""
        return self.data["notifications"]
    
    def get_stats(self):
        """الحصول على الإحصائيات"""
        today = datetime.now().strftime('%Y-%m-%d')
        
        return {
            "total_patients": len(self.data["patients"]),
            "total_prescriptions": len(self.data["prescriptions"]),
            "today_patients": len([p for p in self.data["patients"] if p["created_at"].startswith(today)]),
            "unread_notifications": len([n for n in self.data["notifications"] if not n.get("read", False)])
        }

def print_header(title):
    """طباعة رأس الصفحة"""
    print("\n" + "="*50)
    print(f"🏥 {title}")
    print("="*50)

def print_menu(options):
    """طباعة القائمة"""
    for i, option in enumerate(options, 1):
        print(f"{i}. {option}")
    print("0. خروج")

def registrar_app(clinic):
    """تطبيق الاستقبال"""
    while True:
        print_header("تطبيق الاستقبال")
        
        stats = clinic.get_stats()
        print(f"📊 الإحصائيات:")
        print(f"   👥 إجمالي المرضى: {stats['total_patients']}")
        print(f"   📅 مرضى اليوم: {stats['today_patients']}")
        
        print("\n📋 القائمة:")
        print_menu([
            "➕ إضافة مريض جديد",
            "👥 عرض جميع المرضى",
            "🔍 البحث عن مريض"
        ])
        
        choice = input("\n🔢 اختر رقم: ").strip()
        
        if choice == "1":
            # إضافة مريض
            print("\n➕ إضافة مريض جديد:")
            name = input("الاسم الكامل: ").strip()
            phone = input("رقم الهاتف: ").strip()
            notes = input("ملاحظات (اختياري): ").strip()
            
            if name and phone:
                patient_id = clinic.add_patient(name, phone, notes)
                print(f"✅ تم إضافة المريض بنجاح! (ID: {patient_id})")
            else:
                print("❌ الاسم ورقم الهاتف مطلوبان!")
        
        elif choice == "2":
            # عرض المرضى
            patients = clinic.get_patients()
            print(f"\n👥 جميع المرضى ({len(patients)}):")
            
            if patients:
                for i, patient in enumerate(patients[:10], 1):
                    print(f"{i}. 👤 {patient['name']}")
                    print(f"   📞 {patient['phone']}")
                    print(f"   📅 {patient['created_at'][:10]}")
                    if patient.get('notes'):
                        print(f"   📝 {patient['notes']}")
                    print()
            else:
                print("لا توجد مرضى مسجلين")
        
        elif choice == "3":
            # البحث
            search_term = input("\n🔍 ابحث عن (الاسم أو الهاتف): ").strip()
            patients = clinic.get_patients()
            
            found = []
            for patient in patients:
                if search_term.lower() in patient['name'].lower() or search_term in patient['phone']:
                    found.append(patient)
            
            print(f"\n🔍 نتائج البحث ({len(found)}):")
            for patient in found:
                print(f"👤 {patient['name']} - 📞 {patient['phone']}")
        
        elif choice == "0":
            break
        
        input("\n⏸️ اضغط Enter للمتابعة...")

def doctor_app(clinic):
    """تطبيق الطبيب"""
    while True:
        print_header("تطبيق الطبيب - د. أحمد محمد")
        
        stats = clinic.get_stats()
        print(f"📊 الإحصائيات:")
        print(f"   👥 إجمالي المرضى: {stats['total_patients']}")
        print(f"   📝 إجمالي الروشتات: {stats['total_prescriptions']}")
        print(f"   🔔 تنبيهات جديدة: {stats['unread_notifications']}")
        
        print("\n📋 القائمة:")
        print_menu([
            "🔔 التنبيهات",
            "👥 المرضى",
            "📝 كتابة روشتة",
            "📋 عرض الروشتات",
            "📊 التقارير"
        ])
        
        choice = input("\n🔢 اختر رقم: ").strip()
        
        if choice == "1":
            # التنبيهات
            notifications = clinic.get_notifications()
            print(f"\n🔔 التنبيهات ({len(notifications)}):")
            
            for notification in notifications[:5]:
                print(f"• {notification['message']}")
                print(f"  📅 {notification['created_at'][:16].replace('T', ' ')}")
                print()
        
        elif choice == "2":
            # المرضى
            patients = clinic.get_patients()
            print(f"\n👥 المرضى ({len(patients)}):")
            
            for i, patient in enumerate(patients[:10], 1):
                print(f"{i}. 👤 {patient['name']} - 📞 {patient['phone']}")
        
        elif choice == "3":
            # كتابة روشتة
            patients = clinic.get_patients()
            if not patients:
                print("❌ لا توجد مرضى! أضف مريض أولاً.")
                continue
            
            print("\n📝 كتابة روشتة جديدة:")
            
            # اختيار المريض
            print("👥 اختر مريض:")
            for i, patient in enumerate(patients[:10], 1):
                print(f"{i}. {patient['name']}")
            
            try:
                patient_idx = int(input("رقم المريض: ")) - 1
                if 0 <= patient_idx < len(patients):
                    selected_patient = patients[patient_idx]
                    
                    # بيانات الروشتة
                    diagnosis = input("التشخيص: ").strip()
                    
                    medications = []
                    print("\n💊 الأدوية (اتركه فارغ للانتهاء):")
                    while True:
                        med_name = input(f"دواء {len(medications)+1}: ").strip()
                        if not med_name:
                            break
                        dosage = input("الجرعة: ").strip()
                        medications.append(f"{med_name} - {dosage}")
                    
                    instructions = input("تعليمات إضافية: ").strip()
                    
                    if diagnosis:
                        prescription_id = clinic.add_prescription(
                            selected_patient['id'], 
                            diagnosis, 
                            medications, 
                            instructions
                        )
                        print(f"✅ تم حفظ الروشتة! (ID: {prescription_id})")
                    else:
                        print("❌ التشخيص مطلوب!")
                else:
                    print("❌ رقم مريض غير صحيح!")
            except ValueError:
                print("❌ يرجى إدخال رقم صحيح!")
        
        elif choice == "4":
            # عرض الروشتات
            prescriptions = clinic.get_prescriptions()
            print(f"\n📋 الروشتات ({len(prescriptions)}):")
            
            for prescription in prescriptions[:5]:
                print(f"👤 المريض: {prescription['patient_name']}")
                print(f"🩺 التشخيص: {prescription['diagnosis']}")
                print(f"💊 الأدوية:")
                for med in prescription['medications']:
                    print(f"   • {med}")
                if prescription['instructions']:
                    print(f"📝 تعليمات: {prescription['instructions']}")
                print(f"📅 التاريخ: {prescription['created_at'][:10]}")
                print("-" * 30)
        
        elif choice == "5":
            # التقارير
            print(f"\n📊 تقرير شامل:")
            print(f"👥 إجمالي المرضى: {stats['total_patients']}")
            print(f"📝 إجمالي الروشتات: {stats['total_prescriptions']}")
            
            # أكثر الأدوية وصفاً
            all_meds = []
            for prescription in clinic.get_prescriptions():
                all_meds.extend(prescription['medications'])
            
            if all_meds:
                print(f"\n💊 إجمالي الأدوية الموصوفة: {len(all_meds)}")
        
        elif choice == "0":
            break
        
        input("\n⏸️ اضغط Enter للمتابعة...")

def main():
    """الدالة الرئيسية"""
    clinic = MobileClinineo()
    
    while True:
        print_header("Clinineo - نظام إدارة العيادة")
        print("🏥 مرحباً بك في نظام Clinineo المحلي")
        
        print("\n📱 اختر التطبيق:")
        print_menu([
            "🏥 تطبيق الاستقبال",
            "🩺 تطبيق الطبيب"
        ])
        
        choice = input("\n🔢 اختر رقم: ").strip()
        
        if choice == "1":
            registrar_app(clinic)
        elif choice == "2":
            doctor_app(clinic)
        elif choice == "0":
            print("\n👋 شكراً لاستخدام Clinineo!")
            break
        else:
            print("❌ اختيار غير صحيح!")

if __name__ == "__main__":
    main()
