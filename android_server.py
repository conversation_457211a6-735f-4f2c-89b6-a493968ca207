#!/usr/bin/env python3
"""
Simple Android Server for Clinic Management
خادم بسيط لتطبيقات الأندرويد - إدارة العيادة
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import sqlite3
import uuid
from datetime import datetime, date
from urllib.parse import urlparse, parse_qs
import socket
import threading
import os

class ClinicDatabase:
    """قاعدة بيانات العيادة"""
    
    def __init__(self, db_path="android_clinic.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                role TEXT NOT NULL,
                phone TEXT,
                specialization TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المرضى
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id TEXT PRIMARY KEY,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                date_of_birth DATE NOT NULL,
                gender TEXT NOT NULL,
                phone TEXT NOT NULL,
                email TEXT,
                address TEXT,
                emergency_contact TEXT,
                blood_type TEXT,
                allergies TEXT,
                medical_history TEXT,
                insurance_number TEXT,
                created_by TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التنبيهات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                type TEXT DEFAULT 'info',
                is_read INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج مستخدمين افتراضيين
        cursor.execute('''
            INSERT OR IGNORE INTO users (id, email, password, first_name, last_name, role, specialization)
            VALUES 
            ('1', '<EMAIL>', 'admin123', 'مدير', 'النظام', 'admin', NULL),
            ('2', '<EMAIL>', 'doctor123', 'د. أحمد', 'محمد', 'doctor', 'طب عام'),
            ('3', '<EMAIL>', 'registrar123', 'سارة', 'أحمد', 'registrar', NULL)
        ''')
        
        conn.commit()
        conn.close()
        print("✅ تم إنشاء قاعدة البيانات")
    
    def execute_query(self, query, params=None, fetch=False):
        """تنفيذ استعلام"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch:
                result = cursor.fetchall()
                return [dict(row) for row in result]
            else:
                conn.commit()
                return cursor.lastrowid
        finally:
            conn.close()

class AndroidClinicHandler(BaseHTTPRequestHandler):
    """معالج طلبات تطبيقات الأندرويد"""
    
    def __init__(self, *args, **kwargs):
        self.db = ClinicDatabase()
        super().__init__(*args, **kwargs)
    
    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def do_GET(self):
        """معالجة طلبات GET"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            self.send_json_response({
                "message": "مرحباً بك في خادم Clinineo للأندرويد",
                "status": "working",
                "server_ip": self.get_server_ip(),
                "version": "1.0.0"
            })
        
        elif path == '/api/health':
            self.send_json_response({
                "status": "healthy",
                "message": "الخادم يعمل بشكل طبيعي",
                "timestamp": datetime.now().isoformat()
            })
        
        elif path == '/api/patients':
            self.get_patients()
        
        elif path == '/api/notifications':
            self.get_notifications()
        
        elif path == '/api/dashboard':
            self.get_dashboard_stats()
        
        else:
            self.send_error(404, "Not Found")
    
    def do_POST(self):
        """معالجة طلبات POST"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # قراءة البيانات
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode('utf-8'))
            except:
                data = {}
        else:
            data = {}
        
        if path == '/api/auth/login':
            self.handle_login(data)
        
        elif path == '/api/patients':
            self.create_patient(data)
        
        elif path == '/api/notifications/read':
            self.mark_notification_read(data)
        
        else:
            self.send_error(404, "Not Found")
    
    def handle_login(self, data):
        """معالجة تسجيل الدخول"""
        email = data.get('email', '')
        password = data.get('password', '')
        
        users = self.db.execute_query(
            "SELECT * FROM users WHERE email = ? AND password = ? AND is_active = 1",
            (email, password),
            fetch=True
        )
        
        if users:
            user = users[0]
            response = {
                "success": True,
                "access_token": f"android-token-{user['id']}",
                "user": {
                    "id": user['id'],
                    "email": user['email'],
                    "first_name": user['first_name'],
                    "last_name": user['last_name'],
                    "role": user['role'],
                    "specialization": user['specialization']
                }
            }
        else:
            response = {
                "success": False,
                "message": "بيانات تسجيل الدخول غير صحيحة"
            }
        
        self.send_json_response(response)
    
    def create_patient(self, data):
        """إنشاء مريض جديد"""
        try:
            patient_id = str(uuid.uuid4())
            
            self.db.execute_query('''
                INSERT INTO patients (id, first_name, last_name, date_of_birth, gender, phone, 
                                    email, address, emergency_contact, blood_type, allergies, 
                                    medical_history, insurance_number, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                patient_id,
                data.get('first_name', ''),
                data.get('last_name', ''),
                data.get('date_of_birth', ''),
                data.get('gender', ''),
                data.get('phone', ''),
                data.get('email', ''),
                data.get('address', ''),
                data.get('emergency_contact', ''),
                data.get('blood_type', ''),
                data.get('allergies', ''),
                data.get('medical_history', ''),
                data.get('insurance_number', ''),
                data.get('created_by', '')
            ))
            
            # إرسال تنبيه للأطباء
            self.notify_doctors(f"تم تسجيل مريض جديد: {data.get('first_name', '')} {data.get('last_name', '')}")
            
            response = {
                "success": True,
                "message": "تم إضافة المريض بنجاح",
                "patient_id": patient_id
            }
        except Exception as e:
            response = {
                "success": False,
                "message": f"خطأ في إضافة المريض: {str(e)}"
            }
        
        self.send_json_response(response)
    
    def get_patients(self):
        """الحصول على قائمة المرضى"""
        try:
            patients = self.db.execute_query(
                "SELECT * FROM patients ORDER BY created_at DESC LIMIT 50",
                fetch=True
            )
            response = {
                "success": True,
                "data": patients
            }
        except Exception as e:
            response = {
                "success": False,
                "message": f"خطأ في تحميل المرضى: {str(e)}"
            }
        
        self.send_json_response(response)
    
    def get_notifications(self):
        """الحصول على التنبيهات"""
        query_params = parse_qs(urlparse(self.path).query)
        user_id = query_params.get('user_id', [None])[0]
        unread_only = query_params.get('unread_only', ['false'])[0].lower() == 'true'
        
        if not user_id:
            self.send_json_response({
                "success": False,
                "message": "معرف المستخدم مطلوب"
            })
            return
        
        try:
            query = "SELECT * FROM notifications WHERE user_id = ?"
            params = [user_id]
            
            if unread_only:
                query += " AND is_read = 0"
            
            query += " ORDER BY created_at DESC LIMIT 20"
            
            notifications = self.db.execute_query(query, params, fetch=True)
            
            response = {
                "success": True,
                "data": notifications
            }
        except Exception as e:
            response = {
                "success": False,
                "message": f"خطأ في تحميل التنبيهات: {str(e)}"
            }
        
        self.send_json_response(response)
    
    def mark_notification_read(self, data):
        """تمييز التنبيه كمقروء"""
        notification_id = data.get('notification_id')
        
        if not notification_id:
            self.send_json_response({
                "success": False,
                "message": "معرف التنبيه مطلوب"
            })
            return
        
        try:
            self.db.execute_query(
                "UPDATE notifications SET is_read = 1 WHERE id = ?",
                (notification_id,)
            )
            
            response = {
                "success": True,
                "message": "تم تمييز التنبيه كمقروء"
            }
        except Exception as e:
            response = {
                "success": False,
                "message": f"خطأ في تحديث التنبيه: {str(e)}"
            }
        
        self.send_json_response(response)
    
    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة المعلومات"""
        try:
            today = date.today().isoformat()
            
            # إجمالي المرضى
            total_patients = self.db.execute_query(
                "SELECT COUNT(*) as count FROM patients", fetch=True
            )[0]['count']
            
            # الأطباء النشطين
            active_doctors = self.db.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE role = 'doctor' AND is_active = 1",
                fetch=True
            )[0]['count']
            
            # التنبيهات غير المقروءة
            unread_notifications = self.db.execute_query(
                "SELECT COUNT(*) as count FROM notifications WHERE is_read = 0",
                fetch=True
            )[0]['count']
            
            stats = {
                "total_patients": total_patients,
                "active_doctors": active_doctors,
                "unread_notifications": unread_notifications,
                "today_appointments": 0,
                "server_status": "online"
            }
            
            response = {
                "success": True,
                "data": stats
            }
        except Exception as e:
            response = {
                "success": False,
                "message": f"خطأ في تحميل الإحصائيات: {str(e)}"
            }
        
        self.send_json_response(response)
    
    def notify_doctors(self, message):
        """إرسال تنبيه للأطباء"""
        try:
            doctors = self.db.execute_query(
                "SELECT id FROM users WHERE role = 'doctor' AND is_active = 1",
                fetch=True
            )
            
            for doctor in doctors:
                notification_id = str(uuid.uuid4())
                self.db.execute_query('''
                    INSERT INTO notifications (id, user_id, title, message, type)
                    VALUES (?, ?, ?, ?, ?)
                ''', (notification_id, doctor['id'], "مريض جديد", message, "info"))
                
        except Exception as e:
            print(f"خطأ في إرسال التنبيهات: {e}")
    
    def send_json_response(self, data):
        """إرسال استجابة JSON"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def get_server_ip(self):
        """الحصول على عنوان IP للخادم"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def log_message(self, format, *args):
        """تسجيل الرسائل"""
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {format % args}")

def start_android_server(port=8080):
    """بدء تشغيل الخادم"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, AndroidClinicHandler)
    
    # الحصول على عنوان IP
    hostname = socket.gethostname()
    try:
        local_ip = socket.gethostbyname(hostname)
    except:
        local_ip = "127.0.0.1"
    
    print("📱 Clinineo Android Server")
    print("=" * 50)
    print(f"✅ الخادم يعمل على:")
    print(f"   📍 المحلي: http://127.0.0.1:{port}")
    print(f"   🌐 الشبكة: http://{local_ip}:{port}")
    print(f"   🔍 فحص الصحة: http://{local_ip}:{port}/api/health")
    print("=" * 50)
    print("📱 استخدم عنوان الشبكة في تطبيقات الأندرويد:")
    print(f"   Server URL: http://{local_ip}:{port}")
    print("=" * 50)
    print("🛑 اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
        httpd.shutdown()

if __name__ == "__main__":
    start_android_server()
