const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const { authenticateToken, anyAuthenticated, adminOnly } = require('../middleware/auth');

const router = express.Router();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// @route   GET /api/reports/dashboard
// @desc    Get dashboard statistics
// @access  Private
router.get('/dashboard', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7);

    // Get total patients
    const { count: totalPatients } = await supabase
      .from('patients')
      .select('*', { count: 'exact', head: true });

    // Get today's appointments
    const { count: todayAppointments } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('appointment_date', today)
      .neq('status', 'cancelled');

    // Get this month's appointments
    const { count: monthlyAppointments } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .gte('appointment_date', `${thisMonth}-01`)
      .lt('appointment_date', `${thisMonth}-32`)
      .neq('status', 'cancelled');

    // Get active doctors
    const { count: activeDoctors } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('role', 'doctor')
      .eq('is_active', true);

    // Get pending appointments
    const { count: pendingAppointments } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'scheduled')
      .gte('appointment_date', today);

    // Get completed appointments this month
    const { count: completedAppointments } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'completed')
      .gte('appointment_date', `${thisMonth}-01`)
      .lt('appointment_date', `${thisMonth}-32`);

    res.json({
      success: true,
      data: {
        overview: {
          totalPatients: totalPatients || 0,
          todayAppointments: todayAppointments || 0,
          monthlyAppointments: monthlyAppointments || 0,
          activeDoctors: activeDoctors || 0,
          pendingAppointments: pendingAppointments || 0,
          completedAppointments: completedAppointments || 0
        }
      }
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/reports/diseases
// @desc    Get disease statistics
// @access  Private
router.get('/diseases', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;
    const limit = parseInt(req.query.limit) || 10;

    let query = supabase
      .from('medical_records')
      .select('diagnosis');

    // Apply date filters if provided
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    const { data: records, error } = await query;

    if (error) {
      console.error('Get diseases error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch disease statistics'
      });
    }

    // Count diagnoses
    const diseaseCount = {};
    records.forEach(record => {
      if (record.diagnosis) {
        const diagnosis = record.diagnosis.toLowerCase().trim();
        diseaseCount[diagnosis] = (diseaseCount[diagnosis] || 0) + 1;
      }
    });

    // Sort by count and limit results
    const sortedDiseases = Object.entries(diseaseCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, limit)
      .map(([disease, count]) => ({
        disease: disease.charAt(0).toUpperCase() + disease.slice(1),
        count,
        percentage: ((count / records.length) * 100).toFixed(1)
      }));

    res.json({
      success: true,
      data: {
        diseases: sortedDiseases,
        totalRecords: records.length
      }
    });
  } catch (error) {
    console.error('Disease statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/reports/treatments
// @desc    Get treatment statistics
// @access  Private
router.get('/treatments', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;
    const limit = parseInt(req.query.limit) || 10;

    let query = supabase
      .from('medical_records')
      .select('treatment, medications');

    // Apply date filters if provided
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    const { data: records, error } = await query;

    if (error) {
      console.error('Get treatments error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch treatment statistics'
      });
    }

    // Count treatments
    const treatmentCount = {};
    const medicationCount = {};

    records.forEach(record => {
      // Count treatments
      if (record.treatment) {
        const treatment = record.treatment.toLowerCase().trim();
        treatmentCount[treatment] = (treatmentCount[treatment] || 0) + 1;
      }

      // Count medications
      if (record.medications) {
        try {
          const medications = JSON.parse(record.medications);
          medications.forEach(med => {
            if (med.name) {
              const medName = med.name.toLowerCase().trim();
              medicationCount[medName] = (medicationCount[medName] || 0) + 1;
            }
          });
        } catch (e) {
          // Skip invalid JSON
        }
      }
    });

    // Sort and limit treatments
    const sortedTreatments = Object.entries(treatmentCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, limit)
      .map(([treatment, count]) => ({
        treatment: treatment.charAt(0).toUpperCase() + treatment.slice(1),
        count,
        percentage: ((count / records.length) * 100).toFixed(1)
      }));

    // Sort and limit medications
    const sortedMedications = Object.entries(medicationCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, limit)
      .map(([medication, count]) => ({
        medication: medication.charAt(0).toUpperCase() + medication.slice(1),
        count,
        percentage: ((count / records.length) * 100).toFixed(1)
      }));

    res.json({
      success: true,
      data: {
        treatments: sortedTreatments,
        medications: sortedMedications,
        totalRecords: records.length
      }
    });
  } catch (error) {
    console.error('Treatment statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/reports/appointments
// @desc    Get appointment statistics
// @access  Private
router.get('/appointments', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    let query = supabase
      .from('appointments')
      .select('status, type, appointment_date, doctor_id');

    // Apply date filters if provided
    if (startDate) {
      query = query.gte('appointment_date', startDate);
    }
    if (endDate) {
      query = query.lte('appointment_date', endDate);
    }

    // If user is a doctor, only show their appointments
    if (req.user.role === 'doctor') {
      query = query.eq('doctor_id', req.user.id);
    }

    const { data: appointments, error } = await query;

    if (error) {
      console.error('Get appointment stats error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch appointment statistics'
      });
    }

    // Count by status
    const statusCount = {};
    const typeCount = {};
    const dailyCount = {};

    appointments.forEach(appointment => {
      // Count by status
      statusCount[appointment.status] = (statusCount[appointment.status] || 0) + 1;
      
      // Count by type
      typeCount[appointment.type] = (typeCount[appointment.type] || 0) + 1;
      
      // Count by date
      const date = appointment.appointment_date;
      dailyCount[date] = (dailyCount[date] || 0) + 1;
    });

    // Convert to arrays and sort
    const statusStats = Object.entries(statusCount).map(([status, count]) => ({
      status,
      count,
      percentage: ((count / appointments.length) * 100).toFixed(1)
    }));

    const typeStats = Object.entries(typeCount).map(([type, count]) => ({
      type,
      count,
      percentage: ((count / appointments.length) * 100).toFixed(1)
    }));

    const dailyStats = Object.entries(dailyCount)
      .sort(([a], [b]) => new Date(a) - new Date(b))
      .map(([date, count]) => ({ date, count }));

    res.json({
      success: true,
      data: {
        byStatus: statusStats,
        byType: typeStats,
        daily: dailyStats,
        totalAppointments: appointments.length
      }
    });
  } catch (error) {
    console.error('Appointment statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
