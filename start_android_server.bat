@echo off
echo ========================================
echo    Clinineo Android Server
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

REM التحقق من وجود الملف المطلوب
if not exist "android_server.py" (
    echo ❌ ملف android_server.py غير موجود
    echo يرجى وضع الملف في نفس المجلد
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة
echo 🚀 تشغيل خادم الأندرويد...
echo.
echo 📱 استخدم عنوان الشبكة في تطبيقات الأندرويد
echo 🌐 مثال: http://192.168.1.100:8080
echo.

REM تشغيل الخادم
python android_server.py

echo.
echo 👋 تم إغلاق الخادم
pause
