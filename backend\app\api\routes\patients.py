"""
Patient management routes
مسارات إدارة المرضى
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from ...core.database import get_db, Patient, User
from ...core.security import get_current_active_user, require_registrar_or_admin, require_any_role
from ...schemas.patient import PatientCreate, PatientUpdate, PatientResponse, PatientSearch

router = APIRouter()

@router.post("/", response_model=PatientResponse)
async def create_patient(
    patient: PatientCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_registrar_or_admin)
):
    """
    Create a new patient
    إنشاء مريض جديد
    """
    # Check if patient with same phone already exists
    existing_patient = db.query(Patient).filter(Patient.phone == patient.phone).first()
    if existing_patient:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Patient with this phone number already exists"
        )
    
    # Create new patient
    db_patient = Patient(
        **patient.dict(),
        created_by=current_user.id
    )
    db.add(db_patient)
    db.commit()
    db.refresh(db_patient)
    
    # Send real-time notification
    if hasattr(request.app.state, 'websocket_manager'):
        websocket_manager = request.app.state.websocket_manager
        patient_data = {
            "id": db_patient.id,
            "first_name": db_patient.first_name,
            "last_name": db_patient.last_name,
            "phone": db_patient.phone,
            "gender": db_patient.gender
        }
        registered_by = {
            "id": current_user.id,
            "name": f"{current_user.first_name} {current_user.last_name}"
        }
        await websocket_manager.notify_patient_registered(patient_data, registered_by)
    
    return PatientResponse.from_orm(db_patient)

@router.get("/", response_model=List[PatientResponse])
async def get_patients(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    gender: Optional[str] = Query(None),
    blood_type: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """
    Get patients with filtering and pagination
    الحصول على المرضى مع التصفية والتقسيم
    """
    query = db.query(Patient)
    
    # Apply filters
    if search:
        query = query.filter(
            or_(
                Patient.first_name.ilike(f"%{search}%"),
                Patient.last_name.ilike(f"%{search}%"),
                Patient.phone.ilike(f"%{search}%")
            )
        )
    
    if gender:
        query = query.filter(Patient.gender == gender)
    
    if blood_type:
        query = query.filter(Patient.blood_type == blood_type)
    
    # Apply pagination
    patients = query.offset(skip).limit(limit).all()
    
    return [PatientResponse.from_orm(patient) for patient in patients]

@router.get("/{patient_id}", response_model=PatientResponse)
async def get_patient(
    patient_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """
    Get patient by ID
    الحصول على مريض بالمعرف
    """
    patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )
    
    return PatientResponse.from_orm(patient)

@router.put("/{patient_id}", response_model=PatientResponse)
async def update_patient(
    patient_id: str,
    patient_update: PatientUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_registrar_or_admin)
):
    """
    Update patient information
    تحديث معلومات المريض
    """
    patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )
    
    # Update patient fields
    update_data = patient_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(patient, field, value)
    
    db.commit()
    db.refresh(patient)
    
    return PatientResponse.from_orm(patient)

@router.delete("/{patient_id}")
async def delete_patient(
    patient_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_registrar_or_admin)
):
    """
    Delete patient (soft delete by deactivating)
    حذف المريض (حذف ناعم بإلغاء التفعيل)
    """
    patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )
    
    # For now, we'll do a hard delete
    # In production, consider soft delete
    db.delete(patient)
    db.commit()
    
    return {"message": "Patient deleted successfully"}

@router.get("/search/advanced", response_model=List[PatientResponse])
async def advanced_patient_search(
    search_params: PatientSearch = Depends(),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """
    Advanced patient search
    البحث المتقدم عن المرضى
    """
    query = db.query(Patient)
    
    # Apply search filters
    if search_params.query:
        query = query.filter(
            or_(
                Patient.first_name.ilike(f"%{search_params.query}%"),
                Patient.last_name.ilike(f"%{search_params.query}%"),
                Patient.phone.ilike(f"%{search_params.query}%"),
                Patient.email.ilike(f"%{search_params.query}%")
            )
        )
    
    if search_params.gender:
        query = query.filter(Patient.gender == search_params.gender)
    
    if search_params.blood_type:
        query = query.filter(Patient.blood_type == search_params.blood_type)
    
    # Age filtering would require calculating age from date_of_birth
    # This is a simplified version
    
    patients = query.offset(skip).limit(limit).all()
    
    return [PatientResponse.from_orm(patient) for patient in patients]
