const jwt = require('jsonwebtoken');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', decoded.userId)
      .single();

    if (error || !user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(403).json({
      success: false,
      message: 'Invalid or expired token'
    });
  }
};

// Check user role
const authorizeRole = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

// Admin only middleware
const adminOnly = authorizeRole('admin');

// Doctor only middleware
const doctorOnly = authorizeRole('doctor');

// Registrar only middleware
const registrarOnly = authorizeRole('registrar');

// Doctor or Admin middleware
const doctorOrAdmin = authorizeRole('doctor', 'admin');

// Registrar or Admin middleware
const registrarOrAdmin = authorizeRole('registrar', 'admin');

// Any authenticated user
const anyAuthenticated = authorizeRole('admin', 'doctor', 'registrar');

module.exports = {
  authenticateToken,
  authorizeRole,
  adminOnly,
  doctorOnly,
  registrarOnly,
  doctorOrAdmin,
  registrarOrAdmin,
  anyAuthenticated
};
