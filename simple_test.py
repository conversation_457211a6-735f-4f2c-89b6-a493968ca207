#!/usr/bin/env python3
"""
Simple test to verify Python and basic functionality
اختبار بسيط للتحقق من Python والوظائف الأساسية
"""

print("🚀 Testing Clinineo System...")
print("=" * 50)

# Test 1: Python version
import sys
print(f"✅ Python version: {sys.version}")

# Test 2: Required modules
try:
    import json
    print("✅ JSON module available")
except ImportError:
    print("❌ JSON module not available")

try:
    import sqlite3
    print("✅ SQLite3 module available")
except ImportError:
    print("❌ SQLite3 module not available")

try:
    import tkinter
    print("✅ Tkinter module available")
except ImportError:
    print("❌ Tkinter module not available")

# Test 3: Create simple FastAPI app
try:
    from fastapi import FastAPI
    app = FastAPI(title="Clinineo Test")
    
    @app.get("/")
    def read_root():
        return {"message": "مرحباً بك في نظام Clinineo", "status": "working"}
    
    @app.get("/health")
    def health_check():
        return {"status": "healthy", "message": "النظام يعمل بشكل طبيعي"}
    
    print("✅ FastAPI app created successfully")
    
    # Test 4: Start server
    import uvicorn
    print("✅ Uvicorn available")
    
    print("\n🚀 Starting test server...")
    print("📍 Server will be available at: http://127.0.0.1:8000")
    print("📚 API Documentation: http://127.0.0.1:8000/docs")
    print("🔍 Health Check: http://127.0.0.1:8000/health")
    print("=" * 50)
    print("Press Ctrl+C to stop the server")
    
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
    
except ImportError as e:
    print(f"❌ FastAPI/Uvicorn not available: {e}")
    print("📦 Installing FastAPI and Uvicorn...")
    
    import subprocess
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'fastapi', 'uvicorn'])
        print("✅ FastAPI and Uvicorn installed successfully")
        print("🔄 Please run this script again")
    except subprocess.CalledProcessError:
        print("❌ Failed to install FastAPI and Uvicorn")
        print("Please install manually: pip install fastapi uvicorn")

except Exception as e:
    print(f"❌ Error: {e}")

print("\n👋 Test completed")
