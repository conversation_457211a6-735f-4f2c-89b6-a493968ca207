#!/usr/bin/env python3
"""
Android Registrar App using Kivy
تطبيق موظف الاستقبال للأندرويد باستخدام Kivy
"""

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.popup import Popup
from kivy.uix.spinner import Spinner
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
import requests
import json
from datetime import datetime

class RegistrarApp(App):
    """تطبيق موظف الاستقبال للأندرويد"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.server_url = "http://192.168.1.100:8080"  # عنوان الخادم
        self.current_user = None
        self.access_token = None
    
    def build(self):
        """بناء واجهة التطبيق"""
        self.title = "Clinineo - موظف الاستقبال"
        
        # التخطيط الرئيسي
        main_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # شريط العنوان
        title_label = Label(
            text='نظام إدارة العيادة - موظف الاستقبال',
            size_hint_y=None,
            height=50,
            font_size=20
        )
        main_layout.add_widget(title_label)
        
        # شريط الحالة
        self.status_label = Label(
            text='غير متصل بالخادم',
            size_hint_y=None,
            height=30,
            color=(1, 0, 0, 1)  # أحمر
        )
        main_layout.add_widget(self.status_label)
        
        # منطقة المحتوى
        self.content_layout = BoxLayout(orientation='vertical', spacing=10)
        main_layout.add_widget(self.content_layout)
        
        # عرض شاشة تسجيل الدخول
        self.show_login_screen()
        
        # فحص الاتصال بالخادم
        Clock.schedule_once(self.check_server_connection, 1)
        
        return main_layout
    
    def show_login_screen(self):
        """عرض شاشة تسجيل الدخول"""
        self.content_layout.clear_widgets()
        
        login_layout = GridLayout(cols=1, spacing=10, size_hint_y=None)
        login_layout.bind(minimum_height=login_layout.setter('height'))
        
        # حقول تسجيل الدخول
        login_layout.add_widget(Label(text='تسجيل الدخول', font_size=18))
        
        self.email_input = TextInput(
            hint_text='البريد الإلكتروني',
            text='<EMAIL>',
            multiline=False,
            size_hint_y=None,
            height=40
        )
        login_layout.add_widget(self.email_input)
        
        self.password_input = TextInput(
            hint_text='كلمة المرور',
            text='registrar123',
            password=True,
            multiline=False,
            size_hint_y=None,
            height=40
        )
        login_layout.add_widget(self.password_input)
        
        # أزرار
        buttons_layout = GridLayout(cols=2, spacing=10, size_hint_y=None, height=50)
        
        login_btn = Button(text='تسجيل دخول')
        login_btn.bind(on_press=self.login)
        buttons_layout.add_widget(login_btn)
        
        settings_btn = Button(text='إعدادات الخادم')
        settings_btn.bind(on_press=self.show_server_settings)
        buttons_layout.add_widget(settings_btn)
        
        login_layout.add_widget(buttons_layout)
        
        # إضافة إلى التمرير
        scroll = ScrollView()
        scroll.add_widget(login_layout)
        self.content_layout.add_widget(scroll)
    
    def show_server_settings(self, instance):
        """عرض إعدادات الخادم"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        content.add_widget(Label(text='عنوان الخادم:', size_hint_y=None, height=30))
        
        server_input = TextInput(
            text=self.server_url,
            multiline=False,
            size_hint_y=None,
            height=40
        )
        content.add_widget(server_input)
        
        buttons_layout = BoxLayout(spacing=10, size_hint_y=None, height=50)
        
        save_btn = Button(text='حفظ')
        save_btn.bind(on_press=lambda x: self.save_server_settings(server_input.text, popup))
        buttons_layout.add_widget(save_btn)
        
        cancel_btn = Button(text='إلغاء')
        cancel_btn.bind(on_press=lambda x: popup.dismiss())
        buttons_layout.add_widget(cancel_btn)
        
        content.add_widget(buttons_layout)
        
        popup = Popup(
            title='إعدادات الخادم',
            content=content,
            size_hint=(0.8, 0.6)
        )
        popup.open()
    
    def save_server_settings(self, server_url, popup):
        """حفظ إعدادات الخادم"""
        self.server_url = server_url.strip()
        popup.dismiss()
        self.show_popup('تم حفظ إعدادات الخادم')
        Clock.schedule_once(self.check_server_connection, 0.5)
    
    def check_server_connection(self, dt=None):
        """فحص الاتصال بالخادم"""
        try:
            response = requests.get(f"{self.server_url}/api/health", timeout=3)
            if response.status_code == 200:
                self.status_label.text = f'متصل بالخادم: {self.server_url}'
                self.status_label.color = (0, 1, 0, 1)  # أخضر
            else:
                self.status_label.text = 'خطأ في الاتصال بالخادم'
                self.status_label.color = (1, 0, 0, 1)  # أحمر
        except:
            self.status_label.text = 'غير متصل بالخادم'
            self.status_label.color = (1, 0, 0, 1)  # أحمر
    
    def login(self, instance):
        """تسجيل الدخول"""
        email = self.email_input.text.strip()
        password = self.password_input.text.strip()
        
        if not email or not password:
            self.show_popup('يرجى ملء جميع الحقول')
            return
        
        try:
            response = requests.post(
                f"{self.server_url}/api/auth/login",
                json={"email": email, "password": password},
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.current_user = data["user"]
                    self.access_token = data["access_token"]
                    self.show_main_screen()
                else:
                    self.show_popup(data.get("message", "فشل تسجيل الدخول"))
            else:
                self.show_popup("خطأ في الاتصال بالخادم")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def show_main_screen(self):
        """عرض الشاشة الرئيسية"""
        self.content_layout.clear_widgets()
        
        main_layout = GridLayout(cols=1, spacing=10, size_hint_y=None)
        main_layout.bind(minimum_height=main_layout.setter('height'))
        
        # ترحيب
        welcome_text = f"مرحباً {self.current_user['first_name']} {self.current_user['last_name']}"
        main_layout.add_widget(Label(text=welcome_text, font_size=16))
        
        # الأزرار الرئيسية
        add_patient_btn = Button(
            text='إضافة مريض جديد',
            size_hint_y=None,
            height=60
        )
        add_patient_btn.bind(on_press=self.show_add_patient_screen)
        main_layout.add_widget(add_patient_btn)
        
        patients_list_btn = Button(
            text='قائمة المرضى',
            size_hint_y=None,
            height=60
        )
        patients_list_btn.bind(on_press=self.show_patients_list)
        main_layout.add_widget(patients_list_btn)
        
        refresh_btn = Button(
            text='تحديث البيانات',
            size_hint_y=None,
            height=60
        )
        refresh_btn.bind(on_press=self.refresh_data)
        main_layout.add_widget(refresh_btn)
        
        logout_btn = Button(
            text='تسجيل خروج',
            size_hint_y=None,
            height=60
        )
        logout_btn.bind(on_press=self.logout)
        main_layout.add_widget(logout_btn)
        
        # إضافة إلى التمرير
        scroll = ScrollView()
        scroll.add_widget(main_layout)
        self.content_layout.add_widget(scroll)
    
    def show_add_patient_screen(self, instance):
        """عرض شاشة إضافة مريض"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        # حقول المريض
        fields_layout = GridLayout(cols=1, spacing=5, size_hint_y=None)
        fields_layout.bind(minimum_height=fields_layout.setter('height'))
        
        fields_layout.add_widget(Label(text='إضافة مريض جديد', font_size=18, size_hint_y=None, height=40))
        
        self.first_name_input = TextInput(hint_text='الاسم الأول', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.first_name_input)
        
        self.last_name_input = TextInput(hint_text='الاسم الأخير', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.last_name_input)
        
        self.birth_date_input = TextInput(hint_text='تاريخ الميلاد (YYYY-MM-DD)', text='1990-01-01', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.birth_date_input)
        
        self.gender_spinner = Spinner(
            text='اختر الجنس',
            values=['male', 'female'],
            size_hint_y=None,
            height=40
        )
        fields_layout.add_widget(self.gender_spinner)
        
        self.phone_input = TextInput(hint_text='رقم الهاتف', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.phone_input)
        
        self.email_patient_input = TextInput(hint_text='البريد الإلكتروني (اختياري)', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.email_patient_input)
        
        # أزرار
        buttons_layout = BoxLayout(spacing=10, size_hint_y=None, height=60)
        
        save_btn = Button(text='حفظ')
        save_btn.bind(on_press=lambda x: self.save_patient(popup))
        buttons_layout.add_widget(save_btn)
        
        cancel_btn = Button(text='إلغاء')
        cancel_btn.bind(on_press=lambda x: popup.dismiss())
        buttons_layout.add_widget(cancel_btn)
        
        fields_layout.add_widget(buttons_layout)
        
        # إضافة إلى التمرير
        scroll = ScrollView()
        scroll.add_widget(fields_layout)
        content.add_widget(scroll)
        
        popup = Popup(
            title='إضافة مريض جديد',
            content=content,
            size_hint=(0.9, 0.8)
        )
        popup.open()
    
    def save_patient(self, popup):
        """حفظ بيانات المريض"""
        # التحقق من البيانات المطلوبة
        if not all([
            self.first_name_input.text.strip(),
            self.last_name_input.text.strip(),
            self.birth_date_input.text.strip(),
            self.gender_spinner.text != 'اختر الجنس',
            self.phone_input.text.strip()
        ]):
            self.show_popup('يرجى ملء جميع الحقول المطلوبة')
            return
        
        patient_data = {
            "first_name": self.first_name_input.text.strip(),
            "last_name": self.last_name_input.text.strip(),
            "date_of_birth": self.birth_date_input.text.strip(),
            "gender": self.gender_spinner.text,
            "phone": self.phone_input.text.strip(),
            "email": self.email_patient_input.text.strip(),
            "created_by": self.current_user['id']
        }
        
        try:
            response = requests.post(
                f"{self.server_url}/api/patients",
                json=patient_data,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    popup.dismiss()
                    self.show_popup("تم إضافة المريض بنجاح\nسيتم إرسال تنبيه للأطباء")
                else:
                    self.show_popup(data.get("message", "فشل في إضافة المريض"))
            else:
                self.show_popup("خطأ في الاتصال بالخادم")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def show_patients_list(self, instance):
        """عرض قائمة المرضى"""
        try:
            response = requests.get(f"{self.server_url}/api/patients", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    patients = data.get("data", [])
                    self.display_patients_popup(patients)
                else:
                    self.show_popup(data.get("message", "فشل في تحميل المرضى"))
            else:
                self.show_popup("خطأ في الاتصال بالخادم")
                
        except Exception as e:
            self.show_popup(f"خطأ في الاتصال: {str(e)}")
    
    def display_patients_popup(self, patients):
        """عرض قائمة المرضى في نافذة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        patients_layout = GridLayout(cols=1, spacing=5, size_hint_y=None)
        patients_layout.bind(minimum_height=patients_layout.setter('height'))
        
        patients_layout.add_widget(Label(text=f'قائمة المرضى ({len(patients)})', font_size=16, size_hint_y=None, height=40))
        
        for patient in patients[:20]:  # عرض أول 20 مريض
            patient_text = f"{patient.get('first_name', '')} {patient.get('last_name', '')} - {patient.get('phone', '')}"
            patients_layout.add_widget(Label(text=patient_text, size_hint_y=None, height=30))
        
        scroll = ScrollView()
        scroll.add_widget(patients_layout)
        content.add_widget(scroll)
        
        close_btn = Button(text='إغلاق', size_hint_y=None, height=50)
        close_btn.bind(on_press=lambda x: popup.dismiss())
        content.add_widget(close_btn)
        
        popup = Popup(
            title='قائمة المرضى',
            content=content,
            size_hint=(0.9, 0.8)
        )
        popup.open()
    
    def refresh_data(self, instance):
        """تحديث البيانات"""
        self.check_server_connection()
        self.show_popup('تم تحديث البيانات')
    
    def logout(self, instance):
        """تسجيل الخروج"""
        self.current_user = None
        self.access_token = None
        self.show_login_screen()
    
    def show_popup(self, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        content.add_widget(Label(text=message))
        
        close_btn = Button(text='موافق', size_hint_y=None, height=50)
        content.add_widget(close_btn)
        
        popup = Popup(
            title='رسالة',
            content=content,
            size_hint=(0.8, 0.4)
        )
        
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

if __name__ == '__main__':
    RegistrarApp().run()
