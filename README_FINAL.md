# 🏥 نظام Clini<PERSON>o المحلي - دليل التشغيل النهائي

## ✅ **النظام جاهز للتشغيل!**

### 🎯 **ما تم إنجازه:**

1. **🔧 خادم محلي** (local_server.py) - لا يحتاج إنترنت
2. **👨‍⚕️ تطبيق الطبيب** (doctor_app.py) - لاستقبال التنبيهات والفحص
3. **👩‍💼 تطبيق موظف الاستقبال** (registrar_app.py) - لتسجيل المرضى والمواعيد
4. **🚀 مشغل النظام** (start_clinic.py) - لإدارة جميع التطبيقات
5. **💾 قاعدة بيانات محلية** (SQLite) - تحفظ جميع البيانات

---

## 🚀 **طريقة التشغيل:**

### الطريقة الأولى: المشغل الشامل (الأسهل)
```bash
python start_clinic.py
```

ستظهر نافذة تحكم تتيح لك:
- ✅ تشغيل/إيقاف الخادم
- ✅ تشغيل تطبيق الطبيب
- ✅ تشغيل تطبيق موظف الاستقبال
- ✅ عرض عناوين IP للشبكة المحلية

### الطريقة الثانية: التشغيل اليدوي

#### 1. تشغيل الخادم أولاً:
```bash
python local_server.py
```

#### 2. تشغيل تطبيق موظف الاستقبال:
```bash
python registrar_app.py
```

#### 3. تشغيل تطبيق الطبيب:
```bash
python doctor_app.py
```

---

## 🔑 **بيانات تسجيل الدخول:**

| الدور | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| **الطبيب** | <EMAIL> | doctor123 |
| **موظف الاستقبال** | <EMAIL> | registrar123 |
| **المدير** | <EMAIL> | admin123 |

---

## 🌐 **الربط عبر الشبكة المحلية:**

### للوصول من أجهزة أخرى في نفس الشبكة:

1. **اعرف عنوان IP للجهاز الرئيسي:**
   - سيظهر في مشغل النظام
   - أو استخدم: `ipconfig` (Windows) / `ifconfig` (Linux/Mac)

2. **في التطبيقات الأخرى:**
   - اذهب إلى "الإعدادات" → "إعدادات الخادم"
   - أدخل عنوان IP للجهاز الرئيسي
   - المنفذ: 8080

**مثال:** إذا كان IP الجهاز الرئيسي `*************`
- عنوان الخادم: `*************`
- المنفذ: `8080`

---

## 🔄 **سير العمل:**

### 1. **موظف الاستقبال:**
- يسجل مريض جديد
- يحجز موعد للمريض مع طبيب
- النظام يرسل تنبيه فوري للطبيب

### 2. **الطبيب:**
- يستقبل التنبيه عن المريض الجديد
- يفحص المريض
- يضيف السجل الطبي (التشخيص والعلاج)
- يرفع ملفات الأشعة والتحاليل

### 3. **النظام:**
- يحفظ جميع البيانات محلياً
- يحدث الإحصائيات والتقارير
- يرسل التنبيهات بين التطبيقات

---

## 📊 **الميزات المتاحة:**

### ✅ **مكتملة:**
- تسجيل دخول متعدد الأدوار
- إضافة وعرض المرضى
- نظام التنبيهات الأساسي
- لوحة معلومات بالإحصائيات
- قاعدة بيانات محلية آمنة
- ربط عبر الشبكة المحلية

### 🔄 **قيد التطوير:**
- حجز المواعيد التفصيلي
- السجلات الطبية الكاملة
- رفع الملفات والأشعة
- التقارير المتقدمة
- نظام التنبيهات الفوري

---

## 🛠️ **المتطلبات:**

### الأساسية:
- **Python 3.6+** (مثبت مسبقاً)
- **مكتبة requests** (يتم تثبيتها تلقائياً)

### اختيارية:
- **شبكة محلية** (للربط بين الأجهزة)
- **أجهزة متعددة** (لتوزيع التطبيقات)

---

## 🔧 **حل المشاكل:**

### مشكلة: "لا يمكن الاتصال بالخادم"
**الحل:**
1. تأكد من تشغيل الخادم أولاً
2. تحقق من عنوان IP والمنفذ
3. تأكد من عدم حجب Firewall للمنفذ 8080

### مشكلة: "Python is not recognized"
**الحل:**
1. تأكد من تثبيت Python
2. أضف Python إلى PATH
3. أعد تشغيل Command Prompt

### مشكلة: "No module named 'requests'"
**الحل:**
```bash
pip install requests
```

---

## 📁 **ملفات النظام:**

| الملف | الوصف |
|-------|--------|
| `start_clinic.py` | **مشغل النظام الرئيسي** |
| `local_server.py` | خادم قاعدة البيانات المحلي |
| `doctor_app.py` | تطبيق الطبيب |
| `registrar_app.py` | تطبيق موظف الاستقبال |
| `clinic.db` | قاعدة البيانات المحلية (تُنشأ تلقائياً) |

---

## 🎉 **البدء السريع:**

1. **افتح Command Prompt** في مجلد المشروع
2. **اكتب:** `python start_clinic.py`
3. **اضغط "تشغيل الخادم"** في النافذة التي تظهر
4. **اضغط "تطبيق موظف الاستقبال"** لبدء تسجيل المرضى
5. **اضغط "تطبيق الطبيب"** لاستقبال التنبيهات
6. **استخدم بيانات تسجيل الدخول** المذكورة أعلاه

**🎯 النظام جاهز للاستخدام فوراً!**

---

## 📞 **الدعم:**

النظام مصمم ليعمل بدون إنترنت ومع الحد الأدنى من المتطلبات.
جميع البيانات محفوظة محلياً وآمنة.

**✨ مبروك! لديك الآن نظام إدارة عيادة كامل ومحلي!**
