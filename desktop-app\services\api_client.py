"""
API Client for communicating with backend
عميل API للتواصل مع الخادم الخلفي
"""

import requests
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

class APIResponse:
    """API Response wrapper"""
    
    def __init__(self, success: bool, data: Any = None, message: str = "", status_code: int = 200):
        self.success = success
        self.data = data
        self.message = message
        self.status_code = status_code
    
    def __bool__(self):
        return self.success

class APIClient:
    """API Client for backend communication"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.token = None
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def set_token(self, token: str):
        """Set authentication token"""
        self.token = token
        self.session.headers['Authorization'] = f'Bearer {token}'
    
    def clear_token(self):
        """Clear authentication token"""
        self.token = None
        if 'Authorization' in self.session.headers:
            del self.session.headers['Authorization']
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """Make HTTP request to API"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # Try to parse JSON response
            try:
                data = response.json()
            except json.JSONDecodeError:
                data = {"message": response.text}
            
            # Check if request was successful
            if response.status_code < 400:
                return APIResponse(
                    success=True,
                    data=data.get('data') if isinstance(data, dict) else data,
                    message=data.get('message', '') if isinstance(data, dict) else '',
                    status_code=response.status_code
                )
            else:
                error_message = data.get('detail', data.get('message', 'Unknown error'))
                return APIResponse(
                    success=False,
                    message=error_message,
                    status_code=response.status_code
                )
                
        except requests.exceptions.ConnectionError:
            return APIResponse(
                success=False,
                message="لا يمكن الاتصال بالخادم. تأكد من تشغيل الخادم.",
                status_code=0
            )
        except requests.exceptions.Timeout:
            return APIResponse(
                success=False,
                message="انتهت مهلة الاتصال. حاول مرة أخرى.",
                status_code=0
            )
        except Exception as e:
            return APIResponse(
                success=False,
                message=f"خطأ غير متوقع: {str(e)}",
                status_code=0
            )
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> APIResponse:
        """Make GET request"""
        return self._make_request('GET', endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None) -> APIResponse:
        """Make POST request"""
        kwargs = {}
        if data:
            kwargs['data'] = data
        if json_data:
            kwargs['json'] = json_data
        return self._make_request('POST', endpoint, **kwargs)
    
    def put(self, endpoint: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None) -> APIResponse:
        """Make PUT request"""
        kwargs = {}
        if data:
            kwargs['data'] = data
        if json_data:
            kwargs['json'] = json_data
        return self._make_request('PUT', endpoint, **kwargs)
    
    def delete(self, endpoint: str) -> APIResponse:
        """Make DELETE request"""
        return self._make_request('DELETE', endpoint)
    
    def upload_file(self, endpoint: str, file_path: str, additional_data: Optional[Dict] = None) -> APIResponse:
        """Upload file to API"""
        try:
            with open(file_path, 'rb') as file:
                files = {'file': file}
                data = additional_data or {}
                
                # Remove Content-Type header for file upload
                headers = self.session.headers.copy()
                if 'Content-Type' in headers:
                    del headers['Content-Type']
                
                response = requests.post(
                    f"{self.base_url}{endpoint}",
                    files=files,
                    data=data,
                    headers=headers,
                    timeout=self.timeout
                )
                
                if response.status_code < 400:
                    return APIResponse(
                        success=True,
                        data=response.json(),
                        message="تم رفع الملف بنجاح",
                        status_code=response.status_code
                    )
                else:
                    error_data = response.json() if response.content else {}
                    return APIResponse(
                        success=False,
                        message=error_data.get('detail', 'فشل في رفع الملف'),
                        status_code=response.status_code
                    )
                    
        except FileNotFoundError:
            return APIResponse(
                success=False,
                message="الملف غير موجود",
                status_code=0
            )
        except Exception as e:
            return APIResponse(
                success=False,
                message=f"خطأ في رفع الملف: {str(e)}",
                status_code=0
            )
    
    def health_check(self) -> APIResponse:
        """Check API health"""
        return self.get('/health')
    
    def is_connected(self) -> bool:
        """Check if API is reachable"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
