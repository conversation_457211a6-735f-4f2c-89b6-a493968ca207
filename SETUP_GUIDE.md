# دليل تشغيل نظام Clinineo

## المتطلبات الأساسية

### 1. تثبيت Python
- قم بتحميل Python 3.8+ من: https://python.org/downloads/
- تأكد من إضافة Python إلى PATH أثناء التثبيت
- تحقق من التثبيت: `python --version`

### 2. تثبيت Git (اختياري)
- قم بتحميل Git من: https://git-scm.com/
- للتحكم في الإصدارات والتطوير

## خطوات التشغيل

### الخطوة 1: إعداد Backend (Python FastAPI)

```bash
# الانتقال إلى مجلد Backend
cd backend

# تثبيت المكتبات المطلوبة
pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart python-dotenv pydantic-settings

# أو تثبيت من ملف requirements.txt
pip install -r requirements.txt

# تشغيل الخادم
python run.py

# أو تشغيل مباشر
python main.py
```

### الخطوة 2: اختبار الخادم

بعد تشغيل الخادم، ستكون الخدمات متاحة على:

- **الصفحة الرئيسية:** http://127.0.0.1:8000
- **توثيق API:** http://127.0.0.1:8000/docs
- **فحص الصحة:** http://127.0.0.1:8000/health

### الخطوة 3: تشغيل تطبيق سطح المكتب

```bash
# الانتقال إلى مجلد التطبيق
cd desktop-app

# تثبيت المكتبات المطلوبة
pip install requests pillow

# تشغيل التطبيق
python run.py

# أو تشغيل مباشر
python main.py
```

## اختبار النظام

### 1. تشغيل الخادم أولاً
```bash
cd backend
python run.py
```

### 2. تشغيل تطبيق سطح المكتب
```bash
cd desktop-app
python run.py
```

### 3. تسجيل الدخول
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** admin123

## الميزات المتاحة حالياً

### Backend API:
- ✅ نظام مصادقة كامل
- ✅ إدارة المستخدمين
- ✅ إدارة المرضى
- ✅ إدارة المواعيد
- ✅ السجلات الطبية
- ✅ رفع الملفات
- ✅ التقارير والإحصائيات
- ✅ التنبيهات الفورية (WebSocket)

### تطبيق سطح المكتب:
- ✅ واجهة تسجيل دخول
- ✅ النافذة الرئيسية
- ✅ نظام الأدوار والصلاحيات
- ✅ الاتصال بـ API
- ✅ عرض الإحصائيات السريعة
- 🔄 النوافذ الفرعية (قيد التطوير)

## عناوين الخدمات

- **Backend API:** http://127.0.0.1:8000
- **API Documentation:** http://127.0.0.1:8000/docs
- **Health Check:** http://127.0.0.1:8000/health

## اختبار النظام

### 1. تسجيل الدخول الافتراضي
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** admin123

### 2. إنشاء المستخدمين
- قم بتسجيل الدخول كأدمن
- أنشئ مستخدمين جدد (مسجل، طبيب)

### 3. اختبار سير العمل
1. سجل دخول كمسجل
2. أضف مريض جديد
3. احجز موعد للمريض
4. سجل دخول كطبيب
5. تحقق من التنبيهات
6. أضف سجل طبي للمريض

## عناوين الخدمات

- **Backend API:** http://localhost:3000
- **Health Check:** http://localhost:3000/health
- **API Documentation:** http://localhost:3000/api

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات:**
   - تأكد من صحة بيانات Supabase في ملف .env
   - تأكد من تشغيل schema.sql في Supabase

2. **خطأ في تثبيت المكتبات:**
   - تأكد من وجود اتصال إنترنت
   - قم بحذف node_modules وإعادة تثبيت npm install

3. **خطأ في تشغيل Flutter:**
   - قم بتشغيل flutter doctor
   - تأكد من تثبيت Android Studio أو VS Code

4. **خطأ في التنبيهات:**
   - تأكد من تشغيل Socket.io
   - تحقق من إعدادات CORS

## الميزات المتاحة

### للأدمن:
- إدارة المستخدمين
- عرض جميع التقارير
- إعدادات النظام

### للمسجل:
- تسجيل المرضى
- حجز المواعيد
- إدارة الاستقبال

### للطبيب:
- استقبال التنبيهات
- عرض المرضى والمواعيد
- إضافة السجلات الطبية
- رفع الملفات الطبية

## الدعم

في حالة وجود مشاكل:
1. تحقق من ملفات الـ logs
2. تأكد من تشغيل جميع الخدمات
3. راجع إعدادات قاعدة البيانات
4. تحقق من إعدادات الشبكة والـ CORS
