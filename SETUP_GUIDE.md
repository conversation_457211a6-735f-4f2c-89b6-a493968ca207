# دليل تشغيل نظام Clinineo

## المتطلبات الأساسية

### 1. تثبيت Node.js
- قم بتحميل Node.js من: https://nodejs.org/
- اختر النسخة LTS (الموصى بها)
- تأكد من تثبيت npm معه

### 2. تثبيت Flutter
- قم بتحميل Flutter من: https://flutter.dev/docs/get-started/install
- أضف Flutter إلى PATH
- قم بتشغيل `flutter doctor` للتأكد من التثبيت

### 3. إعداد قاعدة البيانات (Supabase)
- انشئ حساب في: https://supabase.com/
- أنشئ مشروع جديد
- انسخ URL و API Keys

## خطوات التشغيل

### الخطوة 1: إعداد Backend

```bash
# الانتقال إلى مجلد Backend
cd backend

# تثبيت المكتبات
npm install

# إنشاء ملف البيئة
copy .env.example .env

# تحرير ملف .env وإضافة بيانات Supabase
# SUPABASE_URL=your-supabase-url
# SUPABASE_ANON_KEY=your-supabase-anon-key
# SUPABASE_SERVICE_KEY=your-supabase-service-key
# JWT_SECRET=your-secret-key

# تشغيل الخادم
npm run dev
```

### الخطوة 2: إعداد قاعدة البيانات

```sql
-- في Supabase SQL Editor، قم بتشغيل:
-- محتويات ملف backend/database/schema.sql
```

### الخطوة 3: تشغيل تطبيق الموبايل

```bash
# الانتقال إلى مجلد التطبيق
cd mobile-app

# تثبيت المكتبات
flutter pub get

# تشغيل التطبيق
flutter run
```

### الخطوة 4: تشغيل تطبيق سطح المكتب

```bash
# الانتقال إلى مجلد سطح المكتب
cd desktop-app

# تثبيت المكتبات
flutter pub get

# تشغيل التطبيق
flutter run -d windows
```

## اختبار النظام

### 1. تسجيل الدخول الافتراضي
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** admin123

### 2. إنشاء المستخدمين
- قم بتسجيل الدخول كأدمن
- أنشئ مستخدمين جدد (مسجل، طبيب)

### 3. اختبار سير العمل
1. سجل دخول كمسجل
2. أضف مريض جديد
3. احجز موعد للمريض
4. سجل دخول كطبيب
5. تحقق من التنبيهات
6. أضف سجل طبي للمريض

## عناوين الخدمات

- **Backend API:** http://localhost:3000
- **Health Check:** http://localhost:3000/health
- **API Documentation:** http://localhost:3000/api

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات:**
   - تأكد من صحة بيانات Supabase في ملف .env
   - تأكد من تشغيل schema.sql في Supabase

2. **خطأ في تثبيت المكتبات:**
   - تأكد من وجود اتصال إنترنت
   - قم بحذف node_modules وإعادة تثبيت npm install

3. **خطأ في تشغيل Flutter:**
   - قم بتشغيل flutter doctor
   - تأكد من تثبيت Android Studio أو VS Code

4. **خطأ في التنبيهات:**
   - تأكد من تشغيل Socket.io
   - تحقق من إعدادات CORS

## الميزات المتاحة

### للأدمن:
- إدارة المستخدمين
- عرض جميع التقارير
- إعدادات النظام

### للمسجل:
- تسجيل المرضى
- حجز المواعيد
- إدارة الاستقبال

### للطبيب:
- استقبال التنبيهات
- عرض المرضى والمواعيد
- إضافة السجلات الطبية
- رفع الملفات الطبية

## الدعم

في حالة وجود مشاكل:
1. تحقق من ملفات الـ logs
2. تأكد من تشغيل جميع الخدمات
3. راجع إعدادات قاعدة البيانات
4. تحقق من إعدادات الشبكة والـ CORS
