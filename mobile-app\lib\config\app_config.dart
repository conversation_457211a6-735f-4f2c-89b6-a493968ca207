class AppConfig {
  // API Configuration
  static const String baseUrl = 'http://localhost:3000/api';
  static const String socketUrl = 'http://localhost:3000';
  
  // App Information
  static const String appName = 'Clinineo';
  static const String appVersion = '1.0.0';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // API Endpoints
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String meEndpoint = '/auth/me';
  static const String logoutEndpoint = '/auth/logout';
  
  static const String patientsEndpoint = '/patients';
  static const String appointmentsEndpoint = '/appointments';
  static const String medicalRecordsEndpoint = '/medical-records';
  static const String usersEndpoint = '/users';
  static const String notificationsEndpoint = '/notifications';
  static const String reportsEndpoint = '/reports';
  static const String filesEndpoint = '/files';
  
  // File Upload Configuration
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedFileTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf'
  ];
  
  // Pagination
  static const int defaultPageSize = 10;
  static const int maxPageSize = 50;
  
  // Socket Events
  static const String patientRegisteredEvent = 'patient-registered';
  static const String appointmentBookedEvent = 'appointment-booked';
  static const String newPatientNotificationEvent = 'new-patient-notification';
  static const String newAppointmentNotificationEvent = 'new-appointment-notification';
  
  // User Roles
  static const String adminRole = 'admin';
  static const String doctorRole = 'doctor';
  static const String registrarRole = 'registrar';
  
  // Appointment Types
  static const List<String> appointmentTypes = [
    'consultation',
    'follow-up',
    'emergency',
    'checkup'
  ];
  
  // Appointment Status
  static const List<String> appointmentStatuses = [
    'scheduled',
    'confirmed',
    'in-progress',
    'completed',
    'cancelled'
  ];
  
  // Blood Types
  static const List<String> bloodTypes = [
    'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
  ];
  
  // Gender Options
  static const List<String> genders = ['male', 'female'];
  
  // File Types
  static const List<String> medicalFileTypes = [
    'xray',
    'lab_result',
    'prescription',
    'report',
    'other'
  ];
  
  // Notification Types
  static const List<String> notificationTypes = [
    'info',
    'success',
    'warning',
    'error'
  ];
  
  // Colors
  static const int primaryColorValue = 0xFF2196F3;
  static const int accentColorValue = 0xFF4CAF50;
  static const int errorColorValue = 0xFFF44336;
  static const int warningColorValue = 0xFFFF9800;
  
  // Timeouts
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Cache Duration
  static const Duration cacheExpiration = Duration(hours: 1);
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayTimeFormat = 'hh:mm a';
  static const String displayDateTimeFormat = 'dd/MM/yyyy hh:mm a';
  
  // Environment
  static const bool isDebug = true;
  static const bool enableLogging = true;
  
  // Helper Methods
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
  
  static bool isValidRole(String role) {
    return [adminRole, doctorRole, registrarRole].contains(role);
  }
  
  static bool isValidAppointmentType(String type) {
    return appointmentTypes.contains(type);
  }
  
  static bool isValidAppointmentStatus(String status) {
    return appointmentStatuses.contains(status);
  }
  
  static bool isValidBloodType(String bloodType) {
    return bloodTypes.contains(bloodType);
  }
  
  static bool isValidGender(String gender) {
    return genders.contains(gender);
  }
  
  static bool isValidFileType(String fileType) {
    return allowedFileTypes.contains(fileType);
  }
  
  static bool isValidMedicalFileType(String type) {
    return medicalFileTypes.contains(type);
  }
  
  static bool isValidNotificationType(String type) {
    return notificationTypes.contains(type);
  }
}
