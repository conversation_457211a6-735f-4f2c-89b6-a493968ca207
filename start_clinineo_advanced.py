#!/usr/bin/env python3
"""
Clinineo Advanced Launcher - مشغل نظام Clinineo المتطور
مشغل شامل لنظام إدارة العيادة مع تثبيت تلقائي للمكتبات
"""

import subprocess
import sys
import os
import time
import socket
import webbrowser

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    print("📦 تثبيت المكتبات المطلوبة...")
    
    try:
        # قراءة المكتبات من requirements.txt
        if os.path.exists('requirements.txt'):
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        else:
            # تثبيت المكتبات الأساسية
            packages = [
                "Flask==2.3.3",
                "Flask-SocketIO==5.3.6", 
                "qrcode==7.4.2",
                "Pillow==10.0.1",
                "python-socketio==5.9.0",
                "eventlet==0.33.3"
            ]
            
            for package in packages:
                print(f"تثبيت {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        
        print("✅ تم تثبيت جميع المكتبات بنجاح!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_packages = ['flask', 'flask_socketio', 'qrcode', 'PIL', 'socketio']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                __import__('PIL')
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def create_desktop_shortcuts():
    """إنشاء اختصارات سطح المكتب"""
    try:
        local_ip = get_local_ip()
        
        # إنشاء ملف HTML للاختصارات
        shortcuts_html = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinineo Advanced - اختصارات سريعة</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
        }}
        h1 {{ color: #333; margin-bottom: 30px; }}
        .btn {{
            display: block;
            width: 100%;
            padding: 15px;
            margin: 15px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
        }}
        .btn:hover {{ transform: translateY(-2px); }}
        .btn-registrar {{ background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%); }}
        .btn-doctor {{ background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%); }}
        .info {{ background: #f8f9fa; padding: 15px; border-radius: 10px; margin-top: 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Clinineo Advanced</h1>
        <p>نظام إدارة العيادات المتطور</p>
        
        <a href="http://{local_ip}:5000/" class="btn">
            🏠 الصفحة الرئيسية
        </a>
        
        <a href="http://{local_ip}:5000/registrar" class="btn btn-registrar">
            🏥 تطبيق الاستقبال
        </a>
        
        <a href="http://{local_ip}:5000/doctor" class="btn btn-doctor">
            🩺 تطبيق الطبيب
        </a>
        
        <div class="info">
            <h4>📱 للاستخدام على الموبايل:</h4>
            <small style="color: #666;">
                🌐 IP: {local_ip}:5000<br>
                🔧 تأكد من تشغيل الخادم أولاً<br>
                📱 يعمل على جميع الأجهزة بدون إنترنت<br>
                🔗 ربط محلي بين الأجهزة
            </small>
        </div>
    </div>
</body>
</html>
        """
        
        with open('clinineo_shortcuts.html', 'w', encoding='utf-8') as f:
            f.write(shortcuts_html)
        
        print(f"✅ تم إنشاء ملف الاختصارات: clinineo_shortcuts.html")
        
    except Exception as e:
        print(f"⚠️ فشل في إنشاء الاختصارات: {e}")

def open_browser_delayed(url, delay=3):
    """فتح المتصفح بعد تأخير"""
    def open_browser():
        try:
            webbrowser.open(url)
        except:
            pass
    
    import threading
    timer = threading.Timer(delay, open_browser)
    timer.start()

def main():
    """الدالة الرئيسية"""
    print("🌐" + "="*60)
    print("🏥 Clinineo Advanced System - نظام إدارة العيادة المتطور")
    print("="*62)
    print("🎯 نظام شامل يعمل بدون إنترنت مع ربط محلي بين الأجهزة")
    print("📱 واجهات ويب متطورة تعمل على جميع الأجهزة")
    print("🔗 QR Codes للوصول السريع من الموبايل")
    print("="*62)
    
    # التحقق من Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    print("✅ إصدار Python مناسب")
    
    # التحقق من المكتبات
    missing = check_dependencies()
    
    if missing:
        print(f"📦 مكتبات مفقودة: {', '.join(missing)}")
        
        install_choice = input("هل تريد تثبيت المكتبات المطلوبة؟ (y/n): ").lower()
        
        if install_choice in ['y', 'yes', 'نعم', '']:
            if not install_requirements():
                print("❌ فشل في تثبيت المكتبات")
                input("اضغط Enter للخروج...")
                return
        else:
            print("❌ لا يمكن تشغيل النظام بدون المكتبات المطلوبة")
            input("اضغط Enter للخروج...")
            return
    else:
        print("✅ جميع المكتبات متوفرة")
    
    # إنشاء الاختصارات
    create_desktop_shortcuts()
    
    # التحقق من وجود ملف الخادم
    if not os.path.exists('clinineo_server.py'):
        print("❌ ملف الخادم غير موجود: clinineo_server.py")
        input("اضغط Enter للخروج...")
        return
    
    # الحصول على IP
    local_ip = get_local_ip()
    
    print("="*62)
    print("🚀 بدء تشغيل نظام Clinineo المتطور...")
    print("="*62)
    print(f"🔗 الروابط المتاحة:")
    print(f"   📱 الصفحة الرئيسية: http://{local_ip}:5000/")
    print(f"   🏥 تطبيق الاستقبال: http://{local_ip}:5000/registrar")
    print(f"   🩺 تطبيق الطبيب: http://{local_ip}:5000/doctor")
    print("="*62)
    print(f"📱 للاستخدام على الموبايل:")
    print(f"   1. تأكد من اتصال الهاتف بنفس الشبكة")
    print(f"   2. افتح المتصفح على الهاتف")
    print(f"   3. اذهب إلى: http://{local_ip}:5000/")
    print(f"   4. امسح QR Code أو اختر التطبيق")
    print(f"   5. أضف للشاشة الرئيسية")
    print("="*62)
    
    # فتح المتصفح تلقائياً
    print("🌐 سيتم فتح المتصفح تلقائياً خلال 3 ثوان...")
    open_browser_delayed(f"http://{local_ip}:5000/")
    
    try:
        # تشغيل الخادم
        subprocess.run([sys.executable, "clinineo_server.py"])
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except FileNotFoundError:
        print("❌ ملف الخادم غير موجود")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
    
    print("="*62)
    print("👋 شكراً لاستخدام Clinineo Advanced!")
    print("📁 تم حفظ البيانات في: clinineo.db")
    print("🔗 ملف الاختصارات: clinineo_shortcuts.html")
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
