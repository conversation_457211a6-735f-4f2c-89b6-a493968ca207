@echo off
echo ========================================
echo    Clinineo - موظف الاستقبال
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

REM التحقق من وجود الملفات المطلوبة
if not exist "network_shared_data.py" (
    echo ❌ ملف network_shared_data.py غير موجود
    echo يرجى وضع جميع الملفات في نفس المجلد
    pause
    exit /b 1
)

if not exist "registrar_network.py" (
    echo ❌ ملف registrar_network.py غير موجود
    echo يرجى وضع جميع الملفات في نفس المجلد
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة
echo 🚀 تشغيل تطبيق موظف الاستقبال...
echo.

REM تشغيل التطبيق
python registrar_network.py

echo.
echo 👋 تم إغلاق التطبيق
pause
