"""
Authentication service
خدمة المصادقة
"""

from typing import Optional, Dict, Any
from .api_client import APIClient, APIResponse

class AuthService:
    """Authentication service for user login/logout"""
    
    def __init__(self, api_client: APIClient):
        self.api_client = api_client
        self.current_user = None
        self.current_token = None
    
    def login(self, email: str, password: str) -> APIResponse:
        """Login user with email and password"""
        login_data = {
            "email": email,
            "password": password
        }
        
        response = self.api_client.post('/auth/login', json_data=login_data)
        
        if response.success and response.data:
            # Extract user and token from response
            self.current_token = response.data.get('access_token')
            self.current_user = response.data.get('user')
            
            # Set token in API client
            if self.current_token:
                self.api_client.set_token(self.current_token)
        
        return response
    
    def logout(self) -> APIResponse:
        """Logout current user"""
        response = self.api_client.post('/auth/logout')
        
        # Clear local data regardless of API response
        self.current_user = None
        self.current_token = None
        self.api_client.clear_token()
        
        return response
    
    def get_current_user(self) -> APIResponse:
        """Get current user information"""
        if not self.current_token:
            return APIResponse(
                success=False,
                message="لم يتم تسجيل الدخول"
            )
        
        return self.api_client.get('/auth/me')
    
    def change_password(self, current_password: str, new_password: str) -> APIResponse:
        """Change user password"""
        if not self.current_token:
            return APIResponse(
                success=False,
                message="لم يتم تسجيل الدخول"
            )
        
        password_data = {
            "current_password": current_password,
            "new_password": new_password
        }
        
        return self.api_client.post('/auth/change-password', json_data=password_data)
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return self.current_token is not None and self.current_user is not None
    
    def get_user_role(self) -> Optional[str]:
        """Get current user role"""
        if self.current_user:
            return self.current_user.get('role')
        return None
    
    def get_user_name(self) -> str:
        """Get current user full name"""
        if self.current_user:
            first_name = self.current_user.get('first_name', '')
            last_name = self.current_user.get('last_name', '')
            return f"{first_name} {last_name}".strip()
        return "غير معروف"
    
    def has_permission(self, required_role: str) -> bool:
        """Check if user has required role"""
        if not self.is_authenticated():
            return False
        
        user_role = self.get_user_role()
        
        # Admin has all permissions
        if user_role == 'admin':
            return True
        
        # Check specific role
        return user_role == required_role
    
    def can_manage_users(self) -> bool:
        """Check if user can manage other users"""
        return self.has_permission('admin')
    
    def can_register_patients(self) -> bool:
        """Check if user can register patients"""
        return self.has_permission('admin') or self.has_permission('registrar')
    
    def can_manage_appointments(self) -> bool:
        """Check if user can manage appointments"""
        return self.has_permission('admin') or self.has_permission('registrar')
    
    def can_access_medical_records(self) -> bool:
        """Check if user can access medical records"""
        return self.has_permission('admin') or self.has_permission('doctor')
    
    def can_view_reports(self) -> bool:
        """Check if user can view reports"""
        return self.is_authenticated()  # All authenticated users can view reports
