#!/usr/bin/env python3
"""
Radiology Screen for Clinineo Doctor App
شاشة الأشعة والتحاليل لتطبيق الطبيب
"""

from kivy.uix.screenmanager import Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.popup import Popup
from kivy.uix.spinner import Spinner
from kivy.uix.scrollview import ScrollView
from kivy.uix.filechooser import FileChooserListView
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.graphics import Color, RoundedRectangle, Line
from kivy.uix.widget import Widget
import json
import os
import uuid
import shutil
from datetime import datetime

class RadiologyScreen(Screen):
    """شاشة الأشعة والتحاليل"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'radiology'
        self.current_patient = None
        self.selected_files = []
        self.build_ui()
    
    def build_ui(self):
        """بناء واجهة الأشعة والتحاليل"""
        main_layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(15))
        
        # Header
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))
        
        back_btn = Button(text='← رجوع', font_size=dp(16), size_hint_x=None, width=dp(100))
        back_btn.bind(on_press=self.go_back)
        
        title = Label(text='🩻 الأشعة والتحاليل', font_size=dp(20), bold=True, color=(0.1, 0.8, 0.8, 1))
        
        save_btn = Button(text='💾 حفظ', font_size=dp(16), size_hint_x=None, width=dp(100))
        save_btn.bind(on_press=self.save_radiology_record)
        
        header.add_widget(back_btn)
        header.add_widget(title)
        header.add_widget(save_btn)
        
        main_layout.add_widget(header)
        
        # Content ScrollView
        content_scroll = ScrollView()
        content_layout = BoxLayout(orientation='vertical', spacing=dp(15), size_hint_y=None)
        content_layout.bind(minimum_height=content_layout.setter('height'))
        
        # Patient Selection
        patient_section = self.create_patient_selection()
        content_layout.add_widget(patient_section)
        
        # Record Type Selection
        type_section = self.create_type_selection()
        content_layout.add_widget(type_section)
        
        # Record Details
        details_section = self.create_details_section()
        content_layout.add_widget(details_section)
        
        # File Upload Section
        files_section = self.create_files_section()
        content_layout.add_widget(files_section)
        
        # Previous Records
        history_section = self.create_history_section()
        content_layout.add_widget(history_section)
        
        content_scroll.add_widget(content_layout)
        main_layout.add_widget(content_scroll)
        
        self.add_widget(main_layout)
        
        # Load data when screen is created
        Clock.schedule_once(self.load_patients, 0.1)
        Clock.schedule_once(self.load_radiology_history, 0.1)
    
    def create_patient_selection(self):
        """إنشاء قسم اختيار المريض"""
        section_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(100), spacing=dp(10))
        
        section_title = Label(
            text='👤 اختيار المريض',
            font_size=dp(16),
            bold=True,
            size_hint_y=None,
            height=dp(30),
            color=(0.2, 0.2, 0.2, 1)
        )
        section_layout.add_widget(section_title)
        
        patient_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))
        
        patient_label = Label(text='المريض:', font_size=dp(14), size_hint_x=None, width=dp(80))
        
        self.patient_spinner = Spinner(
            text='اختر مريض',
            values=[],
            size_hint_y=None,
            height=dp(40)
        )
        self.patient_spinner.bind(text=self.on_patient_selected)
        
        patient_layout.add_widget(patient_label)
        patient_layout.add_widget(self.patient_spinner)
        
        section_layout.add_widget(patient_layout)
        
        return section_layout
    
    def create_type_selection(self):
        """إنشاء قسم اختيار نوع الفحص"""
        section_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(100), spacing=dp(10))
        
        section_title = Label(
            text='🔬 نوع الفحص',
            font_size=dp(16),
            bold=True,
            size_hint_y=None,
            height=dp(30),
            color=(0.2, 0.2, 0.2, 1)
        )
        section_layout.add_widget(section_title)
        
        type_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))
        
        type_label = Label(text='النوع:', font_size=dp(14), size_hint_x=None, width=dp(80))
        
        self.type_spinner = Spinner(
            text='اختر نوع الفحص',
            values=[
                'أشعة سينية (X-Ray)',
                'أشعة مقطعية (CT)',
                'رنين مغناطيسي (MRI)',
                'موجات فوق صوتية (Ultrasound)',
                'تحليل دم شامل',
                'تحليل بول',
                'تحليل براز',
                'تحليل هرمونات',
                'تحليل فيتامينات',
                'تحليل وظائف كلى',
                'تحليل وظائف كبد',
                'تحليل سكر',
                'تحليل كوليسترول',
                'أخرى'
            ],
            size_hint_y=None,
            height=dp(40)
        )
        
        type_layout.add_widget(type_label)
        type_layout.add_widget(self.type_spinner)
        
        section_layout.add_widget(type_layout)
        
        return section_layout
    
    def create_details_section(self):
        """إنشاء قسم تفاصيل الفحص"""
        section_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(10))
        
        section_title = Label(
            text='📋 تفاصيل الفحص',
            font_size=dp(16),
            bold=True,
            size_hint_y=None,
            height=dp(30),
            color=(0.2, 0.2, 0.2, 1)
        )
        section_layout.add_widget(section_title)
        
        # Test Name
        name_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(80))
        name_label = Label(text='اسم الفحص:', font_size=dp(14), size_hint_y=None, height=dp(25))
        self.test_name_input = TextInput(
            hint_text='أدخل اسم الفحص المحدد...',
            multiline=False,
            size_hint_y=None,
            height=dp(40)
        )
        name_layout.add_widget(name_label)
        name_layout.add_widget(self.test_name_input)
        section_layout.add_widget(name_layout)
        
        # Description
        desc_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(120))
        desc_label = Label(text='وصف الفحص:', font_size=dp(14), size_hint_y=None, height=dp(25))
        self.description_input = TextInput(
            hint_text='وصف تفصيلي للفحص المطلوب...',
            multiline=True,
            size_hint_y=None,
            height=dp(80)
        )
        desc_layout.add_widget(desc_label)
        desc_layout.add_widget(self.description_input)
        section_layout.add_widget(desc_layout)
        
        # Results
        results_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(120))
        results_label = Label(text='النتائج:', font_size=dp(14), size_hint_y=None, height=dp(25))
        self.results_input = TextInput(
            hint_text='نتائج الفحص (إن وجدت)...',
            multiline=True,
            size_hint_y=None,
            height=dp(80)
        )
        results_layout.add_widget(results_label)
        results_layout.add_widget(self.results_input)
        section_layout.add_widget(results_layout)
        
        # Notes
        notes_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(120))
        notes_label = Label(text='ملاحظات:', font_size=dp(14), size_hint_y=None, height=dp(25))
        self.notes_input = TextInput(
            hint_text='ملاحظات إضافية...',
            multiline=True,
            size_hint_y=None,
            height=dp(80)
        )
        notes_layout.add_widget(notes_label)
        notes_layout.add_widget(self.notes_input)
        section_layout.add_widget(notes_layout)
        
        return section_layout
    
    def create_files_section(self):
        """إنشاء قسم الملفات"""
        section_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(10))
        
        section_title = Label(
            text='📎 الملفات المرفقة',
            font_size=dp(16),
            bold=True,
            size_hint_y=None,
            height=dp(30),
            color=(0.2, 0.2, 0.2, 1)
        )
        section_layout.add_widget(section_title)
        
        # File buttons
        buttons_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))
        
        upload_btn = Button(
            text='📁 اختيار ملفات',
            size_hint_y=None,
            height=dp(40),
            background_color=(0.2, 0.6, 1, 1)
        )
        upload_btn.bind(on_press=self.show_file_chooser)
        
        clear_btn = Button(
            text='🗑️ مسح الملفات',
            size_hint_y=None,
            height=dp(40),
            background_color=(1, 0.3, 0.3, 1)
        )
        clear_btn.bind(on_press=self.clear_files)
        
        buttons_layout.add_widget(upload_btn)
        buttons_layout.add_widget(clear_btn)
        
        section_layout.add_widget(buttons_layout)
        
        # Selected files list
        self.files_list_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(5))
        self.files_list_layout.bind(minimum_height=self.files_list_layout.setter('height'))
        
        section_layout.add_widget(self.files_list_layout)
        
        return section_layout
    
    def create_history_section(self):
        """إنشاء قسم السجلات السابقة"""
        section_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(10))
        
        section_title = Label(
            text='📚 السجلات السابقة',
            font_size=dp(16),
            bold=True,
            size_hint_y=None,
            height=dp(30),
            color=(0.2, 0.2, 0.2, 1)
        )
        section_layout.add_widget(section_title)
        
        # History list
        self.history_layout = BoxLayout(orientation='vertical', size_hint_y=None, spacing=dp(5))
        self.history_layout.bind(minimum_height=self.history_layout.setter('height'))
        
        section_layout.add_widget(self.history_layout)
        
        return section_layout

    def create_history_item(self, record):
        """إنشاء عنصر سجل تاريخي"""
        item_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(100), spacing=dp(5))

        # Header with type and date
        header_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(30))

        type_label = Label(
            text=record.get('type', ''),
            font_size=dp(14),
            bold=True,
            color=(0.1, 0.8, 0.8, 1),
            halign='right'
        )
        type_label.bind(size=type_label.setter('text_size'))

        date_label = Label(
            text=record.get('date', '')[:10],
            font_size=dp(12),
            color=(0.6, 0.6, 0.6, 1),
            size_hint_x=None,
            width=dp(100)
        )

        header_layout.add_widget(type_label)
        header_layout.add_widget(date_label)

        # Test name
        test_label = Label(
            text=record.get('test_name', ''),
            font_size=dp(14),
            color=(0.2, 0.2, 0.2, 1),
            size_hint_y=None,
            height=dp(25),
            halign='right'
        )
        test_label.bind(size=test_label.setter('text_size'))

        # Files count
        files_count = len(record.get('files', []))
        files_label = Label(
            text=f"📎 {files_count} ملف مرفق" if files_count > 0 else "لا توجد ملفات",
            font_size=dp(12),
            color=(0.4, 0.4, 0.4, 1),
            size_hint_y=None,
            height=dp(20),
            halign='right'
        )
        files_label.bind(size=files_label.setter('text_size'))

        # View button
        view_btn = Button(
            text='👁️ عرض',
            size_hint_y=None,
            height=dp(30),
            background_color=(0.2, 0.6, 1, 1)
        )
        view_btn.bind(on_press=lambda x: self.view_record_details(record))

        item_layout.add_widget(header_layout)
        item_layout.add_widget(test_label)
        item_layout.add_widget(files_label)
        item_layout.add_widget(view_btn)

        # Wrap in styled container
        container = Widget()
        container.size_hint_y = None
        container.height = dp(100)

        with container.canvas.before:
            Color(0.95, 1, 1, 1)
            container.rect = RoundedRectangle(pos=container.pos, size=container.size, radius=[8])
            Color(0.1, 0.8, 0.8, 1)
            container.border = Line(rounded_rectangle=(container.x, container.y, container.width, container.height, 8), width=1)

        container.bind(pos=lambda instance, value: setattr(container.rect, 'pos', value))
        container.bind(size=lambda instance, value: setattr(container.rect, 'size', value))
        container.bind(pos=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 8)))
        container.bind(size=lambda instance, value: setattr(container.border, 'rounded_rectangle', (container.x, container.y, container.width, container.height, 8)))

        container.add_widget(item_layout)
        return container

    def view_record_details(self, record):
        """عرض تفاصيل السجل"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))

        # Record details
        details_scroll = ScrollView(size_hint_y=0.8)
        details_layout = BoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None)
        details_layout.bind(minimum_height=details_layout.setter('height'))

        # Add record information
        info_items = [
            f"نوع الفحص: {record.get('type', '')}",
            f"اسم الفحص: {record.get('test_name', '')}",
            f"المريض: {record.get('patient_name', '')}",
            f"التاريخ: {record.get('date', '')[:10]}",
            f"الطبيب: {record.get('doctor_name', '')}",
        ]

        if record.get('description'):
            info_items.append(f"الوصف: {record.get('description')}")

        if record.get('results'):
            info_items.append(f"النتائج: {record.get('results')}")

        if record.get('notes'):
            info_items.append(f"ملاحظات: {record.get('notes')}")

        for item in info_items:
            item_label = Label(
                text=item,
                font_size=dp(14),
                color=(0.2, 0.2, 0.2, 1),
                size_hint_y=None,
                height=dp(30),
                halign='right',
                text_size=(dp(300), None)
            )
            details_layout.add_widget(item_label)

        # Files section
        files = record.get('files', [])
        if files:
            files_title = Label(
                text='الملفات المرفقة:',
                font_size=dp(16),
                bold=True,
                color=(0.1, 0.8, 0.8, 1),
                size_hint_y=None,
                height=dp(30)
            )
            details_layout.add_widget(files_title)

            for file_info in files:
                file_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(40), spacing=dp(10))

                file_name = file_info.get('original_name', '')
                file_size = file_info.get('size', 0)
                size_mb = file_size / (1024 * 1024) if file_size > 0 else 0

                file_label = Label(
                    text=f"📎 {file_name} ({size_mb:.1f} MB)",
                    font_size=dp(12),
                    color=(0.4, 0.4, 0.4, 1),
                    halign='right'
                )
                file_label.bind(size=file_label.setter('text_size'))

                open_btn = Button(
                    text='فتح',
                    size_hint_x=None,
                    width=dp(60),
                    size_hint_y=None,
                    height=dp(30),
                    background_color=(0.3, 0.7, 0.3, 1)
                )
                open_btn.bind(on_press=lambda x, path=file_info.get('path'): self.open_file(path))

                file_layout.add_widget(file_label)
                file_layout.add_widget(open_btn)
                details_layout.add_widget(file_layout)

        details_scroll.add_widget(details_layout)
        content.add_widget(details_scroll)

        # Close button
        close_btn = Button(text='إغلاق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(
            title='تفاصيل السجل',
            content=content,
            size_hint=(0.9, 0.8)
        )

        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def open_file(self, file_path):
        """فتح ملف"""
        try:
            if os.path.exists(file_path):
                import subprocess
                import platform

                system = platform.system()
                if system == 'Windows':
                    os.startfile(file_path)
                elif system == 'Darwin':  # macOS
                    subprocess.run(['open', file_path])
                else:  # Linux
                    subprocess.run(['xdg-open', file_path])
            else:
                self.show_popup('خطأ', 'الملف غير موجود')
        except Exception as e:
            self.show_popup('خطأ', f'فشل في فتح الملف: {str(e)}')

    def load_patients(self, dt=None):
        """تحميل قائمة المرضى"""
        try:
            if os.path.exists('clinineo_data.json'):
                with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    patients = data.get('patients', [])

                    patient_names = []
                    for patient in patients:
                        name = f"{patient.get('first_name', '')} {patient.get('last_name', '')}"
                        patient_names.append(name)

                    self.patient_spinner.values = patient_names
        except:
            pass

    def on_patient_selected(self, spinner, text):
        """عند اختيار مريض"""
        if text != 'اختر مريض':
            try:
                if os.path.exists('clinineo_data.json'):
                    with open('clinineo_data.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        patients = data.get('patients', [])

                        for patient in patients:
                            name = f"{patient.get('first_name', '')} {patient.get('last_name', '')}"
                            if name == text:
                                self.current_patient = patient
                                self.load_patient_radiology_history()
                                break
            except:
                pass

    def show_file_chooser(self, instance):
        """عرض منتقي الملفات"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))

        # File chooser
        file_chooser = FileChooserListView(
            filters=['*.jpg', '*.jpeg', '*.png', '*.pdf', '*.doc', '*.docx', '*.txt'],
            size_hint_y=0.8
        )
        content.add_widget(file_chooser)

        # Buttons
        buttons_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))

        select_btn = Button(text='اختيار')
        cancel_btn = Button(text='إلغاء')

        buttons_layout.add_widget(select_btn)
        buttons_layout.add_widget(cancel_btn)
        content.add_widget(buttons_layout)

        popup = Popup(
            title='اختيار ملفات',
            content=content,
            size_hint=(0.9, 0.8)
        )

        def select_files(instance):
            if file_chooser.selection:
                for file_path in file_chooser.selection:
                    if file_path not in self.selected_files:
                        self.selected_files.append(file_path)
                self.update_files_list()
            popup.dismiss()

        select_btn.bind(on_press=select_files)
        cancel_btn.bind(on_press=popup.dismiss)
        popup.open()

    def update_files_list(self):
        """تحديث قائمة الملفات المختارة"""
        self.files_list_layout.clear_widgets()

        for file_path in self.selected_files:
            file_item = self.create_file_item(file_path)
            self.files_list_layout.add_widget(file_item)

    def create_file_item(self, file_path):
        """إنشاء عنصر ملف"""
        item_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(40), spacing=dp(10))

        # File icon based on extension
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext in ['.jpg', '.jpeg', '.png']:
            icon = '🖼️'
        elif file_ext == '.pdf':
            icon = '📄'
        elif file_ext in ['.doc', '.docx']:
            icon = '📝'
        else:
            icon = '📎'

        icon_label = Label(
            text=icon,
            font_size=dp(20),
            size_hint_x=None,
            width=dp(40)
        )

        file_name = os.path.basename(file_path)
        name_label = Label(
            text=file_name,
            font_size=dp(14),
            color=(0.2, 0.2, 0.2, 1),
            halign='right'
        )
        name_label.bind(size=name_label.setter('text_size'))

        remove_btn = Button(
            text='✕',
            size_hint_x=None,
            width=dp(40),
            size_hint_y=None,
            height=dp(30),
            background_color=(1, 0.3, 0.3, 1)
        )
        remove_btn.bind(on_press=lambda x: self.remove_file(file_path))

        item_layout.add_widget(icon_label)
        item_layout.add_widget(name_label)
        item_layout.add_widget(remove_btn)

        return item_layout

    def remove_file(self, file_path):
        """حذف ملف من القائمة"""
        if file_path in self.selected_files:
            self.selected_files.remove(file_path)
            self.update_files_list()

    def clear_files(self, instance):
        """مسح جميع الملفات"""
        self.selected_files.clear()
        self.update_files_list()

    def save_radiology_record(self, instance):
        """حفظ سجل الأشعة/التحليل"""
        if not self.current_patient:
            self.show_popup('خطأ', 'يرجى اختيار مريض أولاً')
            return

        if not self.type_spinner.text or self.type_spinner.text == 'اختر نوع الفحص':
            self.show_popup('خطأ', 'يرجى اختيار نوع الفحص')
            return

        if not self.test_name_input.text.strip():
            self.show_popup('خطأ', 'يرجى إدخال اسم الفحص')
            return

        # Create radiology record
        record_id = str(uuid.uuid4())[:8]

        # Copy files to radiology folder
        saved_files = []
        if self.selected_files:
            radiology_folder = 'radiology_files'
            if not os.path.exists(radiology_folder):
                os.makedirs(radiology_folder)

            for file_path in self.selected_files:
                try:
                    file_name = f"{record_id}_{os.path.basename(file_path)}"
                    dest_path = os.path.join(radiology_folder, file_name)
                    shutil.copy2(file_path, dest_path)
                    saved_files.append({
                        'original_name': os.path.basename(file_path),
                        'saved_name': file_name,
                        'path': dest_path,
                        'size': os.path.getsize(dest_path)
                    })
                except Exception as e:
                    print(f"خطأ في نسخ الملف {file_path}: {e}")

        record = {
            'id': record_id,
            'patient_id': self.current_patient.get('id'),
            'patient_name': f"{self.current_patient.get('first_name', '')} {self.current_patient.get('last_name', '')}",
            'type': self.type_spinner.text,
            'test_name': self.test_name_input.text.strip(),
            'description': self.description_input.text.strip(),
            'results': self.results_input.text.strip(),
            'notes': self.notes_input.text.strip(),
            'files': saved_files,
            'doctor_name': 'د. أحمد محمد',
            'date': datetime.now().isoformat(),
            'created_at': datetime.now().isoformat()
        }

        # Save to file
        try:
            radiology_file = 'radiology_records.json'
            records = []

            if os.path.exists(radiology_file):
                with open(radiology_file, 'r', encoding='utf-8') as f:
                    records = json.load(f)

            records.insert(0, record)

            with open(radiology_file, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)

            self.show_popup('نجح', 'تم حفظ سجل الأشعة/التحليل بنجاح!')
            self.clear_form()
            self.load_radiology_history()

        except Exception as e:
            self.show_popup('خطأ', f'فشل في حفظ السجل: {str(e)}')

    def clear_form(self):
        """مسح النموذج"""
        self.test_name_input.text = ''
        self.description_input.text = ''
        self.results_input.text = ''
        self.notes_input.text = ''
        self.type_spinner.text = 'اختر نوع الفحص'
        self.patient_spinner.text = 'اختر مريض'
        self.current_patient = None
        self.selected_files.clear()
        self.update_files_list()

    def load_radiology_history(self, dt=None):
        """تحميل سجلات الأشعة والتحاليل"""
        self.history_layout.clear_widgets()

        try:
            if os.path.exists('radiology_records.json'):
                with open('radiology_records.json', 'r', encoding='utf-8') as f:
                    records = json.load(f)

                # Show last 10 records
                recent_records = records[:10]

                if recent_records:
                    for record in recent_records:
                        record_item = self.create_history_item(record)
                        self.history_layout.add_widget(record_item)
                else:
                    no_data = Label(
                        text='لا توجد سجلات سابقة',
                        font_size=dp(14),
                        color=(0.6, 0.6, 0.6, 1),
                        size_hint_y=None,
                        height=dp(40)
                    )
                    self.history_layout.add_widget(no_data)
        except:
            pass

    def load_patient_radiology_history(self):
        """تحميل سجلات مريض محدد"""
        if not self.current_patient:
            return

        self.history_layout.clear_widgets()

        try:
            if os.path.exists('radiology_records.json'):
                with open('radiology_records.json', 'r', encoding='utf-8') as f:
                    records = json.load(f)

                # Filter by patient
                patient_records = [r for r in records if r.get('patient_id') == self.current_patient.get('id')]

                if patient_records:
                    for record in patient_records:
                        record_item = self.create_history_item(record)
                        self.history_layout.add_widget(record_item)
                else:
                    no_data = Label(
                        text='لا توجد سجلات لهذا المريض',
                        font_size=dp(14),
                        color=(0.6, 0.6, 0.6, 1),
                        size_hint_y=None,
                        height=dp(40)
                    )
                    self.history_layout.add_widget(no_data)
        except:
            pass

    def show_popup(self, title, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message, text_size=(dp(250), None)))

        close_btn = Button(text='موافق', size_hint_y=None, height=dp(50))
        content.add_widget(close_btn)

        popup = Popup(title=title, content=content, size_hint=(0.8, 0.5))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

    def go_back(self, instance):
        """العودة للشاشة الرئيسية"""
        self.manager.current = 'doctor_home'
