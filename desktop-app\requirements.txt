# Desktop Application Requirements
# متطلبات تطبيق سطح المكتب

# GUI Framework
tkinter  # Usually comes with Python
customtkinter==5.2.2

# HTTP Client
requests==2.31.0

# Image handling
pillow==10.1.0

# Date and time
python-dateutil==2.8.2

# Data validation
pydantic==2.5.0

# JSON handling
orjson==3.9.10

# Configuration
python-dotenv==1.0.0

# WebSocket client for real-time notifications
websocket-client==1.7.0

# Charts and graphs
matplotlib==3.8.2
pandas==2.1.4

# PDF generation
reportlab==4.0.7

# Excel export
openpyxl==3.1.2
