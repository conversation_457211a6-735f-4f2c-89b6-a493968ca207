# 🚀 التشغيل السريع - جهازين منفصلين

## 📦 **الملفات المطلوبة:**

### للجهاز الأول (موظف الاستقبال):
- ✅ `network_shared_data.py`
- ✅ `registrar_network.py`
- ✅ `start_registrar.bat`

### للجهاز الثاني (الطبيب):
- ✅ `network_shared_data.py`
- ✅ `doctor_simple.py`
- ✅ `start_doctor.bat`

---

## ⚡ **التشغيل السريع:**

### 🖥️ **الجهاز الأول (موظف الاستقبال):**
1. **ضع الملفات في مجلد** (مثل `C:\ClinicData`)
2. **اضغط مرتين على** `start_registrar.bat`
3. **انتظر حتى يظهر التطبيق**

### 🖥️ **الجهاز الثاني (الطبيب):**
1. **ضع الملفات في مجلد** (مثل `C:\ClinicData`)
2. **اضغط مرتين على** `start_doctor.bat`
3. **انتظر حتى يظهر التطبيق**

---

## 🔗 **ربط الجهازين:**

### الطريقة الأولى: نسخ ملف البيانات
1. **بعد إضافة مريض في الجهاز الأول**
2. **انسخ ملف** `clinic_data.json`
3. **ضعه في مجلد الجهاز الثاني**
4. **في تطبيق الطبيب، اضغط "تحديث البيانات"**

### الطريقة الثانية: مجلد شبكة مشترك
1. **شارك مجلد البيانات من الجهاز الأول**
2. **اتصل بالمجلد المشترك من الجهاز الثاني**
3. **شغل التطبيقات من نفس المجلد المشترك**

---

## 🧪 **اختبار سريع:**

### في جهاز موظف الاستقبال:
1. **اضغط "إضافة مريض جديد"**
2. **املأ البيانات واضغط "حفظ"**

### في جهاز الطبيب:
1. **انتظر 10 ثواني**
2. **يجب أن تظهر نافذة تنبيه**
3. **اذهب إلى "التنبيهات الجديدة"**

---

## 🔧 **حل المشاكل:**

### ❌ **"Python غير مثبت"**
- حمل Python من: https://python.org
- تأكد من إضافته إلى PATH

### ❌ **"ملف غير موجود"**
- تأكد من وضع جميع الملفات في نفس المجلد
- تأكد من أسماء الملفات صحيحة

### ❌ **"لا تظهر التنبيهات"**
- انسخ ملف `clinic_data.json` بين الجهازين
- اضغط "تحديث البيانات" في تطبيق الطبيب

---

## 🎯 **النتيجة:**
✅ **نظام كامل لإدارة العيادة**
✅ **يعمل على جهازين منفصلين**
✅ **تنبيهات فورية للطبيب**
✅ **بدون خادم معقد**

**🚀 ابدأ الآن: اضغط على ملفات .bat للتشغيل!**
