"""
Main application window
النافذة الرئيسية للتطبيق
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class MainWindow:
    """Main application window"""
    
    def __init__(self, root, api_client, current_user, on_logout_callback):
        self.root = root
        self.api_client = api_client
        self.current_user = current_user
        self.on_logout_callback = on_logout_callback
        
        # Setup main window
        self.setup_window()
        self.create_menu()
        self.create_widgets()
        self.update_status()
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title(f"Clinineo - {self.current_user['first_name']} {self.current_user['last_name']}")
        
        # Configure grid weights
        self.root.grid_rowconfigure(1, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
    
    def create_menu(self):
        """Create application menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="تسجيل خروج", command=self.logout)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # Patients menu
        if self.can_manage_patients():
            patients_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المرضى", menu=patients_menu)
            patients_menu.add_command(label="إضافة مريض جديد", command=self.show_add_patient)
            patients_menu.add_command(label="قائمة المرضى", command=self.show_patients_list)
            patients_menu.add_command(label="البحث عن مريض", command=self.show_patient_search)
        
        # Appointments menu
        if self.can_manage_appointments():
            appointments_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المواعيد", menu=appointments_menu)
            appointments_menu.add_command(label="حجز موعد جديد", command=self.show_add_appointment)
            appointments_menu.add_command(label="مواعيد اليوم", command=self.show_today_appointments)
            appointments_menu.add_command(label="جميع المواعيد", command=self.show_all_appointments)
        
        # Medical records menu
        if self.can_access_medical_records():
            records_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="السجلات الطبية", menu=records_menu)
            records_menu.add_command(label="إضافة سجل طبي", command=self.show_add_medical_record)
            records_menu.add_command(label="عرض السجلات", command=self.show_medical_records)
        
        # Reports menu
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="لوحة المعلومات", command=self.show_dashboard)
        reports_menu.add_command(label="إحصائيات الأمراض", command=self.show_disease_stats)
        reports_menu.add_command(label="إحصائيات العلاجات", command=self.show_treatment_stats)
        
        # Users menu (admin only)
        if self.current_user['role'] == 'admin':
            users_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المستخدمين", menu=users_menu)
            users_menu.add_command(label="إضافة مستخدم", command=self.show_add_user)
            users_menu.add_command(label="إدارة المستخدمين", command=self.show_users_list)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_widgets(self):
        """Create main window widgets"""
        # Toolbar
        toolbar = ttk.Frame(self.root)
        toolbar.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        # Quick action buttons
        if self.can_manage_patients():
            ttk.Button(toolbar, text="مريض جديد", command=self.show_add_patient).pack(side=tk.LEFT, padx=2)
        
        if self.can_manage_appointments():
            ttk.Button(toolbar, text="موعد جديد", command=self.show_add_appointment).pack(side=tk.LEFT, padx=2)
        
        if self.can_access_medical_records():
            ttk.Button(toolbar, text="سجل طبي", command=self.show_add_medical_record).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(toolbar, text="التقارير", command=self.show_dashboard).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # User info
        user_info = f"المستخدم: {self.current_user['first_name']} {self.current_user['last_name']} ({self.get_role_display()})"
        ttk.Label(toolbar, text=user_info).pack(side=tk.LEFT, padx=10)
        
        # Logout button
        ttk.Button(toolbar, text="تسجيل خروج", command=self.logout).pack(side=tk.RIGHT, padx=2)
        
        # Main content area
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        self.main_frame.grid_rowconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Welcome message
        self.show_welcome()
        
        # Status bar
        self.status_bar = ttk.Label(self.root, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=2, column=0, sticky="ew")
    
    def show_welcome(self):
        """Show welcome message"""
        # Clear main frame
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        welcome_frame = ttk.Frame(self.main_frame)
        welcome_frame.pack(expand=True, fill=tk.BOTH)
        
        # Welcome message
        welcome_label = ttk.Label(
            welcome_frame,
            text=f"مرحباً {self.current_user['first_name']} {self.current_user['last_name']}",
            font=('Arial', 20, 'bold')
        )
        welcome_label.pack(pady=50)
        
        role_label = ttk.Label(
            welcome_frame,
            text=f"دورك في النظام: {self.get_role_display()}",
            font=('Arial', 14)
        )
        role_label.pack(pady=10)
        
        # Quick stats
        stats_frame = ttk.LabelFrame(welcome_frame, text="إحصائيات سريعة", padding="20")
        stats_frame.pack(pady=30, padx=50, fill=tk.X)
        
        # Load and display quick stats
        self.load_quick_stats(stats_frame)
    
    def load_quick_stats(self, parent):
        """Load and display quick statistics"""
        try:
            response = self.api_client.get('/reports/dashboard')
            if response.success and response.data:
                stats = response.data
                
                # Create stats grid
                stats_grid = ttk.Frame(parent)
                stats_grid.pack(fill=tk.X)
                
                # Configure grid
                for i in range(3):
                    stats_grid.grid_columnconfigure(i, weight=1)
                
                # Display stats
                self.create_stat_widget(stats_grid, "إجمالي المرضى", stats.get('total_patients', 0), 0, 0)
                self.create_stat_widget(stats_grid, "مواعيد اليوم", stats.get('today_appointments', 0), 0, 1)
                self.create_stat_widget(stats_grid, "الأطباء النشطين", stats.get('active_doctors', 0), 0, 2)
                
            else:
                ttk.Label(parent, text="لا يمكن تحميل الإحصائيات").pack()
                
        except Exception as e:
            ttk.Label(parent, text=f"خطأ في تحميل الإحصائيات: {str(e)}").pack()
    
    def create_stat_widget(self, parent, title, value, row, col):
        """Create a statistics widget"""
        stat_frame = ttk.Frame(parent, relief=tk.RAISED, borderwidth=1)
        stat_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
        
        ttk.Label(stat_frame, text=str(value), font=('Arial', 18, 'bold')).pack(pady=5)
        ttk.Label(stat_frame, text=title, font=('Arial', 10)).pack(pady=2)
    
    def get_role_display(self):
        """Get role display name in Arabic"""
        role_map = {
            'admin': 'مدير النظام',
            'doctor': 'طبيب',
            'registrar': 'موظف استقبال'
        }
        return role_map.get(self.current_user['role'], self.current_user['role'])
    
    def can_manage_patients(self):
        """Check if user can manage patients"""
        return self.current_user['role'] in ['admin', 'registrar']
    
    def can_manage_appointments(self):
        """Check if user can manage appointments"""
        return self.current_user['role'] in ['admin', 'registrar']
    
    def can_access_medical_records(self):
        """Check if user can access medical records"""
        return self.current_user['role'] in ['admin', 'doctor']
    
    def update_status(self, message="جاهز"):
        """Update status bar"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_bar.config(text=f"{message} - {timestamp}")
    
    def logout(self):
        """Handle logout"""
        if messagebox.askyesno("تسجيل خروج", "هل تريد تسجيل الخروج؟"):
            self.on_logout_callback()
    
    # Placeholder methods for menu actions
    def show_add_patient(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_patients_list(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_patient_search(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_add_appointment(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_today_appointments(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_all_appointments(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_add_medical_record(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_medical_records(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_dashboard(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_disease_stats(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_treatment_stats(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_add_user(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_users_list(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_about(self):
        messagebox.showinfo(
            "حول البرنامج",
            "نظام Clinineo لإدارة العيادة\nالإصدار 1.0.0\n\nتم تطويره بـ Python و FastAPI"
        )
