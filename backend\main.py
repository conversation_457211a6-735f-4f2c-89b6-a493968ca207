"""
Clinineo Backend - FastAPI Application
نظام إدارة العيادة - الخادم الخلفي
"""

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import asyncio
from typing import List
import json

from app.core.config import settings
from app.core.database import create_tables
from app.api.routes import auth, users, patients, appointments, medical_records, reports, files, notifications
from app.core.websocket_manager import WebSocketManager

# WebSocket manager for real-time notifications
websocket_manager = WebSocketManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting Clinineo Backend...")
    await create_tables()
    print("✅ Database tables created")
    yield
    # Shutdown
    print("🛑 Shutting down Clinineo Backend...")

# Create FastAPI app
app = FastAPI(
    title="Clinineo API",
    description="نظام إدارة العيادة - واجهة برمجة التطبيقات",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # في الإنتاج، حدد النطاقات المسموحة
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files for uploaded files
app.mount("/static", StaticFiles(directory="uploads"), name="static")

# Include API routes
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(users.router, prefix="/api/users", tags=["Users"])
app.include_router(patients.router, prefix="/api/patients", tags=["Patients"])
app.include_router(appointments.router, prefix="/api/appointments", tags=["Appointments"])
app.include_router(medical_records.router, prefix="/api/medical-records", tags=["Medical Records"])
app.include_router(reports.router, prefix="/api/reports", tags=["Reports"])
app.include_router(files.router, prefix="/api/files", tags=["Files"])
app.include_router(notifications.router, prefix="/api/notifications", tags=["Notifications"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "مرحباً بك في نظام Clinineo لإدارة العيادة",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "message": "النظام يعمل بشكل طبيعي",
        "version": "1.0.0"
    }

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for real-time notifications"""
    await websocket_manager.connect(websocket, user_id)
    try:
        while True:
            # Keep connection alive and listen for messages
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle different message types
            if message.get("type") == "ping":
                await websocket.send_text(json.dumps({"type": "pong"}))
            elif message.get("type") == "join_room":
                room = message.get("room")
                await websocket_manager.join_room(user_id, room)
            elif message.get("type") == "leave_room":
                room = message.get("room")
                await websocket_manager.leave_room(user_id, room)
                
    except WebSocketDisconnect:
        await websocket_manager.disconnect(user_id)

# Global WebSocket manager instance
app.state.websocket_manager = websocket_manager

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
