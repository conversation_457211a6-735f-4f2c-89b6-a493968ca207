"""
WebSocket Manager for real-time notifications
مدير WebSocket للتنبيهات الفورية
"""

import json
import asyncio
from typing import Dict, List, Set
from fastapi import WebSocket
from datetime import datetime

class WebSocketManager:
    """Manages WebSocket connections for real-time notifications"""
    
    def __init__(self):
        # Store active connections: {user_id: websocket}
        self.active_connections: Dict[str, WebSocket] = {}
        # Store user rooms: {user_id: set of rooms}
        self.user_rooms: Dict[str, Set[str]] = {}
        # Store room members: {room: set of user_ids}
        self.room_members: Dict[str, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_rooms[user_id] = set()
        print(f"✅ User {user_id} connected via WebSocket")
    
    async def disconnect(self, user_id: str):
        """Remove a WebSocket connection"""
        if user_id in self.active_connections:
            # Remove from all rooms
            if user_id in self.user_rooms:
                for room in self.user_rooms[user_id].copy():
                    await self.leave_room(user_id, room)
                del self.user_rooms[user_id]
            
            # Remove connection
            del self.active_connections[user_id]
            print(f"❌ User {user_id} disconnected from WebSocket")
    
    async def join_room(self, user_id: str, room: str):
        """Add user to a room"""
        if user_id in self.user_rooms:
            self.user_rooms[user_id].add(room)
            
            if room not in self.room_members:
                self.room_members[room] = set()
            self.room_members[room].add(user_id)
            
            print(f"👥 User {user_id} joined room {room}")
    
    async def leave_room(self, user_id: str, room: str):
        """Remove user from a room"""
        if user_id in self.user_rooms:
            self.user_rooms[user_id].discard(room)
            
        if room in self.room_members:
            self.room_members[room].discard(user_id)
            # Remove empty rooms
            if not self.room_members[room]:
                del self.room_members[room]
                
        print(f"👋 User {user_id} left room {room}")
    
    async def send_personal_message(self, user_id: str, message: dict):
        """Send message to a specific user"""
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]
                await websocket.send_text(json.dumps(message))
                print(f"📨 Sent personal message to user {user_id}")
                return True
            except Exception as e:
                print(f"❌ Failed to send message to user {user_id}: {e}")
                await self.disconnect(user_id)
                return False
        return False
    
    async def send_room_message(self, room: str, message: dict, exclude_user: str = None):
        """Send message to all users in a room"""
        if room in self.room_members:
            sent_count = 0
            failed_users = []
            
            for user_id in self.room_members[room].copy():
                if exclude_user and user_id == exclude_user:
                    continue
                    
                success = await self.send_personal_message(user_id, message)
                if success:
                    sent_count += 1
                else:
                    failed_users.append(user_id)
            
            # Clean up failed connections
            for user_id in failed_users:
                await self.disconnect(user_id)
            
            print(f"📢 Sent room message to {sent_count} users in room {room}")
            return sent_count
        return 0
    
    async def broadcast_message(self, message: dict, exclude_user: str = None):
        """Send message to all connected users"""
        sent_count = 0
        failed_users = []
        
        for user_id in list(self.active_connections.keys()):
            if exclude_user and user_id == exclude_user:
                continue
                
            success = await self.send_personal_message(user_id, message)
            if success:
                sent_count += 1
            else:
                failed_users.append(user_id)
        
        # Clean up failed connections
        for user_id in failed_users:
            await self.disconnect(user_id)
        
        print(f"📡 Broadcast message to {sent_count} users")
        return sent_count
    
    async def notify_patient_registered(self, patient_data: dict, registered_by: dict):
        """Send notification when a new patient is registered"""
        message = {
            "type": "patient_registered",
            "title": "مريض جديد",
            "message": f"تم تسجيل مريض جديد: {patient_data.get('first_name')} {patient_data.get('last_name')}",
            "data": {
                "patient": patient_data,
                "registered_by": registered_by,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Send to all doctors
        await self.send_room_message("doctors", message)
        
        # Send to admins
        await self.send_room_message("admins", message)
    
    async def notify_appointment_booked(self, appointment_data: dict, patient_data: dict, doctor_id: str):
        """Send notification when an appointment is booked"""
        message = {
            "type": "appointment_booked",
            "title": "موعد جديد",
            "message": f"تم حجز موعد جديد مع المريض: {patient_data.get('first_name')} {patient_data.get('last_name')}",
            "data": {
                "appointment": appointment_data,
                "patient": patient_data,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Send to specific doctor
        await self.send_personal_message(doctor_id, message)
        
        # Send to admins
        await self.send_room_message("admins", message)
    
    async def notify_medical_record_added(self, record_data: dict, patient_data: dict):
        """Send notification when a medical record is added"""
        message = {
            "type": "medical_record_added",
            "title": "سجل طبي جديد",
            "message": f"تم إضافة سجل طبي للمريض: {patient_data.get('first_name')} {patient_data.get('last_name')}",
            "data": {
                "record": record_data,
                "patient": patient_data,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Send to admins
        await self.send_room_message("admins", message)
    
    def get_connection_stats(self) -> dict:
        """Get WebSocket connection statistics"""
        return {
            "total_connections": len(self.active_connections),
            "total_rooms": len(self.room_members),
            "users_per_room": {room: len(members) for room, members in self.room_members.items()},
            "connected_users": list(self.active_connections.keys())
        }
