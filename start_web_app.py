#!/usr/bin/env python3
"""
Clinineo Web App Launcher
مشغل تطبيق Clinineo الويب
"""

import subprocess
import sys
import socket
import time
import webbrowser
from threading import Timer

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def check_flask():
    """التحقق من وجود Flask"""
    try:
        import flask
        return True
    except ImportError:
        return False

def install_flask():
    """تثبيت Flask"""
    print("🔧 تثبيت Flask...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "flask"])
        print("✅ تم تثبيت Flask بنجاح!")
        return True
    except:
        print("❌ فشل في تثبيت Flask")
        return False

def open_browser_delayed(url, delay=3):
    """فتح المتصفح بعد تأخير"""
    def open_browser():
        try:
            webbrowser.open(url)
        except:
            pass
    
    Timer(delay, open_browser).start()

def main():
    """الدالة الرئيسية"""
    print("🌐" + "="*60)
    print("🏥 Clinineo Web App - مشغل التطبيق الويب")
    print("="*62)
    
    # التحقق من Flask
    if not check_flask():
        print("📦 Flask غير مثبت. جاري التثبيت...")
        if not install_flask():
            print("❌ فشل في تثبيت Flask. يرجى تثبيته يدوياً:")
            print("   pip install flask")
            input("اضغط Enter للخروج...")
            return
    
    # الحصول على IP
    local_ip = get_local_ip()
    port = 8080
    
    print(f"🔗 الروابط المتاحة:")
    print(f"   📱 الصفحة الرئيسية: http://{local_ip}:{port}/")
    print(f"   🏥 تطبيق الاستقبال: http://{local_ip}:{port}/registrar")
    print(f"   🩺 تطبيق الطبيب: http://{local_ip}:{port}/doctor")
    print("="*62)
    print(f"📱 للاستخدام على الموبايل:")
    print(f"   1. تأكد من اتصال الهاتف بنفس الشبكة")
    print(f"   2. افتح المتصفح على الهاتف")
    print(f"   3. اذهب إلى: http://{local_ip}:{port}/")
    print(f"   4. اختر التطبيق المطلوب")
    print(f"   5. أضف للشاشة الرئيسية")
    print("="*62)
    print(f"🔧 بدء تشغيل الخادم...")
    print(f"🛑 اضغط Ctrl+C للإيقاف")
    print("="*62)
    
    # فتح المتصفح تلقائياً بعد 3 ثوان
    open_browser_delayed(f"http://{local_ip}:{port}/")
    
    # تشغيل التطبيق
    try:
        import clinineo_web_app
        clinineo_web_app.app.run(host='0.0.0.0', port=port, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
