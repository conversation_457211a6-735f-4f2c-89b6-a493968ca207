"""
Reports and statistics routes
مسارات التقارير والإحصائيات
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, extract
from datetime import date, datetime

from ...core.database import get_db, Patient, Appointment, MedicalRecord, User
from ...core.security import get_current_active_user, require_any_role

router = APIRouter()

@router.get("/dashboard")
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
) -> Dict[str, Any]:
    """Get dashboard statistics"""
    today = date.today()
    
    # Total patients
    total_patients = db.query(func.count(Patient.id)).scalar()
    
    # Today's appointments
    today_appointments = db.query(func.count(Appointment.id)).filter(
        Appointment.appointment_date == today,
        Appointment.status != "cancelled"
    ).scalar()
    
    # This month's appointments
    month_appointments = db.query(func.count(Appointment.id)).filter(
        extract('year', Appointment.appointment_date) == today.year,
        extract('month', Appointment.appointment_date) == today.month,
        Appointment.status != "cancelled"
    ).scalar()
    
    # Active doctors
    active_doctors = db.query(func.count(User.id)).filter(
        User.role == "doctor",
        User.is_active == True
    ).scalar()
    
    # Pending appointments
    pending_appointments = db.query(func.count(Appointment.id)).filter(
        Appointment.status == "scheduled",
        Appointment.appointment_date >= today
    ).scalar()
    
    # Completed appointments this month
    completed_appointments = db.query(func.count(Appointment.id)).filter(
        extract('year', Appointment.appointment_date) == today.year,
        extract('month', Appointment.appointment_date) == today.month,
        Appointment.status == "completed"
    ).scalar()
    
    return {
        "total_patients": total_patients or 0,
        "today_appointments": today_appointments or 0,
        "month_appointments": month_appointments or 0,
        "active_doctors": active_doctors or 0,
        "pending_appointments": pending_appointments or 0,
        "completed_appointments": completed_appointments or 0
    }

@router.get("/diseases")
async def get_disease_statistics(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
) -> Dict[str, Any]:
    """Get disease statistics"""
    query = db.query(MedicalRecord.diagnosis)
    
    # Apply date filters
    if start_date:
        query = query.filter(MedicalRecord.created_at >= start_date)
    if end_date:
        query = query.filter(MedicalRecord.created_at <= end_date)
    
    # If user is a doctor, only show their records
    if current_user.role == "doctor":
        query = query.filter(MedicalRecord.doctor_id == current_user.id)
    
    records = query.all()
    
    # Count diagnoses
    disease_count = {}
    for record in records:
        if record.diagnosis:
            diagnosis = record.diagnosis.lower().strip()
            disease_count[diagnosis] = disease_count.get(diagnosis, 0) + 1
    
    # Sort and limit
    sorted_diseases = sorted(disease_count.items(), key=lambda x: x[1], reverse=True)[:limit]
    
    total_records = len(records)
    diseases = [
        {
            "disease": disease.title(),
            "count": count,
            "percentage": round((count / total_records) * 100, 1) if total_records > 0 else 0
        }
        for disease, count in sorted_diseases
    ]
    
    return {
        "diseases": diseases,
        "total_records": total_records
    }

@router.get("/treatments")
async def get_treatment_statistics(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
) -> Dict[str, Any]:
    """Get treatment statistics"""
    query = db.query(MedicalRecord.treatment, MedicalRecord.medications)
    
    # Apply date filters
    if start_date:
        query = query.filter(MedicalRecord.created_at >= start_date)
    if end_date:
        query = query.filter(MedicalRecord.created_at <= end_date)
    
    # If user is a doctor, only show their records
    if current_user.role == "doctor":
        query = query.filter(MedicalRecord.doctor_id == current_user.id)
    
    records = query.all()
    
    # Count treatments
    treatment_count = {}
    medication_count = {}
    
    for record in records:
        # Count treatments
        if record.treatment:
            treatment = record.treatment.lower().strip()
            treatment_count[treatment] = treatment_count.get(treatment, 0) + 1
        
        # Count medications
        if record.medications:
            for med in record.medications:
                if isinstance(med, dict) and 'name' in med:
                    med_name = med['name'].lower().strip()
                    medication_count[med_name] = medication_count.get(med_name, 0) + 1
    
    # Sort and limit
    sorted_treatments = sorted(treatment_count.items(), key=lambda x: x[1], reverse=True)[:limit]
    sorted_medications = sorted(medication_count.items(), key=lambda x: x[1], reverse=True)[:limit]
    
    total_records = len(records)
    
    treatments = [
        {
            "treatment": treatment.title(),
            "count": count,
            "percentage": round((count / total_records) * 100, 1) if total_records > 0 else 0
        }
        for treatment, count in sorted_treatments
    ]
    
    medications = [
        {
            "medication": medication.title(),
            "count": count,
            "percentage": round((count / total_records) * 100, 1) if total_records > 0 else 0
        }
        for medication, count in sorted_medications
    ]
    
    return {
        "treatments": treatments,
        "medications": medications,
        "total_records": total_records
    }

@router.get("/appointments")
async def get_appointment_statistics(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
) -> Dict[str, Any]:
    """Get appointment statistics"""
    query = db.query(Appointment)
    
    # Apply date filters
    if start_date:
        query = query.filter(Appointment.appointment_date >= start_date)
    if end_date:
        query = query.filter(Appointment.appointment_date <= end_date)
    
    # If user is a doctor, only show their appointments
    if current_user.role == "doctor":
        query = query.filter(Appointment.doctor_id == current_user.id)
    
    appointments = query.all()
    
    # Count by status
    status_count = {}
    type_count = {}
    daily_count = {}
    
    for appointment in appointments:
        # Count by status
        status_count[appointment.status] = status_count.get(appointment.status, 0) + 1
        
        # Count by type
        type_count[appointment.type] = type_count.get(appointment.type, 0) + 1
        
        # Count by date
        date_str = str(appointment.appointment_date)
        daily_count[date_str] = daily_count.get(date_str, 0) + 1
    
    total_appointments = len(appointments)
    
    # Convert to lists
    status_stats = [
        {
            "status": status,
            "count": count,
            "percentage": round((count / total_appointments) * 100, 1) if total_appointments > 0 else 0
        }
        for status, count in status_count.items()
    ]
    
    type_stats = [
        {
            "type": type_name,
            "count": count,
            "percentage": round((count / total_appointments) * 100, 1) if total_appointments > 0 else 0
        }
        for type_name, count in type_count.items()
    ]
    
    daily_stats = [
        {"date": date_str, "count": count}
        for date_str, count in sorted(daily_count.items())
    ]
    
    return {
        "by_status": status_stats,
        "by_type": type_stats,
        "daily": daily_stats,
        "total_appointments": total_appointments
    }
