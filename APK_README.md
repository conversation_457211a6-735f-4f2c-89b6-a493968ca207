# 📱 Clinineo APK - تطبيقات أندرويد جاهزة

## 🎯 **نظرة عامة:**

**Clinineo APK** هي ملفات تطبيقات أندرويد جاهزة للتثبيت لنظام إدارة العيادات المتطور.

### **📦 الحزمة تشمل:**
- 🏥 **Clinineo Registrar APK** - تطبيق الاستقبال
- 🩺 **Clinineo Doctor APK** - تطبيق الطبيب

---

## 🚀 **التشغيل السريع:**

### **الطريقة 1: بناء APK (موصى بها)**
```bash
# تثبيت أدوات البناء
python install_build_tools.py

# بناء ملفات APK
python build_apk.py
```

### **الطريقة 2: استخدام APK جاهزة**
إذا كانت متوفرة، حمل الملفات من:
- `clinineo_registrar_*.apk`
- `clinineo_doctor_*.apk`

---

## 📲 **التثبيت على الأندرويد:**

### **الخطوة 1: إعداد الهاتف**
1. اذهب إلى **الإعدادات** → **الأمان والخصوصية**
2. فعّل **"مصادر غير معروفة"** أو **"تثبيت تطبيقات غير معروفة"**
3. أو اذهب إلى **الإعدادات** → **التطبيقات** → **الوصول الخاص** → **تثبيت تطبيقات غير معروفة**

### **الخطوة 2: نقل الملفات**
انسخ ملفات APK إلى الهاتف عبر:
- 📱 **كابل USB**
- 📧 **البريد الإلكتروني**
- ☁️ **Google Drive / Dropbox**
- 📶 **البلوتوث**
- 💬 **WhatsApp / Telegram**

### **الخطوة 3: التثبيت**

#### **🏥 على هاتف الاستقبال:**
1. افتح مدير الملفات
2. اذهب إلى مجلد التحميلات
3. اضغط على `clinineo_registrar_*.apk`
4. اضغط **"تثبيت"**
5. انتظر انتهاء التثبيت
6. اضغط **"فتح"** أو ابحث عن أيقونة التطبيق

#### **🩺 على هاتف الطبيب:**
1. كرر نفس الخطوات مع `clinineo_doctor_*.apk`

---

## 🔧 **الإعداد والتشغيل:**

### **الترتيب الصحيح:**

#### **1. 🏥 تشغيل تطبيق الاستقبال أولاً:**
- افتح تطبيق **Clinineo Registrar**
- انتظر حتى ترى **"🟢 الخادم يعمل"**
- لاحظ عنوان IP المعروض (مثل: *************)

#### **2. 🩺 تشغيل تطبيق الطبيب ثانياً:**
- افتح تطبيق **Clinineo Doctor**
- اذهب إلى **⚙️ الإعدادات**
- اضغط **🌐 إعدادات الاتصال**
- أدخل عنوان IP من تطبيق الاستقبال: `http://*************:8080`
- اضغط **"اختبار الاتصال"**
- إذا نجح، اضغط **"حفظ"**
- ارجع للشاشة الرئيسية وانتظر **"🟢 متصل"**

---

## 🧪 **الاختبار:**

### **اختبار سريع:**
1. في تطبيق الاستقبال، اضغط **"➕ إضافة مريض جديد"**
2. املأ البيانات:
   - الاسم الأول: أحمد
   - الاسم الأخير: محمد
   - تاريخ الميلاد: 1990-01-01
   - الجنس: ذكر
   - الهاتف: 0123456789
3. اضغط **"💾 حفظ المريض"**
4. في تطبيق الطبيب، انتظر 10 ثواني
5. ستظهر نافذة تنبيه منبثقة **"🔔 مريض جديد"**!

---

## 📱 **متطلبات الأندرويد:**

### **الحد الأدنى:**
- **Android 5.0** (API 21) أو أحدث
- **RAM:** 2 GB أو أكثر
- **التخزين:** 100 MB مساحة فارغة
- **الشبكة:** WiFi للاتصال بين الهاتفين

### **موصى به:**
- **Android 8.0** أو أحدث
- **RAM:** 4 GB أو أكثر
- **معالج:** Quad-core أو أفضل

---

## 🌐 **متطلبات الشبكة:**

### **الإعداد المطلوب:**
- 📶 **راوتر WiFi** محلي
- 📱 **هاتفين متصلين بنفس الشبكة**
- 🚫 **لا حاجة للإنترنت** (شبكة محلية فقط)

### **التحقق من الشبكة:**
1. تأكد أن كلا الهاتفين متصلين بنفس WiFi
2. تحقق من عنوان IP في إعدادات WiFi
3. يجب أن يكونا في نفس النطاق (مثل: 192.168.1.x)

---

## 🔧 **حل المشاكل:**

### **❌ "فشل في التثبيت"**
**الحلول:**
- تأكد من تفعيل "مصادر غير معروفة"
- تحقق من وجود مساحة كافية
- أعد تشغيل الهاتف وحاول مرة أخرى
- تأكد من سلامة ملف APK

### **❌ "التطبيق لا يفتح"**
**الحلول:**
- تأكد من توافق إصدار الأندرويد
- أعد تثبيت التطبيق
- امسح cache التطبيق من الإعدادات
- تحقق من وجود RAM كافي

### **❌ "غير متصل بتطبيق الاستقبال"**
**الحلول:**
- تأكد من تشغيل تطبيق الاستقبال أولاً
- تحقق من اتصال الهاتفين بنفس WiFi
- تأكد من صحة عنوان IP في الإعدادات
- جرب إعادة تشغيل كلا التطبيقين

### **❌ "لا تظهر التنبيهات"**
**الحلول:**
- تأكد من ظهور "🟢 متصل" في تطبيق الطبيب
- أضف مريض جديد في تطبيق الاستقبال
- انتظر 10-15 ثانية للتحديث التلقائي
- اضغط "🔄 تحديث البيانات" في تطبيق الطبيب

---

## 📊 **معلومات التطبيقات:**

### **🏥 Clinineo Registrar:**
- **الحجم:** ~15-25 MB
- **الوظائف:**
  - إضافة وإدارة المرضى
  - خادم HTTP مدمج
  - نسخ احتياطي للبيانات
  - إحصائيات وتقارير

### **🩺 Clinineo Doctor:**
- **الحجم:** ~15-25 MB
- **الوظائف:**
  - استقبال التنبيهات الفورية
  - عرض وبحث المرضى
  - إعدادات الاتصال
  - لوحة معلومات طبية

---

## 🔐 **الأمان والخصوصية:**

### **✅ آمن:**
- البيانات محفوظة محلياً فقط
- لا يتم إرسال بيانات للإنترنت
- التواصل عبر الشبكة المحلية فقط
- لا يطلب صلاحيات حساسة

### **🔒 الصلاحيات المطلوبة:**
- **الشبكة:** للاتصال بين التطبيقين
- **التخزين:** لحفظ البيانات محلياً

---

## 🆕 **التحديثات:**

### **للحصول على تحديثات:**
1. احتفظ بملفات المصدر الأصلية
2. أعد بناء APK عند توفر تحديثات
3. ثبت الإصدار الجديد فوق القديم

### **الإصدار الحالي:**
- **النسخة:** 1.0.0
- **تاريخ البناء:** [يُحدد عند البناء]
- **الميزات:** جميع الميزات الأساسية

---

## 📞 **الدعم:**

### **للمساعدة:**
- 📖 راجع `APK_BUILD_GUIDE.md` للتفاصيل
- 📋 اقرأ `CLININEO_GUIDE.md` للميزات
- 🔧 استخدم `build_apk.py` لبناء جديد

### **المشاكل الشائعة:**
- تأكد من تشغيل الاستقبال قبل الطبيب
- تحقق من اتصال الشبكة
- راجع إعدادات الأمان في الهاتف

---

## 🎉 **الاستمتاع بالتطبيق:**

بعد التثبيت والإعداد الناجح:

### **🏥 موظف الاستقبال يمكنه:**
- إضافة مرضى جدد بسهولة
- عرض قائمة شاملة بالمرضى
- إنشاء نسخ احتياطية
- مراقبة حالة النظام

### **🩺 الطبيب يمكنه:**
- استقبال تنبيهات فورية
- البحث في قاعدة المرضى
- عرض تفاصيل المرضى
- متابعة الإحصائيات

---

**📱 Clinineo APK - نظام إدارة العيادات في جيبك!**

*"تطوير الرعاية الصحية من خلال التكنولوجيا المحمولة"*
