#!/usr/bin/env python3
"""
Clinineo Mobile Server - خادم الهاتف
نسخة مبسطة من نظام Clinineo تعمل على الهاتف كخادم
"""

from flask import Flask, render_template_string, request, jsonify, redirect
import json
import os
import uuid
from datetime import datetime
import socket
import threading
import time

app = Flask(__name__)

# بيانات مؤقتة في الذاكرة
clinic_data = {
    "patients": [],
    "prescriptions": [],
    "notifications": [],
    "appointments": []
}

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def save_data():
    """حفظ البيانات في ملف"""
    try:
        with open('clinineo_mobile_data.json', 'w', encoding='utf-8') as f:
            json.dump(clinic_data, f, ensure_ascii=False, indent=2)
        return True
    except:
        return False

def load_data():
    """تحميل البيانات من ملف"""
    global clinic_data
    try:
        if os.path.exists('clinineo_mobile_data.json'):
            with open('clinineo_mobile_data.json', 'r', encoding='utf-8') as f:
                clinic_data = json.load(f)
        return True
    except:
        return False

# قالب HTML مبسط للموبايل
MOBILE_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Clinineo Mobile</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: {{ bg_color }};
            padding: 10px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 100%;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header {
            background: {{ header_color }};
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: {{ btn_color }};
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-size: 16px;
            border: none;
            cursor: pointer;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .card {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid {{ accent_color }};
        }
        
        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: {{ accent_color }};
        }
        
        .back-btn {
            background: #6c757d;
            padding: 8px 15px;
            font-size: 14px;
            margin-bottom: 15px;
            display: inline-block;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>{{ header_title }}</h2>
            <p>{{ header_subtitle }}</p>
        </div>
        {{ content }}
    </div>
    
    <script>
        // تحديث تلقائي كل 30 ثانية
        setInterval(function() {
            if (window.location.pathname === '/') {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
"""

@app.route('/')
def home():
    """الصفحة الرئيسية"""
    local_ip = get_local_ip()
    
    content = f"""
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">{len(clinic_data['patients'])}</div>
            <div>المرضى</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{len(clinic_data['prescriptions'])}</div>
            <div>الروشتات</div>
        </div>
    </div>
    
    <a href="/registrar" class="btn">🏥 الاستقبال</a>
    <a href="/doctor" class="btn">🩺 الطبيب</a>
    
    <div class="card">
        <h4>📱 معلومات الخادم:</h4>
        <p><strong>IP:</strong> {local_ip}:5000</p>
        <p><strong>للاتصال من جهاز آخر:</strong></p>
        <p>http://{local_ip}:5000/</p>
    </div>
    
    <div class="card">
        <h4>🔗 روابط سريعة:</h4>
        <p><strong>الاستقبال:</strong> http://{local_ip}:5000/registrar</p>
        <p><strong>الطبيب:</strong> http://{local_ip}:5000/doctor</p>
    </div>
    """
    
    return render_template_string(MOBILE_TEMPLATE,
        title="الرئيسية",
        header_title="🏥 Clinineo Mobile Server",
        header_subtitle="خادم الهاتف",
        bg_color="linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        header_color="linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        btn_color="#667eea",
        accent_color="#667eea",
        content=content
    )

@app.route('/registrar')
def registrar():
    """تطبيق الاستقبال"""
    content = f"""
    <a href="/" class="back-btn">← العودة</a>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">{len(clinic_data['patients'])}</div>
            <div>إجمالي المرضى</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{len([p for p in clinic_data['patients'] if p.get('created_at', '').startswith(datetime.now().strftime('%Y-%m-%d'))])}</div>
            <div>مرضى اليوم</div>
        </div>
    </div>
    
    <a href="/add_patient" class="btn">➕ إضافة مريض جديد</a>
    <a href="/patients" class="btn">👥 عرض المرضى</a>
    <a href="/search" class="btn">🔍 البحث</a>
    """
    
    return render_template_string(MOBILE_TEMPLATE,
        title="الاستقبال",
        header_title="🏥 الاستقبال",
        header_subtitle="إدارة المرضى",
        bg_color="linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)",
        header_color="linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)",
        btn_color="#2196f3",
        accent_color="#2196f3",
        content=content
    )

@app.route('/doctor')
def doctor():
    """تطبيق الطبيب"""
    unread_notifications = len([n for n in clinic_data['notifications'] if not n.get('read', False)])
    
    content = f"""
    <a href="/" class="back-btn">← العودة</a>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">{len(clinic_data['patients'])}</div>
            <div>المرضى</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{unread_notifications}</div>
            <div>تنبيهات جديدة</div>
        </div>
    </div>
    
    <a href="/notifications" class="btn">🔔 التنبيهات ({unread_notifications})</a>
    <a href="/patients" class="btn">👥 المرضى</a>
    <a href="/prescriptions" class="btn">📝 الروشتات</a>
    <a href="/reports" class="btn">📊 التقارير</a>
    """
    
    return render_template_string(MOBILE_TEMPLATE,
        title="الطبيب",
        header_title="🩺 د. أحمد محمد",
        header_subtitle="طب عام",
        bg_color="linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)",
        header_color="linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)",
        btn_color="#4caf50",
        accent_color="#4caf50",
        content=content
    )

@app.route('/add_patient', methods=['GET', 'POST'])
def add_patient():
    """إضافة مريض جديد"""
    if request.method == 'POST':
        patient = {
            'id': str(uuid.uuid4())[:8],
            'name': request.form.get('name', ''),
            'phone': request.form.get('phone', ''),
            'age': request.form.get('age', ''),
            'gender': request.form.get('gender', ''),
            'notes': request.form.get('notes', ''),
            'created_at': datetime.now().isoformat()
        }
        
        clinic_data['patients'].insert(0, patient)
        
        # إضافة تنبيه
        notification = {
            'id': str(uuid.uuid4())[:8],
            'message': f'مريض جديد: {patient["name"]}',
            'read': False,
            'created_at': datetime.now().isoformat()
        }
        clinic_data['notifications'].insert(0, notification)
        
        save_data()
        return redirect('/registrar?success=تم إضافة المريض بنجاح')
    
    success = request.args.get('success', '')
    
    content = f"""
    <a href="/registrar" class="back-btn">← العودة</a>
    
    {f'<div class="success">{success}</div>' if success else ''}
    
    <form method="POST">
        <div class="form-group">
            <label>الاسم الكامل *</label>
            <input type="text" name="name" class="form-control" required>
        </div>
        
        <div class="form-group">
            <label>رقم الهاتف *</label>
            <input type="tel" name="phone" class="form-control" required>
        </div>
        
        <div class="form-group">
            <label>العمر</label>
            <input type="number" name="age" class="form-control">
        </div>
        
        <div class="form-group">
            <label>الجنس</label>
            <select name="gender" class="form-control">
                <option value="">اختر الجنس</option>
                <option value="male">ذكر</option>
                <option value="female">أنثى</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>ملاحظات</label>
            <textarea name="notes" class="form-control" rows="3"></textarea>
        </div>
        
        <button type="submit" class="btn">💾 حفظ المريض</button>
    </form>
    """
    
    return render_template_string(MOBILE_TEMPLATE,
        title="إضافة مريض",
        header_title="➕ إضافة مريض جديد",
        header_subtitle="بيانات المريض",
        bg_color="linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)",
        header_color="linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)",
        btn_color="#4caf50",
        accent_color="#4caf50",
        content=content
    )

@app.route('/patients')
def patients():
    """عرض المرضى"""
    patients_html = ""
    for patient in clinic_data['patients'][:10]:
        patients_html += f"""
        <div class="card">
            <h4>👤 {patient.get('name', '')}</h4>
            <p>📞 {patient.get('phone', '')}</p>
            <p>📅 {patient.get('created_at', '')[:10]}</p>
        </div>
        """
    
    if not patients_html:
        patients_html = '<div class="card">لا توجد مرضى</div>'
    
    content = f"""
    <a href="/registrar" class="back-btn">← العودة</a>
    
    <h3>👥 المرضى ({len(clinic_data['patients'])})</h3>
    
    {patients_html}
    """
    
    return render_template_string(MOBILE_TEMPLATE,
        title="المرضى",
        header_title="👥 قائمة المرضى",
        header_subtitle=f"إجمالي {len(clinic_data['patients'])} مريض",
        bg_color="linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)",
        header_color="linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)",
        btn_color="#2196f3",
        accent_color="#2196f3",
        content=content
    )

@app.route('/notifications')
def notifications():
    """التنبيهات"""
    # تحديد التنبيهات كمقروءة
    for notification in clinic_data['notifications']:
        notification['read'] = True
    save_data()
    
    notifications_html = ""
    for notification in clinic_data['notifications'][:10]:
        notifications_html += f"""
        <div class="card">
            <p>{notification.get('message', '')}</p>
            <small>{notification.get('created_at', '')[:16].replace('T', ' ')}</small>
        </div>
        """
    
    if not notifications_html:
        notifications_html = '<div class="card">لا توجد تنبيهات</div>'
    
    content = f"""
    <a href="/doctor" class="back-btn">← العودة</a>
    
    <h3>🔔 التنبيهات</h3>
    
    {notifications_html}
    """
    
    return render_template_string(MOBILE_TEMPLATE,
        title="التنبيهات",
        header_title="🔔 التنبيهات",
        header_subtitle="تنبيهات العيادة",
        bg_color="linear-gradient(135deg, #ff9800 0%, #ffc107 100%)",
        header_color="linear-gradient(135deg, #ff9800 0%, #ffc107 100%)",
        btn_color="#ff9800",
        accent_color="#ff9800",
        content=content
    )

def start_mobile_server():
    """بدء تشغيل خادم الهاتف"""
    load_data()
    
    local_ip = get_local_ip()
    port = 5000
    
    print("📱" + "="*50)
    print("🏥 Clinineo Mobile Server - خادم الهاتف")
    print("="*52)
    print(f"🔗 الروابط المتاحة:")
    print(f"   📱 الصفحة الرئيسية: http://{local_ip}:{port}/")
    print(f"   🏥 تطبيق الاستقبال: http://{local_ip}:{port}/registrar")
    print(f"   🩺 تطبيق الطبيب: http://{local_ip}:{port}/doctor")
    print("="*52)
    print(f"📱 للاتصال من جهاز آخر:")
    print(f"   1. تأكد من الاتصال بنفس الشبكة")
    print(f"   2. افتح المتصفح")
    print(f"   3. اذهب إلى: http://{local_ip}:{port}/")
    print("="*52)
    print(f"🔧 خادم الهاتف يعمل على: {local_ip}:{port}")
    print(f"💾 البيانات محفوظة في: clinineo_mobile_data.json")
    print(f"🛑 اضغط Ctrl+C للإيقاف")
    print("="*52)
    
    try:
        app.run(host='0.0.0.0', port=port, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف خادم الهاتف")
        save_data()
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == '__main__':
    start_mobile_server()
