# 🚀 تعليمات تشغيل نظام Clinineo

## المتطلبات الأساسية

### 1. تثبيت Python
- قم بتحميل Python 3.8+ من: https://python.org/downloads/
- **مهم:** تأكد من تحديد "Add Python to PATH" أثناء التثبيت
- تحقق من التثبيت: افتح Command Prompt واكتب `python --version`

### 2. تثبيت المكتبات المطلوبة
افتح Command Prompt في مجلد المشروع واكتب:
```bash
pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart python-dotenv pydantic-settings requests pillow
```

## 🔧 طرق التشغيل

### الطريقة 1: التشغيل التلقائي (الأسهل)
```bash
# تشغيل الخادم
python simple_test.py
```

### الطريقة 2: التشغيل اليدوي

#### أ) تشغيل الخادم (Backend)
```bash
cd backend
python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload
```

#### ب) تشغيل تطبيق سطح المكتب
افتح نافذة Command Prompt جديدة:
```bash
cd desktop-app
python main.py
```

### الطريقة 3: استخدام ملفات التشغيل
```bash
# للخادم
python start_server.py

# للتطبيق
cd desktop-app
python run.py
```

## 🌐 اختبار الخادم

بعد تشغيل الخادم، افتح المتصفح وانتقل إلى:

- **الصفحة الرئيسية:** http://127.0.0.1:8000
- **توثيق API:** http://127.0.0.1:8000/docs
- **فحص الصحة:** http://127.0.0.1:8000/health

إذا رأيت رسالة ترحيب، فالخادم يعمل بنجاح! ✅

## 🖥️ تشغيل تطبيق سطح المكتب

1. تأكد من تشغيل الخادم أولاً
2. افتح نافذة Command Prompt جديدة
3. انتقل إلى مجلد `desktop-app`
4. اكتب: `python main.py`

### بيانات تسجيل الدخول:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** admin123

## 🔧 حل المشاكل الشائعة

### مشكلة: "python is not recognized"
**الحل:** تأكد من إضافة Python إلى PATH:
1. ابحث عن "Environment Variables" في Windows
2. أضف مسار Python إلى PATH
3. أعد تشغيل Command Prompt

### مشكلة: "No module named 'fastapi'"
**الحل:** ثبت المكتبات:
```bash
pip install fastapi uvicorn
```

### مشكلة: "Address already in use"
**الحل:** غير المنفذ أو أوقف العملية السابقة:
```bash
# استخدم منفذ مختلف
python -m uvicorn main:app --host 127.0.0.1 --port 8001
```

### مشكلة: "Connection refused"
**الحل:** تأكد من تشغيل الخادم أولاً قبل تطبيق سطح المكتب

## 📱 الميزات المتاحة

### للأدمن:
- إدارة المستخدمين
- عرض جميع التقارير
- إعدادات النظام

### للمسجل:
- تسجيل المرضى
- حجز المواعيد
- إدارة الاستقبال

### للطبيب:
- استقبال التنبيهات
- عرض المرضى والمواعيد
- إضافة السجلات الطبية
- رفع الملفات الطبية

## 🆘 الحصول على المساعدة

إذا واجهت مشاكل:
1. تأكد من تثبيت Python بشكل صحيح
2. تأكد من تثبيت جميع المكتبات المطلوبة
3. تأكد من تشغيل الخادم قبل التطبيق
4. تحقق من عدم استخدام منفذ 8000 من برنامج آخر

## 🎯 الخطوات السريعة

1. **ثبت Python** (إذا لم يكن مثبتاً)
2. **افتح Command Prompt** في مجلد المشروع
3. **ثبت المكتبات:** `pip install fastapi uvicorn requests`
4. **شغل الخادم:** `python simple_test.py`
5. **افتح المتصفح:** http://127.0.0.1:8000
6. **شغل التطبيق:** `cd desktop-app && python main.py`
7. **سجل دخول:** <EMAIL> / admin123

🎉 **مبروك! النظام يعمل الآن**
