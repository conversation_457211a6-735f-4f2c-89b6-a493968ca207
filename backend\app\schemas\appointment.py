"""
Appointment schemas for API requests and responses
نماذج المواعيد لطلبات واستجابات API
"""

from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime, date, time

class AppointmentBase(BaseModel):
    """Base appointment schema"""
    patient_id: str
    doctor_id: str
    appointment_date: date
    appointment_time: time
    type: str
    notes: Optional[str] = None

class AppointmentCreate(AppointmentBase):
    """Schema for creating a new appointment"""
    status: str = "scheduled"
    
    @validator('type')
    def validate_type(cls, v):
        allowed_types = ['consultation', 'follow_up', 'emergency', 'checkup']
        if v not in allowed_types:
            raise ValueError(f'Type must be one of: {", ".join(allowed_types)}')
        return v
    
    @validator('status')
    def validate_status(cls, v):
        allowed_statuses = ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled']
        if v not in allowed_statuses:
            raise ValueError(f'Status must be one of: {", ".join(allowed_statuses)}')
        return v
    
    @validator('appointment_date')
    def validate_appointment_date(cls, v):
        if v < date.today():
            raise ValueError('Appointment date cannot be in the past')
        return v

class AppointmentUpdate(BaseModel):
    """Schema for updating appointment"""
    appointment_date: Optional[date] = None
    appointment_time: Optional[time] = None
    type: Optional[str] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class AppointmentResponse(AppointmentBase):
    """Schema for appointment response"""
    id: str
    status: str
    created_at: datetime
    
    class Config:
        from_attributes = True
