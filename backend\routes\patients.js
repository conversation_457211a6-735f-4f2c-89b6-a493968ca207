const express = require('express');
const Joi = require('joi');
const { createClient } = require('@supabase/supabase-js');
const { authenticateToken, registrarOrAdmin, anyAuthenticated } = require('../middleware/auth');

const router = express.Router();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Validation schema
const patientSchema = Joi.object({
  firstName: Joi.string().min(2).required(),
  lastName: Joi.string().min(2).required(),
  dateOfBirth: Joi.date().required(),
  gender: Joi.string().valid('male', 'female').required(),
  phone: Joi.string().required(),
  email: Joi.string().email().optional(),
  address: Joi.string().optional(),
  emergencyContact: Joi.string().optional(),
  bloodType: Joi.string().valid('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-').optional(),
  allergies: Joi.string().optional(),
  medicalHistory: Joi.string().optional(),
  insuranceNumber: Joi.string().optional()
});

// @route   POST /api/patients
// @desc    Create new patient
// @access  Private (Registrar/Admin)
router.post('/', authenticateToken, registrarOrAdmin, async (req, res) => {
  try {
    // Validate input
    const { error } = patientSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const patientData = {
      ...req.body,
      date_of_birth: req.body.dateOfBirth,
      first_name: req.body.firstName,
      last_name: req.body.lastName,
      emergency_contact: req.body.emergencyContact,
      blood_type: req.body.bloodType,
      medical_history: req.body.medicalHistory,
      insurance_number: req.body.insuranceNumber,
      created_by: req.user.id,
      created_at: new Date().toISOString()
    };

    // Remove camelCase fields
    delete patientData.firstName;
    delete patientData.lastName;
    delete patientData.dateOfBirth;
    delete patientData.emergencyContact;
    delete patientData.bloodType;
    delete patientData.medicalHistory;
    delete patientData.insuranceNumber;

    // Check if patient already exists (by phone)
    const { data: existingPatient } = await supabase
      .from('patients')
      .select('id')
      .eq('phone', req.body.phone)
      .single();

    if (existingPatient) {
      return res.status(400).json({
        success: false,
        message: 'Patient with this phone number already exists'
      });
    }

    // Create patient
    const { data: newPatient, error: createError } = await supabase
      .from('patients')
      .insert([patientData])
      .select()
      .single();

    if (createError) {
      console.error('Create patient error:', createError);
      return res.status(500).json({
        success: false,
        message: 'Failed to create patient'
      });
    }

    // Emit socket event for real-time notification
    const io = req.app.get('io');
    if (io) {
      io.emit('patient-registered', {
        patient: {
          id: newPatient.id,
          firstName: newPatient.first_name,
          lastName: newPatient.last_name,
          phone: newPatient.phone,
          gender: newPatient.gender
        },
        registeredBy: {
          id: req.user.id,
          name: `${req.user.first_name} ${req.user.last_name}`
        }
      });
    }

    res.status(201).json({
      success: true,
      message: 'Patient created successfully',
      data: {
        patient: {
          id: newPatient.id,
          firstName: newPatient.first_name,
          lastName: newPatient.last_name,
          dateOfBirth: newPatient.date_of_birth,
          gender: newPatient.gender,
          phone: newPatient.phone,
          email: newPatient.email,
          address: newPatient.address,
          emergencyContact: newPatient.emergency_contact,
          bloodType: newPatient.blood_type,
          allergies: newPatient.allergies,
          medicalHistory: newPatient.medical_history,
          insuranceNumber: newPatient.insurance_number,
          createdAt: newPatient.created_at
        }
      }
    });
  } catch (error) {
    console.error('Create patient error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/patients
// @desc    Get all patients with pagination and search
// @access  Private
router.get('/', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const offset = (page - 1) * limit;

    let query = supabase
      .from('patients')
      .select('*', { count: 'exact' });

    // Add search filter
    if (search) {
      query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,phone.ilike.%${search}%`);
    }

    // Add pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: patients, error, count } = await query;

    if (error) {
      console.error('Get patients error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch patients'
      });
    }

    // Transform data
    const transformedPatients = patients.map(patient => ({
      id: patient.id,
      firstName: patient.first_name,
      lastName: patient.last_name,
      dateOfBirth: patient.date_of_birth,
      gender: patient.gender,
      phone: patient.phone,
      email: patient.email,
      address: patient.address,
      emergencyContact: patient.emergency_contact,
      bloodType: patient.blood_type,
      allergies: patient.allergies,
      medicalHistory: patient.medical_history,
      insuranceNumber: patient.insurance_number,
      createdAt: patient.created_at
    }));

    res.json({
      success: true,
      data: {
        patients: transformedPatients,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: limit
        }
      }
    });
  } catch (error) {
    console.error('Get patients error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/patients/:id
// @desc    Get patient by ID
// @access  Private
router.get('/:id', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const { data: patient, error } = await supabase
      .from('patients')
      .select('*')
      .eq('id', req.params.id)
      .single();

    if (error || !patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    res.json({
      success: true,
      data: {
        patient: {
          id: patient.id,
          firstName: patient.first_name,
          lastName: patient.last_name,
          dateOfBirth: patient.date_of_birth,
          gender: patient.gender,
          phone: patient.phone,
          email: patient.email,
          address: patient.address,
          emergencyContact: patient.emergency_contact,
          bloodType: patient.blood_type,
          allergies: patient.allergies,
          medicalHistory: patient.medical_history,
          insuranceNumber: patient.insurance_number,
          createdAt: patient.created_at
        }
      }
    });
  } catch (error) {
    console.error('Get patient error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
