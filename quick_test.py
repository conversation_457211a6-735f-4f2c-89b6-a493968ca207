#!/usr/bin/env python3
"""
Quick Test - اختبار سريع
اختبار سريع لمعرفة سبب عدم عمل التطبيق على الهاتف
"""

import socket
import subprocess
import sys

def get_local_ip():
    """الحصول على IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def check_port(port):
    """فحص المنفذ"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        result = s.connect_ex(('127.0.0.1', port))
        s.close()
        return result == 0
    except:
        return False

def test_flask():
    """اختبار Flask"""
    try:
        import flask
        return True, flask.__version__
    except ImportError:
        return False, "غير مثبت"

def main():
    print("🔍" + "="*50)
    print("🧪 اختبار سريع - Clinineo")
    print("="*52)
    
    # فحص IP
    local_ip = get_local_ip()
    print(f"🌐 عنوان IP المحلي: {local_ip}")
    
    # فحص Flask
    flask_ok, flask_version = test_flask()
    print(f"📦 Flask: {'✅ مثبت' if flask_ok else '❌ غير مثبت'} ({flask_version})")
    
    # فحص المنافذ
    ports = [5000, 8080, 9090]
    print(f"🔌 فحص المنافذ:")
    for port in ports:
        status = "🟢 متاح" if not check_port(port) else "🔴 مستخدم"
        print(f"   المنفذ {port}: {status}")
    
    print("="*52)
    print(f"📱 جرب هذه العناوين على الهاتف:")
    print(f"   http://{local_ip}:5000/")
    print(f"   http://{local_ip}:8080/")
    print(f"   http://{local_ip}:9090/")
    print("="*52)
    
    # تشغيل خادم بسيط
    if flask_ok:
        print("🚀 تشغيل خادم اختبار بسيط...")
        
        from flask import Flask
        app = Flask(__name__)
        
        @app.route('/')
        def test():
            return f"""
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>اختبار الاتصال</title>
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        text-align: center;
                        padding: 50px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        min-height: 100vh;
                        margin: 0;
                    }}
                    .container {{
                        background: white;
                        color: #333;
                        padding: 30px;
                        border-radius: 20px;
                        display: inline-block;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    }}
                    h1 {{ color: #4CAF50; }}
                    .info {{ margin: 20px 0; padding: 15px; background: #e8f5e8; border-radius: 10px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🎉 تم الاتصال بنجاح!</h1>
                    <div class="info">
                        <strong>✅ الهاتف متصل بالخادم</strong><br>
                        📱 IP: {local_ip}<br>
                        🕒 الوقت: {__import__('datetime').datetime.now().strftime('%H:%M:%S')}
                    </div>
                    <p>🎯 هذا يعني أن التطبيق الكامل سيعمل أيضاً!</p>
                </div>
            </body>
            </html>
            """
        
        try:
            print(f"🔗 افتح هذا الرابط على الهاتف:")
            print(f"   http://{local_ip}:5555/")
            print("🛑 اضغط Ctrl+C للإيقاف")
            print("="*52)
            
            app.run(host='0.0.0.0', port=5555, debug=False)
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف الاختبار")
        except Exception as e:
            print(f"❌ خطأ: {e}")
    else:
        print("❌ يجب تثبيت Flask أولاً:")
        print("   pip install flask")

if __name__ == "__main__":
    main()
