const express = require('express');
const Joi = require('joi');
const { createClient } = require('@supabase/supabase-js');
const { authenticateToken, doctorOrAdmin, anyAuthenticated } = require('../middleware/auth');

const router = express.Router();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Validation schema
const medicalRecordSchema = Joi.object({
  patientId: Joi.string().uuid().required(),
  appointmentId: Joi.string().uuid().optional(),
  diagnosis: Joi.string().required(),
  symptoms: Joi.string().optional(),
  treatment: Joi.string().required(),
  medications: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    dosage: Joi.string().required(),
    frequency: Joi.string().required(),
    duration: Joi.string().required(),
    instructions: Joi.string().optional()
  })).optional(),
  labTests: Joi.array().items(Joi.string()).optional(),
  followUpDate: Joi.date().optional(),
  notes: Joi.string().optional()
});

// @route   POST /api/medical-records
// @desc    Create new medical record
// @access  Private (Doctor/Admin)
router.post('/', authenticateToken, doctorOrAdmin, async (req, res) => {
  try {
    // Validate input
    const { error } = medicalRecordSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { 
      patientId, 
      appointmentId, 
      diagnosis, 
      symptoms, 
      treatment, 
      medications, 
      labTests, 
      followUpDate, 
      notes 
    } = req.body;

    // Check if patient exists
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('*')
      .eq('id', patientId)
      .single();

    if (patientError || !patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Create medical record
    const recordData = {
      patient_id: patientId,
      appointment_id: appointmentId,
      doctor_id: req.user.id,
      diagnosis,
      symptoms,
      treatment,
      medications: medications ? JSON.stringify(medications) : null,
      lab_tests: labTests ? JSON.stringify(labTests) : null,
      follow_up_date: followUpDate,
      notes,
      created_at: new Date().toISOString()
    };

    const { data: newRecord, error: createError } = await supabase
      .from('medical_records')
      .insert([recordData])
      .select(`
        *,
        patients:patient_id(id, first_name, last_name, phone),
        doctors:doctor_id(id, first_name, last_name, specialization)
      `)
      .single();

    if (createError) {
      console.error('Create medical record error:', createError);
      return res.status(500).json({
        success: false,
        message: 'Failed to create medical record'
      });
    }

    // Update appointment status if provided
    if (appointmentId) {
      await supabase
        .from('appointments')
        .update({ status: 'completed' })
        .eq('id', appointmentId);
    }

    res.status(201).json({
      success: true,
      message: 'Medical record created successfully',
      data: {
        record: {
          id: newRecord.id,
          patientId: newRecord.patient_id,
          appointmentId: newRecord.appointment_id,
          doctorId: newRecord.doctor_id,
          diagnosis: newRecord.diagnosis,
          symptoms: newRecord.symptoms,
          treatment: newRecord.treatment,
          medications: newRecord.medications ? JSON.parse(newRecord.medications) : null,
          labTests: newRecord.lab_tests ? JSON.parse(newRecord.lab_tests) : null,
          followUpDate: newRecord.follow_up_date,
          notes: newRecord.notes,
          createdAt: newRecord.created_at,
          patient: newRecord.patients ? {
            id: newRecord.patients.id,
            firstName: newRecord.patients.first_name,
            lastName: newRecord.patients.last_name,
            phone: newRecord.patients.phone
          } : null,
          doctor: newRecord.doctors ? {
            id: newRecord.doctors.id,
            firstName: newRecord.doctors.first_name,
            lastName: newRecord.doctors.last_name,
            specialization: newRecord.doctors.specialization
          } : null
        }
      }
    });
  } catch (error) {
    console.error('Create medical record error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/medical-records
// @desc    Get medical records with filters
// @access  Private
router.get('/', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const patientId = req.query.patientId;
    const doctorId = req.query.doctorId;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('medical_records')
      .select(`
        *,
        patients:patient_id(id, first_name, last_name, phone, gender),
        doctors:doctor_id(id, first_name, last_name, specialization)
      `, { count: 'exact' });

    // Apply filters
    if (patientId) {
      query = query.eq('patient_id', patientId);
    }
    if (doctorId) {
      query = query.eq('doctor_id', doctorId);
    }

    // If user is a doctor, only show their records
    if (req.user.role === 'doctor') {
      query = query.eq('doctor_id', req.user.id);
    }

    // Add pagination and ordering
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: records, error, count } = await query;

    if (error) {
      console.error('Get medical records error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch medical records'
      });
    }

    // Transform data
    const transformedRecords = records.map(record => ({
      id: record.id,
      patientId: record.patient_id,
      appointmentId: record.appointment_id,
      doctorId: record.doctor_id,
      diagnosis: record.diagnosis,
      symptoms: record.symptoms,
      treatment: record.treatment,
      medications: record.medications ? JSON.parse(record.medications) : null,
      labTests: record.lab_tests ? JSON.parse(record.lab_tests) : null,
      followUpDate: record.follow_up_date,
      notes: record.notes,
      createdAt: record.created_at,
      patient: record.patients ? {
        id: record.patients.id,
        firstName: record.patients.first_name,
        lastName: record.patients.last_name,
        phone: record.patients.phone,
        gender: record.patients.gender
      } : null,
      doctor: record.doctors ? {
        id: record.doctors.id,
        firstName: record.doctors.first_name,
        lastName: record.doctors.last_name,
        specialization: record.doctors.specialization
      } : null
    }));

    res.json({
      success: true,
      data: {
        records: transformedRecords,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: limit
        }
      }
    });
  } catch (error) {
    console.error('Get medical records error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/medical-records/:id
// @desc    Get medical record by ID
// @access  Private
router.get('/:id', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    let query = supabase
      .from('medical_records')
      .select(`
        *,
        patients:patient_id(id, first_name, last_name, phone, gender, date_of_birth),
        doctors:doctor_id(id, first_name, last_name, specialization)
      `)
      .eq('id', req.params.id);

    // If user is a doctor, only show their records
    if (req.user.role === 'doctor') {
      query = query.eq('doctor_id', req.user.id);
    }

    const { data: record, error } = await query.single();

    if (error || !record) {
      return res.status(404).json({
        success: false,
        message: 'Medical record not found'
      });
    }

    res.json({
      success: true,
      data: {
        record: {
          id: record.id,
          patientId: record.patient_id,
          appointmentId: record.appointment_id,
          doctorId: record.doctor_id,
          diagnosis: record.diagnosis,
          symptoms: record.symptoms,
          treatment: record.treatment,
          medications: record.medications ? JSON.parse(record.medications) : null,
          labTests: record.lab_tests ? JSON.parse(record.lab_tests) : null,
          followUpDate: record.follow_up_date,
          notes: record.notes,
          createdAt: record.created_at,
          patient: record.patients ? {
            id: record.patients.id,
            firstName: record.patients.first_name,
            lastName: record.patients.last_name,
            phone: record.patients.phone,
            gender: record.patients.gender,
            dateOfBirth: record.patients.date_of_birth
          } : null,
          doctor: record.doctors ? {
            id: record.doctors.id,
            firstName: record.doctors.first_name,
            lastName: record.doctors.last_name,
            specialization: record.doctors.specialization
          } : null
        }
      }
    });
  } catch (error) {
    console.error('Get medical record error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
