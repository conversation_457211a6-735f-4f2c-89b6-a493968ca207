#!/usr/bin/env python3
"""
Quick start for Clinineo - Minimal working version
بداية سريعة لـ Clinineo - نسخة مبسطة
"""

print("🏥 Clinineo Clinic Management System")
print("=" * 50)

# Simple FastAPI server
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(
    title="Clinineo API",
    description="نظام إدارة العيادة",
    version="1.0.0"
)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple routes
@app.get("/")
def read_root():
    return {
        "message": "مرحباً بك في نظام Clinineo لإدارة العيادة",
        "version": "1.0.0",
        "status": "working",
        "docs": "http://127.0.0.1:8000/docs"
    }

@app.get("/health")
def health_check():
    return {
        "status": "healthy",
        "message": "النظام يعمل بشكل طبيعي"
    }

@app.post("/api/auth/login")
def login(credentials: dict):
    """Simple login for testing"""
    email = credentials.get("email")
    password = credentials.get("password")
    
    # Simple validation
    if email == "<EMAIL>" and password == "admin123":
        return {
            "success": True,
            "access_token": "test-token-123",
            "token_type": "bearer",
            "user": {
                "id": "1",
                "email": email,
                "first_name": "System",
                "last_name": "Admin",
                "role": "admin"
            }
        }
    else:
        return {
            "success": False,
            "message": "Invalid credentials"
        }

@app.get("/api/reports/dashboard")
def get_dashboard():
    """Simple dashboard data"""
    return {
        "total_patients": 25,
        "today_appointments": 8,
        "active_doctors": 3,
        "pending_appointments": 5,
        "completed_appointments": 15,
        "month_appointments": 45
    }

if __name__ == "__main__":
    print("🚀 Starting Clinineo Server...")
    print("📍 Server: http://127.0.0.1:8000")
    print("📚 API Docs: http://127.0.0.1:8000/docs")
    print("🔍 Health: http://127.0.0.1:8000/health")
    print("=" * 50)
    print("Press Ctrl+C to stop")
    
    try:
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Try: pip install fastapi uvicorn")
