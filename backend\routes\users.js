const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const { authenticateToken, adminOnly, anyAuthenticated } = require('../middleware/auth');

const router = express.Router();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// @route   GET /api/users
// @desc    Get all users
// @access  Private (Admin)
router.get('/', authenticateToken, adminOnly, async (req, res) => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, first_name, last_name, role, phone, specialization, is_active, created_at, last_login')
      .order('created_at', { ascending: false });

    if (error) {
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch users'
      });
    }

    const transformedUsers = users.map(user => ({
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      role: user.role,
      phone: user.phone,
      specialization: user.specialization,
      isActive: user.is_active,
      createdAt: user.created_at,
      lastLogin: user.last_login
    }));

    res.json({
      success: true,
      data: { users: transformedUsers }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/users/doctors
// @desc    Get all doctors
// @access  Private
router.get('/doctors', authenticateToken, anyAuthenticated, async (req, res) => {
  try {
    const { data: doctors, error } = await supabase
      .from('users')
      .select('id, first_name, last_name, specialization, phone')
      .eq('role', 'doctor')
      .eq('is_active', true)
      .order('first_name', { ascending: true });

    if (error) {
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch doctors'
      });
    }

    const transformedDoctors = doctors.map(doctor => ({
      id: doctor.id,
      firstName: doctor.first_name,
      lastName: doctor.last_name,
      specialization: doctor.specialization,
      phone: doctor.phone
    }));

    res.json({
      success: true,
      data: { doctors: transformedDoctors }
    });
  } catch (error) {
    console.error('Get doctors error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/users/:id/status
// @desc    Update user status (activate/deactivate)
// @access  Private (Admin)
router.put('/:id/status', authenticateToken, adminOnly, async (req, res) => {
  try {
    const { isActive } = req.body;
    const userId = req.params.id;

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'isActive must be a boolean value'
      });
    }

    // Prevent admin from deactivating themselves
    if (userId === req.user.id && !isActive) {
      return res.status(400).json({
        success: false,
        message: 'Cannot deactivate your own account'
      });
    }

    const { data: updatedUser, error } = await supabase
      .from('users')
      .update({ is_active: isActive })
      .eq('id', userId)
      .select('id, email, first_name, last_name, role, is_active')
      .single();

    if (error || !updatedUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      data: {
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          firstName: updatedUser.first_name,
          lastName: updatedUser.last_name,
          role: updatedUser.role,
          isActive: updatedUser.is_active
        }
      }
    });
  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
