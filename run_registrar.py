#!/usr/bin/env python3
"""
Direct launcher for Registrar App
مشغل مباشر لتطبيق موظف الاستقبال
"""

import sys
import os

print("🚀 تشغيل تطبيق موظف الاستقبال...")

# التحقق من وجود الملف
if not os.path.exists('registrar_app.py'):
    print("❌ ملف registrar_app.py غير موجود")
    input("اضغط Enter للخروج...")
    sys.exit(1)

try:
    # تشغيل تطبيق موظف الاستقبال
    exec(open('registrar_app.py').read())
except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    input("اضغط Enter للخروج...")
