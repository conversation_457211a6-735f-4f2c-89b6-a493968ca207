# 📋 دليل التشغيل خطوة بخطوة - جهازين منفصلين

## 🎯 **الهدف:**
- **الجهاز الأول:** تطبيق موظف الاستقبال
- **الجهاز الثاني:** تطبيق الطبيب
- **الربط:** عبر ملف مشترك على الشبكة المحلية

---

## 📋 **الخطوة 1: إعداد الجهاز الأول (موظف الاستقبال)**

### 1️⃣ إنشاء مجلد البيانات:
```cmd
mkdir C:\ClinicData
cd C:\ClinicData
```

### 2️⃣ وضع الملفات المطلوبة:
ضع هذه الملفات في `C:\ClinicData`:
- ✅ `network_shared_data.py`
- ✅ `registrar_network.py`

### 3️⃣ تشغيل التطبيق:
```cmd
cd C:\ClinicData
python registrar_network.py
```

### 4️⃣ التحقق من عمل التطبيق:
- ✅ يجب أن تظهر نافذة التطبيق
- ✅ يجب أن تظهر معلومات الجهاز في الأسفل
- ✅ يجب أن يظهر "تم تسجيل الدخول تلقائياً"

---

## 📋 **الخطوة 2: إعداد الجهاز الثاني (الطبيب)**

### 1️⃣ إنشاء نفس المجلد:
```cmd
mkdir C:\ClinicData
cd C:\ClinicData
```

### 2️⃣ وضع الملفات المطلوبة:
ضع هذه الملفات في `C:\ClinicData`:
- ✅ `network_shared_data.py`
- ✅ `doctor_simple.py`

### 3️⃣ تشغيل التطبيق:
```cmd
cd C:\ClinicData
python doctor_simple.py
```

### 4️⃣ التحقق من عمل التطبيق:
- ✅ يجب أن تظهر نافذة التطبيق
- ✅ يجب أن تظهر "تم تسجيل الدخول تلقائياً"
- ✅ يجب أن تظهر "لا توجد تنبيهات جديدة"

---

## 📋 **الخطوة 3: ربط الجهازين**

### الطريقة الأولى: مجلد شبكة مشترك (الأفضل)

#### في الجهاز الأول:
1. **انقر بالزر الأيمن على مجلد** `C:\ClinicData`
2. **اختر "Properties" → "Sharing"**
3. **اضغط "Share"** واختر "Everyone"
4. **اعطه اسم مثل** `ClinicShare`

#### في الجهاز الثاني:
1. **افتح File Explorer**
2. **اكتب في شريط العنوان:** `\\اسم_الجهاز_الأول\ClinicShare`
3. **انسخ الملفات إلى هذا المجلد**
4. **شغل التطبيق من المجلد المشترك**

### الطريقة الثانية: نسخ ملف البيانات (أبسط)

#### بعد كل عملية في الجهاز الأول:
1. **انسخ ملف** `clinic_data.json`
2. **انقله إلى الجهاز الثاني** (USB أو شبكة)
3. **ضعه في** `C:\ClinicData`

#### في الجهاز الثاني:
1. **أعد تشغيل تطبيق الطبيب**
2. **اضغط "تحديث البيانات"**

---

## 📋 **الخطوة 4: اختبار النظام**

### 🧪 **الاختبار الأول: إضافة مريض**

#### في جهاز موظف الاستقبال:
1. **اضغط "إضافة مريض جديد"**
2. **املأ البيانات:**
   - الاسم الأول: أحمد
   - الاسم الأخير: محمد
   - تاريخ الميلاد: 1990-01-01
   - الجنس: male
   - الهاتف: 0123456789
3. **اضغط "حفظ"**
4. **يجب أن تظهر رسالة "تم إضافة المريض بنجاح"**

#### في جهاز الطبيب:
1. **انتظر 5-10 ثواني**
2. **يجب أن تظهر نافذة تنبيه منبثقة**
3. **اضغط "موافق" لإغلاق التنبيه**
4. **اذهب إلى "التنبيهات" → "التنبيهات الجديدة"**
5. **يجب أن ترى التنبيه في القائمة**

---

## 📋 **الخطوة 5: حل المشاكل الشائعة**

### ❌ **مشكلة: "ملف غير موجود"**
**الحل:**
```cmd
# تأكد من وجود الملفات
dir C:\ClinicData
```
يجب أن ترى:
- `network_shared_data.py`
- `registrar_network.py` أو `doctor_simple.py`

### ❌ **مشكلة: "لا تظهر التنبيهات"**
**الحل:**
1. **تأكد من وجود ملف** `clinic_data.json` في كلا الجهازين
2. **في تطبيق الطبيب، اضغط "تحديث البيانات"**
3. **انتظر 10 ثواني للتحديث التلقائي**

### ❌ **مشكلة: "خطأ في الشبكة"**
**الحل:**
1. **تأكد من اتصال الجهازين بنفس الشبكة**
2. **استخدم الطريقة الثانية (نسخ الملف يدوياً)**

---

## 📋 **الخطوة 6: التشغيل اليومي**

### 🌅 **بداية اليوم:**
1. **شغل تطبيق موظف الاستقبال** في الجهاز الأول
2. **شغل تطبيق الطبيب** في الجهاز الثاني
3. **تأكد من ظهور "متصل" في كلا التطبيقين**

### 📝 **أثناء العمل:**
1. **موظف الاستقبال يسجل المرضى**
2. **الطبيب يستقبل التنبيهات تلقائياً**
3. **كل 30 ثانية يتم التحديث التلقائي**

### 🌙 **نهاية اليوم:**
1. **احفظ نسخة احتياطية من** `clinic_data.json`
2. **أغلق التطبيقات**

---

## 📁 **ملخص الملفات المطلوبة:**

### الجهاز الأول (موظف الاستقبال):
```
C:\ClinicData\
├── network_shared_data.py
├── registrar_network.py
└── clinic_data.json (ينشأ تلقائياً)
```

### الجهاز الثاني (الطبيب):
```
C:\ClinicData\
├── network_shared_data.py
├── doctor_simple.py
└── clinic_data.json (ينسخ من الجهاز الأول)
```

---

## 🎉 **النتيجة النهائية:**

✅ **جهاز موظف الاستقبال:** يسجل المرضى والمواعيد
✅ **جهاز الطبيب:** يستقبل التنبيهات ويعرض البيانات
✅ **الربط:** عبر ملف مشترك على الشبكة المحلية
✅ **لا حاجة لخادم معقد أو إعدادات شبكة متقدمة**

**🚀 ابدأ من الخطوة 1 وتابع بالترتيب!**
