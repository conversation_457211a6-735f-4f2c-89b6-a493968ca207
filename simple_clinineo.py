#!/usr/bin/env python3
"""
Simple Clinineo - تطبيق مبسط للاختبار
نسخة مبسطة من التطبيق لضمان العمل على الموبايل
"""

from flask import Flask, request, redirect, url_for
import json
import os
import uuid
from datetime import datetime
import socket

app = Flask(__name__)

# بيانات مؤقتة في الذاكرة
patients_data = []
notifications_data = []

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.route('/')
def home():
    return f"""
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Clinineo - نسخة مبسطة</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
            }}
            .container {{
                max-width: 400px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                padding: 30px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }}
            h1 {{
                text-align: center;
                color: #333;
                margin-bottom: 30px;
            }}
            .btn {{
                display: block;
                width: 100%;
                padding: 15px;
                margin: 15px 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-decoration: none;
                border-radius: 10px;
                text-align: center;
                font-size: 16px;
                font-weight: bold;
                border: none;
                cursor: pointer;
            }}
            .btn:hover {{
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            }}
            .btn-success {{
                background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            }}
            .stats {{
                background: #f8f9fa;
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                text-align: center;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏥 Clinineo</h1>
            <div class="stats">
                <strong>📊 الإحصائيات:</strong><br>
                👥 المرضى: {len(patients_data)}<br>
                🔔 التنبيهات: {len(notifications_data)}
            </div>
            
            <a href="/registrar" class="btn">
                🏥 تطبيق الاستقبال
            </a>
            
            <a href="/doctor" class="btn btn-success">
                🩺 تطبيب الطبيب
            </a>
            
            <div style="text-align: center; margin-top: 30px; color: #666; font-size: 12px;">
                📱 IP: {get_local_ip()}:5000<br>
                🕒 {datetime.now().strftime('%Y-%m-%d %H:%M')}
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/registrar')
def registrar():
    return f"""
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>الاستقبال - Clinineo</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
            }}
            .container {{
                max-width: 400px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                padding: 30px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }}
            h1 {{
                text-align: center;
                color: #333;
                margin-bottom: 30px;
            }}
            .btn {{
                display: block;
                width: 100%;
                padding: 15px;
                margin: 15px 0;
                background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
                color: white;
                text-decoration: none;
                border-radius: 10px;
                text-align: center;
                font-size: 16px;
                font-weight: bold;
                border: none;
                cursor: pointer;
            }}
            .back-btn {{
                background: #6c757d;
                padding: 10px 20px;
                font-size: 14px;
                margin-bottom: 20px;
            }}
            .patients-list {{
                background: #f8f9fa;
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                max-height: 300px;
                overflow-y: auto;
            }}
            .patient-item {{
                background: white;
                padding: 10px;
                margin: 5px 0;
                border-radius: 8px;
                border-left: 4px solid #2196f3;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <a href="/" class="btn back-btn">← العودة</a>
            
            <h1>🏥 الاستقبال</h1>
            
            <a href="/add_patient" class="btn">
                ➕ إضافة مريض جديد
            </a>
            
            <div class="patients-list">
                <strong>👥 المرضى ({len(patients_data)}):</strong>
                {''.join([f'<div class="patient-item">👤 {p["name"]}<br>📞 {p["phone"]}</div>' for p in patients_data[-5:]]) if patients_data else '<p style="text-align: center; color: #666;">لا توجد مرضى</p>'}
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/doctor')
def doctor():
    return f"""
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>الطبيب - Clinineo</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
            }}
            .container {{
                max-width: 400px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                padding: 30px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }}
            h1 {{
                text-align: center;
                color: #333;
                margin-bottom: 30px;
            }}
            .btn {{
                display: block;
                width: 100%;
                padding: 15px;
                margin: 15px 0;
                background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
                color: white;
                text-decoration: none;
                border-radius: 10px;
                text-align: center;
                font-size: 16px;
                font-weight: bold;
                border: none;
                cursor: pointer;
            }}
            .back-btn {{
                background: #6c757d;
                padding: 10px 20px;
                font-size: 14px;
                margin-bottom: 20px;
            }}
            .notifications {{
                background: #fff3cd;
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                border-left: 4px solid #ffc107;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <a href="/" class="btn back-btn">← العودة</a>
            
            <h1>🩺 د. أحمد محمد</h1>
            
            <div class="notifications">
                <strong>🔔 التنبيهات ({len(notifications_data)}):</strong>
                {''.join([f'<div>• {n["message"]}</div>' for n in notifications_data[-3:]]) if notifications_data else '<p>لا توجد تنبيهات جديدة</p>'}
            </div>
            
            <a href="/patients" class="btn">
                👥 المرضى
            </a>
            
            <a href="/prescription" class="btn">
                📝 كتابة روشتة
            </a>
        </div>
    </body>
    </html>
    """

@app.route('/add_patient', methods=['GET', 'POST'])
def add_patient():
    if request.method == 'POST':
        patient = {
            'id': str(uuid.uuid4())[:8],
            'name': request.form.get('name', ''),
            'phone': request.form.get('phone', ''),
            'created_at': datetime.now().isoformat()
        }
        patients_data.append(patient)
        
        # إضافة تنبيه
        notification = {
            'id': str(uuid.uuid4())[:8],
            'message': f'مريض جديد: {patient["name"]}',
            'created_at': datetime.now().isoformat()
        }
        notifications_data.append(notification)
        
        return redirect('/registrar')
    
    return f"""
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إضافة مريض - Clinineo</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
            }}
            .container {{
                max-width: 400px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                padding: 30px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }}
            h1 {{
                text-align: center;
                color: #333;
                margin-bottom: 30px;
            }}
            .form-group {{
                margin-bottom: 20px;
            }}
            label {{
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
                color: #333;
            }}
            input {{
                width: 100%;
                padding: 12px;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-size: 16px;
                box-sizing: border-box;
            }}
            input:focus {{
                outline: none;
                border-color: #2196f3;
            }}
            .btn {{
                display: block;
                width: 100%;
                padding: 15px;
                margin: 15px 0;
                background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
                color: white;
                text-decoration: none;
                border-radius: 10px;
                text-align: center;
                font-size: 16px;
                font-weight: bold;
                border: none;
                cursor: pointer;
            }}
            .back-btn {{
                background: #6c757d;
                padding: 10px 20px;
                font-size: 14px;
                margin-bottom: 20px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <a href="/registrar" class="btn back-btn">← العودة</a>
            
            <h1>➕ إضافة مريض</h1>
            
            <form method="POST">
                <div class="form-group">
                    <label>الاسم الكامل *</label>
                    <input type="text" name="name" required>
                </div>
                
                <div class="form-group">
                    <label>رقم الهاتف *</label>
                    <input type="tel" name="phone" required>
                </div>
                
                <button type="submit" class="btn">
                    💾 حفظ المريض
                </button>
            </form>
        </div>
    </body>
    </html>
    """

if __name__ == '__main__':
    local_ip = get_local_ip()
    port = 5000
    
    print("🌐" + "="*50)
    print("🏥 Clinineo Simple - تطبيق مبسط للاختبار")
    print("="*52)
    print(f"📱 جرب هذا العنوان على الهاتف:")
    print(f"   http://{local_ip}:{port}/")
    print("="*52)
    print(f"🔧 الخادم يعمل على: {local_ip}:{port}")
    print("🛑 اضغط Ctrl+C للإيقاف")
    print("="*52)
    
    app.run(host='0.0.0.0', port=port, debug=False)
