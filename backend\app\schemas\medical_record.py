"""
Medical record schemas for API requests and responses
نماذج السجلات الطبية لطلبات واستجابات API
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime, date

class MedicationSchema(BaseModel):
    """Schema for medication"""
    name: str
    dosage: str
    frequency: str
    duration: str
    instructions: Optional[str] = None

class MedicalRecordBase(BaseModel):
    """Base medical record schema"""
    patient_id: str
    appointment_id: Optional[str] = None
    diagnosis: str
    symptoms: Optional[str] = None
    treatment: str
    medications: Optional[List[MedicationSchema]] = None
    lab_tests: Optional[List[str]] = None
    follow_up_date: Optional[date] = None
    notes: Optional[str] = None

class MedicalRecordCreate(MedicalRecordBase):
    """Schema for creating a new medical record"""
    pass

class MedicalRecordUpdate(BaseModel):
    """Schema for updating medical record"""
    diagnosis: Optional[str] = None
    symptoms: Optional[str] = None
    treatment: Optional[str] = None
    medications: Optional[List[MedicationSchema]] = None
    lab_tests: Optional[List[str]] = None
    follow_up_date: Optional[date] = None
    notes: Optional[str] = None

class MedicalRecordResponse(MedicalRecordBase):
    """Schema for medical record response"""
    id: str
    doctor_id: str
    created_at: datetime
    
    class Config:
        from_attributes = True
