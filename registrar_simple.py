#!/usr/bin/env python3
"""
Simple Registrar App - No Server Required
تطبيق موظف الاستقبال البسيط - بدون خادم
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, date
import os
import sys

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from shared_data import shared_data
except ImportError:
    messagebox.showerror("خطأ", "ملف shared_data.py غير موجود")
    sys.exit(1)

class SimpleRegistrarApp:
    """تطبيق موظف الاستقبال البسيط"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Clinineo - موظف الاستقبال (بدون خادم)")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        self.current_user = None
        self.auto_refresh = True
        
        # إعداد الواجهة
        self.setup_ui()
        self.start_auto_refresh()
        
        # محاولة تسجيل دخول تلقائي
        self.auto_login()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة المرضى
        patients_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المرضى", menu=patients_menu)
        patients_menu.add_command(label="إضافة مريض جديد", command=self.show_add_patient)
        patients_menu.add_command(label="قائمة المرضى", command=self.show_patients_list)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="تسجيل دخول", command=self.show_login)
        settings_menu.add_command(label="تحديث البيانات", command=self.refresh_data)
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط الحالة
        self.status_frame = ttk.Frame(main_frame)
        self.status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(self.status_frame, text="جاهز", foreground="green")
        self.status_label.pack(side=tk.LEFT)
        
        self.user_label = ttk.Label(self.status_frame, text="")
        self.user_label.pack(side=tk.RIGHT)
        
        # منطقة المحتوى
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # الصفحة الرئيسية
        self.show_home_page()
    
    def auto_login(self):
        """تسجيل دخول تلقائي للاختبار"""
        user = shared_data.authenticate_user("<EMAIL>", "registrar123")
        if user:
            self.current_user = user
            self.user_label.config(text=f"المستخدم: {user['first_name']} {user['last_name']}")
            self.status_label.config(text="تم تسجيل الدخول تلقائياً", foreground="blue")
    
    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        def refresh_loop():
            while self.auto_refresh:
                try:
                    # تحديث الإحصائيات كل 10 ثواني
                    self.root.after(0, self.update_stats_if_visible)
                    time.sleep(10)
                except:
                    break
        
        threading.Thread(target=refresh_loop, daemon=True).start()
    
    def update_stats_if_visible(self):
        """تحديث الإحصائيات إذا كانت مرئية"""
        # هذه الدالة ستحدث الإحصائيات إذا كانت الصفحة الرئيسية مرئية
        pass
    
    def show_login(self):
        """إظهار نافذة تسجيل الدخول"""
        dialog = tk.Toplevel(self.root)
        dialog.title("تسجيل الدخول")
        dialog.geometry("300x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="البريد الإلكتروني:").pack(pady=5)
        email_entry = ttk.Entry(dialog, width=25)
        email_entry.pack(pady=5)
        email_entry.insert(0, "<EMAIL>")
        
        ttk.Label(dialog, text="كلمة المرور:").pack(pady=5)
        password_entry = ttk.Entry(dialog, width=25, show="*")
        password_entry.pack(pady=5)
        password_entry.insert(0, "registrar123")
        
        def login():
            email = email_entry.get().strip()
            password = password_entry.get().strip()
            
            user = shared_data.authenticate_user(email, password)
            if user:
                self.current_user = user
                self.user_label.config(text=f"المستخدم: {user['first_name']} {user['last_name']}")
                dialog.destroy()
                messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح")
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "بيانات تسجيل الدخول غير صحيحة")
        
        ttk.Button(dialog, text="تسجيل دخول", command=login).pack(pady=10)
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.config(text="تم تحديث البيانات", foreground="green")
        # إعادة تحميل الصفحة الحالية
        if hasattr(self, 'current_page'):
            if self.current_page == 'home':
                self.show_home_page()
            elif self.current_page == 'patients':
                self.show_patients_list()
    
    def show_home_page(self):
        """إظهار الصفحة الرئيسية"""
        self.current_page = 'home'
        
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # العنوان
        title_label = ttk.Label(self.content_frame, text="نظام إدارة العيادة - موظف الاستقبال", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # الأزرار الرئيسية
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(pady=20)
        
        ttk.Button(buttons_frame, text="إضافة مريض جديد", 
                  command=self.show_add_patient, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="قائمة المرضى", 
                  command=self.show_patients_list, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="تحديث البيانات", 
                  command=self.refresh_data, width=20).pack(pady=5)
        
        # إحصائيات سريعة
        stats_frame = ttk.LabelFrame(self.content_frame, text="إحصائيات سريعة", padding="10")
        stats_frame.pack(fill=tk.X, pady=20, padx=50)
        
        try:
            stats = shared_data.get_dashboard_stats()
            
            stats_grid = ttk.Frame(stats_frame)
            stats_grid.pack(fill=tk.X)
            
            ttk.Label(stats_grid, text=f"إجمالي المرضى: {stats.get('total_patients', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=0, column=0, padx=20, pady=5, sticky='w')
            
            ttk.Label(stats_grid, text=f"مواعيد اليوم: {stats.get('today_appointments', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=0, column=1, padx=20, pady=5, sticky='w')
            
            ttk.Label(stats_grid, text=f"الأطباء النشطين: {stats.get('active_doctors', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=1, column=0, padx=20, pady=5, sticky='w')
            
        except Exception as e:
            ttk.Label(stats_frame, text=f"خطأ في تحميل الإحصائيات: {str(e)}", 
                     foreground="red").pack()
    
    def show_add_patient(self):
        """إظهار نافذة إضافة مريض جديد"""
        if not self.current_user:
            messagebox.showwarning("تحذير", "يرجى تسجيل الدخول أولاً")
            return
        
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة مريض جديد")
        dialog.geometry("400x500")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # إطار التمرير
        canvas = tk.Canvas(dialog)
        scrollbar = ttk.Scrollbar(dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # الحقول
        fields = {}
        
        ttk.Label(scrollable_frame, text="الاسم الأول:").pack(pady=2, anchor='w')
        fields['first_name'] = ttk.Entry(scrollable_frame, width=30)
        fields['first_name'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="الاسم الأخير:").pack(pady=2, anchor='w')
        fields['last_name'] = ttk.Entry(scrollable_frame, width=30)
        fields['last_name'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="تاريخ الميلاد (YYYY-MM-DD):").pack(pady=2, anchor='w')
        fields['date_of_birth'] = ttk.Entry(scrollable_frame, width=30)
        fields['date_of_birth'].pack(pady=2, fill='x')
        fields['date_of_birth'].insert(0, "1990-01-01")
        
        ttk.Label(scrollable_frame, text="الجنس:").pack(pady=2, anchor='w')
        fields['gender'] = ttk.Combobox(scrollable_frame, values=['male', 'female'], width=27)
        fields['gender'].pack(pady=2, fill='x')
        fields['gender'].set('male')
        
        ttk.Label(scrollable_frame, text="رقم الهاتف:").pack(pady=2, anchor='w')
        fields['phone'] = ttk.Entry(scrollable_frame, width=30)
        fields['phone'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="البريد الإلكتروني:").pack(pady=2, anchor='w')
        fields['email'] = ttk.Entry(scrollable_frame, width=30)
        fields['email'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="العنوان:").pack(pady=2, anchor='w')
        fields['address'] = tk.Text(scrollable_frame, width=30, height=3)
        fields['address'].pack(pady=2, fill='x')
        
        def save_patient():
            # جمع البيانات
            patient_data = {}
            for field_name, widget in fields.items():
                if isinstance(widget, tk.Text):
                    patient_data[field_name] = widget.get("1.0", tk.END).strip()
                else:
                    patient_data[field_name] = widget.get().strip()
            
            # التحقق من البيانات المطلوبة
            required_fields = ['first_name', 'last_name', 'date_of_birth', 'gender', 'phone']
            for field in required_fields:
                if not patient_data.get(field):
                    messagebox.showerror("خطأ", f"يرجى ملء حقل {field}")
                    return
            
            # حفظ المريض
            try:
                success = shared_data.add_patient(patient_data, self.current_user['id'])
                if success:
                    messagebox.showinfo("نجح", "تم إضافة المريض بنجاح\nسيتم إرسال تنبيه للأطباء")
                    dialog.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المريض")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حفظ البيانات: {str(e)}")
        
        ttk.Button(scrollable_frame, text="حفظ", command=save_patient).pack(pady=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def show_patients_list(self):
        """إظهار قائمة المرضى"""
        self.current_page = 'patients'
        
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        ttk.Label(self.content_frame, text="قائمة المرضى", 
                 font=('Arial', 14, 'bold')).pack(pady=10)
        
        # جدول المرضى
        columns = ('ID', 'الاسم', 'الهاتف', 'الجنس', 'تاريخ الإضافة')
        tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        # تحميل البيانات
        try:
            patients = shared_data.get_patients()
            for patient in patients:
                tree.insert('', tk.END, values=(
                    patient.get('id', ''),
                    f"{patient.get('first_name', '')} {patient.get('last_name', '')}",
                    patient.get('phone', ''),
                    'ذكر' if patient.get('gender') == 'male' else 'أنثى',
                    patient.get('created_at', '')[:10]
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
        
        tree.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(pady=10)
        
        ttk.Button(buttons_frame, text="تحديث", command=self.show_patients_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="العودة للرئيسية", command=self.show_home_page).pack(side=tk.LEFT, padx=5)
    
    def on_closing(self):
        """عند إغلاق التطبيق"""
        self.auto_refresh = False
        self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    print("🚀 تشغيل تطبيق موظف الاستقبال البسيط...")
    app = SimpleRegistrarApp()
    app.run()
