#!/usr/bin/env python3
"""
Clinineo Launcher - مشغل تطبيقات Clinineo
يساعد في تشغيل التطبيقات بسهولة
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_banner():
    """طباعة شعار التطبيق"""
    print("=" * 60)
    print("🏥 Clinineo - نظام إدارة العيادات المتطور")
    print("=" * 60)
    print("📱 تطبيقين أندرويد مميزين بواجهات احترافية")
    print("🩺 للاستقبال والطبيب مع تنبيهات فورية")
    print("=" * 60)
    print()

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    try:
        import kivy
        print("✅ Kivy متوفر")
    except ImportError:
        print("❌ Kivy غير متوفر")
        print("💡 قم بتثبيته: pip install kivy")
        return False
    
    try:
        import requests
        print("✅ Requests متوفر")
    except ImportError:
        print("❌ Requests غير متوفر")
        print("💡 قم بتثبيته: pip install requests")
        return False
    
    # فحص الملفات
    if os.path.exists('clinineo_registrar.py'):
        print("✅ تطبيق الاستقبال متوفر")
    else:
        print("❌ تطبيق الاستقبال غير موجود")
        return False
    
    if os.path.exists('clinineo_doctor.py'):
        print("✅ تطبيق الطبيب متوفر")
    else:
        print("❌ تطبيق الطبيب غير موجود")
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    print()
    return True

def show_menu():
    """عرض القائمة الرئيسية"""
    print("📋 اختر التطبيق المطلوب:")
    print()
    print("1️⃣  تشغيل تطبيق الاستقبال (Registrar)")
    print("2️⃣  تشغيل تطبيق الطبيب (Doctor)")
    print("3️⃣  تشغيل كلا التطبيقين معاً")
    print("4️⃣  عرض معلومات النظام")
    print("5️⃣  تثبيت المتطلبات")
    print("0️⃣  خروج")
    print()

def run_registrar():
    """تشغيل تطبيق الاستقبال"""
    print("🏥 تشغيل تطبيق الاستقبال...")
    print("📡 سيعمل كخادم على المنفذ 8080")
    print("🔄 انتظر حتى ترى 'الخادم يعمل' في التطبيق")
    print()
    
    try:
        subprocess.run([sys.executable, 'clinineo_registrar.py'])
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف تطبيق الاستقبال")
    except Exception as e:
        print(f"❌ خطأ في تشغيل تطبيق الاستقبال: {e}")

def run_doctor():
    """تشغيل تطبيق الطبيب"""
    print("🩺 تشغيل تطبيق الطبيب...")
    print("🔗 سيتصل بتطبيق الاستقبال تلقائياً")
    print("🔔 ستظهر التنبيهات عند إضافة مرضى جدد")
    print()
    
    try:
        subprocess.run([sys.executable, 'clinineo_doctor.py'])
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف تطبيق الطبيب")
    except Exception as e:
        print(f"❌ خطأ في تشغيل تطبيق الطبيب: {e}")

def run_both():
    """تشغيل كلا التطبيقين"""
    print("🚀 تشغيل كلا التطبيقين...")
    print("⚠️  ملاحظة: ستحتاج لإغلاق هذه النافذة وفتح نافذة أخرى")
    print("📋 الترتيب الصحيح:")
    print("   1. شغل تطبيق الاستقبال أولاً")
    print("   2. انتظر حتى يعمل الخادم")
    print("   3. شغل تطبيق الطبيب في نافذة أخرى")
    print()
    
    choice = input("هل تريد المتابعة؟ (y/n): ").lower()
    if choice == 'y' or choice == 'yes':
        print("🏥 تشغيل تطبيق الاستقبال...")
        run_registrar()

def show_system_info():
    """عرض معلومات النظام"""
    print("ℹ️  معلومات النظام:")
    print("=" * 40)
    
    # معلومات Python
    print(f"🐍 Python: {sys.version}")
    
    # معلومات المكتبات
    try:
        import kivy
        print(f"🎨 Kivy: {kivy.__version__}")
    except:
        print("🎨 Kivy: غير مثبت")
    
    try:
        import requests
        print(f"🌐 Requests: {requests.__version__}")
    except:
        print("🌐 Requests: غير مثبت")
    
    # معلومات الملفات
    print("\n📁 الملفات:")
    files = [
        'clinineo_registrar.py',
        'clinineo_doctor.py',
        'clinineo_data.json',
        'CLININEO_GUIDE.md'
    ]
    
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size} bytes)")
        else:
            print(f"❌ {file} (غير موجود)")
    
    # معلومات الشبكة
    print("\n🌐 معلومات الشبكة:")
    try:
        import socket
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"🖥️  اسم الجهاز: {hostname}")
        print(f"📡 عنوان IP: {local_ip}")
        print(f"🔗 رابط الخادم: http://{local_ip}:8080")
    except:
        print("❌ فشل في الحصول على معلومات الشبكة")
    
    print("=" * 40)
    print()

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    print()
    
    packages = ['kivy', 'requests']
    
    for package in packages:
        print(f"📥 تثبيت {package}...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✅ تم تثبيت {package} بنجاح")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت {package}: {e}")
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
    
    print("\n🔄 فحص التثبيت...")
    check_requirements()

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    if not check_requirements():
        print("⚠️  يرجى تثبيت المتطلبات أولاً")
        print()
    
    while True:
        show_menu()
        
        try:
            choice = input("اختر رقم الخيار: ").strip()
            print()
            
            if choice == '1':
                run_registrar()
            elif choice == '2':
                run_doctor()
            elif choice == '3':
                run_both()
            elif choice == '4':
                show_system_info()
            elif choice == '5':
                install_requirements()
            elif choice == '0':
                print("👋 شكراً لاستخدام Clinineo!")
                break
            else:
                print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")
            
            print()
            input("اضغط Enter للمتابعة...")
            print()
            
        except KeyboardInterrupt:
            print("\n\n👋 تم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
