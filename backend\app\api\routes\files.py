"""
File management routes
مسارات إدارة الملفات
"""

import os
import shutil
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, Form
from sqlalchemy.orm import Session

from ...core.database import get_db, MedicalFile, Patient, User
from ...core.security import get_current_active_user, require_any_role
from ...core.config import settings

router = APIRouter()

@router.post("/upload")
async def upload_file(
    patient_id: str = Form(...),
    medical_record_id: Optional[str] = Form(None),
    file_type: str = Form("other"),
    description: Optional[str] = Form(None),
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """Upload a medical file"""
    # Check if patient exists
    patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )
    
    # Validate file type
    if file.content_type not in settings.ALLOWED_FILE_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type {file.content_type} not allowed"
        )
    
    # Validate file size
    file_content = await file.read()
    if len(file_content) > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size too large"
        )
    
    # Create patient directory if it doesn't exist
    patient_dir = os.path.join(settings.UPLOAD_DIR, patient_id)
    os.makedirs(patient_dir, exist_ok=True)
    
    # Generate unique filename
    import uuid
    file_extension = os.path.splitext(file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(patient_dir, unique_filename)
    
    # Save file
    with open(file_path, "wb") as buffer:
        buffer.write(file_content)
    
    # Create database record
    db_file = MedicalFile(
        patient_id=patient_id,
        medical_record_id=medical_record_id,
        filename=file.filename,
        file_path=file_path,
        file_type=file.content_type,
        file_size=len(file_content),
        type=file_type,
        description=description,
        uploaded_by=current_user.id
    )
    db.add(db_file)
    db.commit()
    db.refresh(db_file)
    
    return {
        "id": db_file.id,
        "filename": db_file.filename,
        "file_type": db_file.file_type,
        "file_size": db_file.file_size,
        "message": "File uploaded successfully"
    }

@router.get("/")
async def get_files(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    patient_id: Optional[str] = Query(None),
    medical_record_id: Optional[str] = Query(None),
    file_type: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """Get medical files with filtering"""
    query = db.query(MedicalFile)
    
    # Apply filters
    if patient_id:
        query = query.filter(MedicalFile.patient_id == patient_id)
    
    if medical_record_id:
        query = query.filter(MedicalFile.medical_record_id == medical_record_id)
    
    if file_type:
        query = query.filter(MedicalFile.type == file_type)
    
    # Apply pagination and ordering
    files = query.order_by(MedicalFile.created_at.desc()).offset(skip).limit(limit).all()
    
    return [
        {
            "id": file.id,
            "patient_id": file.patient_id,
            "medical_record_id": file.medical_record_id,
            "filename": file.filename,
            "file_type": file.file_type,
            "file_size": file.file_size,
            "type": file.type,
            "description": file.description,
            "created_at": file.created_at
        }
        for file in files
    ]

@router.get("/{file_id}")
async def get_file(
    file_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """Get file information by ID"""
    file = db.query(MedicalFile).filter(MedicalFile.id == file_id).first()
    if not file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    return {
        "id": file.id,
        "patient_id": file.patient_id,
        "medical_record_id": file.medical_record_id,
        "filename": file.filename,
        "file_type": file.file_type,
        "file_size": file.file_size,
        "type": file.type,
        "description": file.description,
        "created_at": file.created_at
    }

@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role)
):
    """Delete a medical file"""
    file = db.query(MedicalFile).filter(MedicalFile.id == file_id).first()
    if not file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    # Check permissions (only uploader or admin can delete)
    if current_user.role != "admin" and file.uploaded_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    
    # Delete physical file
    try:
        if os.path.exists(file.file_path):
            os.remove(file.file_path)
    except Exception as e:
        print(f"Error deleting file: {e}")
    
    # Delete database record
    db.delete(file)
    db.commit()
    
    return {"message": "File deleted successfully"}
