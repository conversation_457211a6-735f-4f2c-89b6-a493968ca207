class Patient {
  final String id;
  final String firstName;
  final String lastName;
  final DateTime dateOfBirth;
  final String gender;
  final String phone;
  final String? email;
  final String? address;
  final String? emergencyContact;
  final String? bloodType;
  final String? allergies;
  final String? medicalHistory;
  final String? insuranceNumber;
  final DateTime createdAt;

  Patient({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.dateOfBirth,
    required this.gender,
    required this.phone,
    this.email,
    this.address,
    this.emergencyContact,
    this.bloodType,
    this.allergies,
    this.medicalHistory,
    this.insuranceNumber,
    required this.createdAt,
  });

  factory Patient.fromJson(Map<String, dynamic> json) {
    return Patient(
      id: json['id'] ?? '',
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      dateOfBirth: DateTime.parse(json['dateOfBirth'] ?? DateTime.now().toIso8601String()),
      gender: json['gender'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'],
      address: json['address'],
      emergencyContact: json['emergencyContact'],
      bloodType: json['bloodType'],
      allergies: json['allergies'],
      medicalHistory: json['medicalHistory'],
      insuranceNumber: json['insuranceNumber'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'dateOfBirth': dateOfBirth.toIso8601String().split('T')[0],
      'gender': gender,
      'phone': phone,
      'email': email,
      'address': address,
      'emergencyContact': emergencyContact,
      'bloodType': bloodType,
      'allergies': allergies,
      'medicalHistory': medicalHistory,
      'insuranceNumber': insuranceNumber,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  String get fullName => '$firstName $lastName';
  
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  String get genderDisplay {
    switch (gender) {
      case 'male':
        return 'ذكر';
      case 'female':
        return 'أنثى';
      default:
        return gender;
    }
  }

  Patient copyWith({
    String? id,
    String? firstName,
    String? lastName,
    DateTime? dateOfBirth,
    String? gender,
    String? phone,
    String? email,
    String? address,
    String? emergencyContact,
    String? bloodType,
    String? allergies,
    String? medicalHistory,
    String? insuranceNumber,
    DateTime? createdAt,
  }) {
    return Patient(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      bloodType: bloodType ?? this.bloodType,
      allergies: allergies ?? this.allergies,
      medicalHistory: medicalHistory ?? this.medicalHistory,
      insuranceNumber: insuranceNumber ?? this.insuranceNumber,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Patient(id: $id, fullName: $fullName, phone: $phone)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Patient && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class CreatePatientRequest {
  final String firstName;
  final String lastName;
  final DateTime dateOfBirth;
  final String gender;
  final String phone;
  final String? email;
  final String? address;
  final String? emergencyContact;
  final String? bloodType;
  final String? allergies;
  final String? medicalHistory;
  final String? insuranceNumber;

  CreatePatientRequest({
    required this.firstName,
    required this.lastName,
    required this.dateOfBirth,
    required this.gender,
    required this.phone,
    this.email,
    this.address,
    this.emergencyContact,
    this.bloodType,
    this.allergies,
    this.medicalHistory,
    this.insuranceNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'dateOfBirth': dateOfBirth.toIso8601String().split('T')[0],
      'gender': gender,
      'phone': phone,
      'email': email,
      'address': address,
      'emergencyContact': emergencyContact,
      'bloodType': bloodType,
      'allergies': allergies,
      'medicalHistory': medicalHistory,
      'insuranceNumber': insuranceNumber,
    };
  }
}

class UpdatePatientRequest {
  final String? firstName;
  final String? lastName;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? phone;
  final String? email;
  final String? address;
  final String? emergencyContact;
  final String? bloodType;
  final String? allergies;
  final String? medicalHistory;
  final String? insuranceNumber;

  UpdatePatientRequest({
    this.firstName,
    this.lastName,
    this.dateOfBirth,
    this.gender,
    this.phone,
    this.email,
    this.address,
    this.emergencyContact,
    this.bloodType,
    this.allergies,
    this.medicalHistory,
    this.insuranceNumber,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (firstName != null) data['firstName'] = firstName;
    if (lastName != null) data['lastName'] = lastName;
    if (dateOfBirth != null) data['dateOfBirth'] = dateOfBirth!.toIso8601String().split('T')[0];
    if (gender != null) data['gender'] = gender;
    if (phone != null) data['phone'] = phone;
    if (email != null) data['email'] = email;
    if (address != null) data['address'] = address;
    if (emergencyContact != null) data['emergencyContact'] = emergencyContact;
    if (bloodType != null) data['bloodType'] = bloodType;
    if (allergies != null) data['allergies'] = allergies;
    if (medicalHistory != null) data['medicalHistory'] = medicalHistory;
    if (insuranceNumber != null) data['insuranceNumber'] = insuranceNumber;
    
    return data;
  }
}
