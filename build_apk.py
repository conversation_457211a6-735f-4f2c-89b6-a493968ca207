#!/usr/bin/env python3
"""
APK Builder for Clinineo Apps
أداة بناء ملفات APK لتطبيقات Clinineo
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def print_banner():
    """طباعة شعار البناء"""
    print("=" * 60)
    print("📱 Clinineo APK Builder")
    print("=" * 60)
    print("🏗️  بناء ملفات APK للتطبيقين")
    print("📦 تطبيق الاستقبال + تطبيق الطبيب")
    print("=" * 60)
    print()

def check_requirements():
    """فحص متطلبات البناء"""
    print("🔍 فحص متطلبات البناء...")
    
    # فحص Python
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    print("✅ Python متوفر")
    
    # فحص buildozer
    try:
        result = subprocess.run(['buildozer', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Buildozer متوفر")
        else:
            print("❌ Buildozer غير متوفر")
            print("💡 قم بتثبيته: pip install buildozer")
            return False
    except FileNotFoundError:
        print("❌ Buildozer غير مثبت")
        print("💡 قم بتثبيته: pip install buildozer")
        return False
    
    # فحص الملفات المطلوبة
    required_files = [
        'clinineo_registrar.py',
        'clinineo_doctor.py',
        'registrar_main.py',
        'doctor_main.py',
        'buildozer_registrar.spec',
        'buildozer_doctor.spec'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} غير موجود")
            return False
    
    print("✅ جميع المتطلبات متوفرة!")
    print()
    return True

def setup_build_environment():
    """إعداد بيئة البناء"""
    print("🏗️  إعداد بيئة البناء...")
    
    # إنشاء مجلدات البناء
    build_dirs = ['build_registrar', 'build_doctor', 'apk_output']
    
    for dir_name in build_dirs:
        if os.path.exists(dir_name):
            print(f"🗑️  حذف مجلد قديم: {dir_name}")
            shutil.rmtree(dir_name)
        
        os.makedirs(dir_name)
        print(f"📁 إنشاء مجلد: {dir_name}")
    
    print("✅ تم إعداد بيئة البناء")
    print()

def build_registrar_apk():
    """بناء APK تطبيق الاستقبال"""
    print("🏥 بناء APK تطبيق الاستقبال...")
    print("⏳ هذا قد يستغرق عدة دقائق...")
    
    try:
        # نسخ الملفات المطلوبة
        os.chdir('build_registrar')
        
        # نسخ ملفات التطبيق
        shutil.copy('../clinineo_registrar.py', 'clinineo_registrar.py')
        shutil.copy('../registrar_main.py', 'main.py')
        shutil.copy('../buildozer_registrar.spec', 'buildozer.spec')
        
        print("📋 تم نسخ ملفات تطبيق الاستقبال")
        
        # بناء APK
        print("🔨 بدء عملية البناء...")
        result = subprocess.run(['buildozer', 'android', 'debug'], 
                              capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء APK تطبيق الاستقبال بنجاح!")
            
            # نقل APK إلى مجلد الإخراج
            apk_files = []
            bin_dir = 'bin'
            if os.path.exists(bin_dir):
                for file in os.listdir(bin_dir):
                    if file.endswith('.apk'):
                        apk_files.append(file)
                        src = os.path.join(bin_dir, file)
                        dst = os.path.join('..', 'apk_output', f'clinineo_registrar_{file}')
                        shutil.copy(src, dst)
                        print(f"📱 APK محفوظ: {dst}")
            
            return True
        else:
            print("❌ فشل في بناء APK تطبيق الاستقبال")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء تطبيق الاستقبال: {e}")
        return False
    finally:
        os.chdir('..')

def build_doctor_apk():
    """بناء APK تطبيق الطبيب"""
    print("🩺 بناء APK تطبيق الطبيب...")
    print("⏳ هذا قد يستغرق عدة دقائق...")
    
    try:
        # نسخ الملفات المطلوبة
        os.chdir('build_doctor')
        
        # نسخ ملفات التطبيق
        shutil.copy('../clinineo_doctor.py', 'clinineo_doctor.py')
        shutil.copy('../doctor_main.py', 'main.py')
        shutil.copy('../buildozer_doctor.spec', 'buildozer.spec')
        
        print("📋 تم نسخ ملفات تطبيق الطبيب")
        
        # بناء APK
        print("🔨 بدء عملية البناء...")
        result = subprocess.run(['buildozer', 'android', 'debug'], 
                              capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء APK تطبيق الطبيب بنجاح!")
            
            # نقل APK إلى مجلد الإخراج
            apk_files = []
            bin_dir = 'bin'
            if os.path.exists(bin_dir):
                for file in os.listdir(bin_dir):
                    if file.endswith('.apk'):
                        apk_files.append(file)
                        src = os.path.join(bin_dir, file)
                        dst = os.path.join('..', 'apk_output', f'clinineo_doctor_{file}')
                        shutil.copy(src, dst)
                        print(f"📱 APK محفوظ: {dst}")
            
            return True
        else:
            print("❌ فشل في بناء APK تطبيق الطبيب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء تطبيق الطبيب: {e}")
        return False
    finally:
        os.chdir('..')

def show_results():
    """عرض نتائج البناء"""
    print("📊 نتائج البناء:")
    print("=" * 40)
    
    apk_dir = 'apk_output'
    if os.path.exists(apk_dir):
        apk_files = [f for f in os.listdir(apk_dir) if f.endswith('.apk')]
        
        if apk_files:
            print("✅ ملفات APK المُنشأة:")
            for apk in apk_files:
                file_path = os.path.join(apk_dir, apk)
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                print(f"📱 {apk} ({file_size:.1f} MB)")
            
            print(f"\n📁 الملفات محفوظة في: {os.path.abspath(apk_dir)}")
            
            print("\n📋 خطوات التثبيت:")
            print("1. انسخ ملفات APK إلى هاتفك الأندرويد")
            print("2. فعّل 'مصادر غير معروفة' في إعدادات الأمان")
            print("3. ثبت clinineo_registrar_*.apk على هاتف الاستقبال")
            print("4. ثبت clinineo_doctor_*.apk على هاتف الطبيب")
            print("5. شغل تطبيق الاستقبال أولاً، ثم تطبيق الطبيب")
            
        else:
            print("❌ لم يتم إنشاء أي ملفات APK")
    else:
        print("❌ مجلد الإخراج غير موجود")
    
    print("=" * 40)

def install_buildozer():
    """تثبيت buildozer"""
    print("📦 تثبيت Buildozer...")
    
    try:
        # تثبيت buildozer
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'buildozer'], check=True)
        print("✅ تم تثبيت Buildozer")
        
        # تثبيت متطلبات إضافية
        additional_packages = ['cython', 'virtualenv']
        for package in additional_packages:
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], check=True)
            print(f"✅ تم تثبيت {package}")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت Buildozer: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص المتطلبات
    if not check_requirements():
        print("⚠️  هل تريد تثبيت Buildozer؟ (y/n): ", end="")
        choice = input().lower()
        if choice in ['y', 'yes']:
            if install_buildozer():
                print("🔄 إعادة فحص المتطلبات...")
                if not check_requirements():
                    print("❌ فشل في إعداد البيئة")
                    return
            else:
                print("❌ فشل في تثبيت المتطلبات")
                return
        else:
            print("❌ لا يمكن المتابعة بدون المتطلبات")
            return
    
    print("🚀 بدء عملية بناء APK...")
    print("⚠️  تحذير: عملية البناء قد تستغرق 15-30 دقيقة للمرة الأولى")
    print("📱 سيتم بناء تطبيقين منفصلين")
    print()
    
    # تأكيد المتابعة
    print("هل تريد المتابعة؟ (y/n): ", end="")
    choice = input().lower()
    if choice not in ['y', 'yes']:
        print("❌ تم إلغاء العملية")
        return
    
    start_time = datetime.now()
    
    # إعداد بيئة البناء
    setup_build_environment()
    
    # بناء التطبيقات
    registrar_success = build_registrar_apk()
    doctor_success = build_doctor_apk()
    
    # عرض النتائج
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n⏱️  وقت البناء: {duration}")
    print(f"🏥 تطبيق الاستقبال: {'✅ نجح' if registrar_success else '❌ فشل'}")
    print(f"🩺 تطبيق الطبيب: {'✅ نجح' if doctor_success else '❌ فشل'}")
    
    if registrar_success or doctor_success:
        show_results()
    
    print("\n🎉 انتهت عملية البناء!")

if __name__ == "__main__":
    main()
