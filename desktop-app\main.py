#!/usr/bin/env python3
"""
Clinineo Desktop Application
تطبيق Clinineo لسطح المكتب
"""

import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
from datetime import datetime, date
import threading
import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings
from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from services.api_client import APIClient
from services.auth_service import AuthService

class CliniceoApp:
    """Main application class"""
    
    def __init__(self):
        self.settings = Settings()
        self.api_client = APIClient(self.settings.API_BASE_URL)
        self.auth_service = AuthService(self.api_client)
        self.current_user = None
        self.main_window = None
        
        # Create root window (hidden initially)
        self.root = tk.Tk()
        self.root.withdraw()  # Hide main window initially
        
        # Configure root window
        self.root.title("Clinineo - نظام إدارة العيادة")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Set icon (if available)
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
        
        # Configure style
        self.setup_styles()
        
        # Start with login window
        self.show_login()
    
    def setup_styles(self):
        """Setup application styles"""
        style = ttk.Style()
        
        # Configure theme
        style.theme_use('clam')
        
        # Configure colors
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
    
    def show_login(self):
        """Show login window"""
        login_window = LoginWindow(self.root, self.auth_service, self.on_login_success)
        login_window.show()
    
    def on_login_success(self, user_data, token):
        """Handle successful login"""
        self.current_user = user_data
        self.api_client.set_token(token)
        
        # Show main window
        self.root.deiconify()  # Show main window
        self.main_window = MainWindow(self.root, self.api_client, self.current_user, self.on_logout)
        
        # Show welcome message
        messagebox.showinfo(
            "تسجيل دخول ناجح",
            f"مرحباً {user_data['first_name']} {user_data['last_name']}\nدورك: {self.get_role_display(user_data['role'])}"
        )
    
    def on_logout(self):
        """Handle logout"""
        self.current_user = None
        self.api_client.clear_token()
        
        # Hide main window
        self.root.withdraw()
        
        # Show login window again
        self.show_login()
    
    def get_role_display(self, role):
        """Get role display name in Arabic"""
        role_map = {
            'admin': 'مدير النظام',
            'doctor': 'طبيب',
            'registrar': 'موظف استقبال'
        }
        return role_map.get(role, role)
    
    def run(self):
        """Start the application"""
        try:
            print("🚀 Starting Clinineo Desktop Application...")
            print("📍 Connecting to API:", self.settings.API_BASE_URL)
            
            # Test API connection
            if self.test_api_connection():
                print("✅ API connection successful")
            else:
                print("❌ API connection failed")
                messagebox.showerror(
                    "خطأ في الاتصال",
                    f"لا يمكن الاتصال بالخادم\n{self.settings.API_BASE_URL}\n\nتأكد من تشغيل الخادم أولاً"
                )
                return
            
            # Start main loop
            self.root.mainloop()
            
        except Exception as e:
            print(f"❌ Application error: {e}")
            messagebox.showerror("خطأ في التطبيق", f"حدث خطأ غير متوقع:\n{str(e)}")
    
    def test_api_connection(self):
        """Test API connection"""
        try:
            response = requests.get(f"{self.settings.API_BASE_URL}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

def main():
    """Main function"""
    try:
        app = CliniceoApp()
        app.run()
    except KeyboardInterrupt:
        print("\n👋 Application closed by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        messagebox.showerror("خطأ فادح", f"حدث خطأ فادح:\n{str(e)}")

if __name__ == "__main__":
    main()
