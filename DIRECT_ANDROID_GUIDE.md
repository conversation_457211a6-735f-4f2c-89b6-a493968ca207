# 📱 دليل التطبيقات المباشرة - بدون كمبيوتر

## 🎯 **النظام الجديد:**
- **📱 تطبيق الاستقبال** يعمل كخادم مدمج
- **📱 تطبيق الطبيب** يتصل بتطبيق الاستقبال مباشرة
- **🌐 الربط عبر الراوتر** فقط - لا حاجة لكمبيوتر
- **🚫 بدون إنترنت** - شبكة محلية فقط

---

## 📋 **الخطوة 1: إعداد هاتف الاستقبال (الخادم)**

### 1.1 تثبيت المتطلبات:
```bash
pip install kivy requests
```

### 1.2 تشغيل تطبيق الاستقبال:
```bash
python android_registrar_server.py
```

### 1.3 معرفة عنوان IP:
سيظهر في التطبيق:
```
عنوان الخادم: http://*************:8080
الخادم يعمل على: *************:8080
```

**احفظ هذا العنوان:** `http://*************:8080`

---

## 📋 **الخطوة 2: إعداد هاتف الطبيب (العميل)**

### 2.1 تشغيل تطبيق الطبيب:
```bash
python android_doctor_client.py
```

### 2.2 إعداد الاتصال:
1. **اضغط "إعدادات الاتصال"**
2. **أدخل عنوان تطبيق الاستقبال:** `http://*************:8080`
3. **اضغط "اختبار الاتصال"** للتأكد
4. **اضغط "حفظ"**

---

## 📋 **الخطوة 3: اختبار النظام**

### 🧪 **الاختبار الكامل:**

#### في هاتف الاستقبال:
1. **تأكد من ظهور "الخادم يعمل"** باللون الأخضر
2. **اضغط "إضافة مريض جديد"**
3. **املأ البيانات:**
   - الاسم الأول: أحمد
   - الاسم الأخير: محمد
   - تاريخ الميلاد: 1990-01-01
   - الجنس: male
   - الهاتف: 0123456789
4. **اضغط "حفظ"**

#### في هاتف الطبيب:
1. **تأكد من ظهور "متصل بتطبيق الاستقبال"** باللون الأخضر
2. **انتظر 10 ثواني**
3. **ستظهر نافذة تنبيه منبثقة** 🔔
4. **اضغط "تمييز كمقروء"**
5. **اذهب إلى "التنبيهات الجديدة"** لرؤية جميع التنبيهات

---

## 🔧 **إعدادات الشبكة:**

### 🌐 **متطلبات الربط:**

#### تأكد من:
1. **كلا الهاتفين متصلين بنفس الراوتر** (WiFi)
2. **نفس الشبكة المحلية** (192.168.1.x أو 192.168.0.x)
3. **لا حاجة لإنترنت** - الراوتر فقط

#### معرفة عنوان IP للهاتف:
- **في الأندرويد:** الإعدادات → WiFi → تفاصيل الشبكة
- **أو استخدم تطبيق:** "WiFi Analyzer" أو "Network Info"

---

## 🔄 **سير العمل:**

```
📱 هاتف الاستقبال (الخادم):
   ├── تشغيل android_registrar_server.py
   ├── يعمل كخادم على المنفذ 8080
   ├── يحفظ البيانات في ملف JSON
   └── يرسل تنبيهات للأطباء

📱 هاتف الطبيب (العميل):
   ├── تشغيل android_doctor_client.py
   ├── يتصل بتطبيق الاستقبال
   ├── يفحص التنبيهات كل 10 ثواني
   └── يستقبل تنبيهات فورية
```

---

## ✨ **الميزات المتاحة:**

### 📱 **تطبيق الاستقبال (الخادم):**
- ✅ خادم مدمج يعمل تلقائياً
- ✅ إضافة مرضى جدد
- ✅ عرض قائمة المرضى
- ✅ عرض الإحصائيات
- ✅ حفظ البيانات محلياً
- ✅ إرسال تنبيهات تلقائية

### 📱 **تطبيق الطبيب (العميل):**
- ✅ اتصال مباشر بتطبيق الاستقبال
- ✅ تنبيهات فورية ومنبثقة
- ✅ فحص دوري كل 10 ثواني
- ✅ عرض جميع التنبيهات
- ✅ عرض قائمة المرضى
- ✅ لوحة معلومات شاملة
- ✅ إعدادات اتصال مرنة

---

## 🔧 **حل المشاكل:**

### ❌ **"غير متصل بتطبيق الاستقبال"**
**الحل:**
1. تأكد من تشغيل تطبيق الاستقبال أولاً
2. تأكد من اتصال الهاتفين بنفس الراوتر
3. تحقق من عنوان IP في إعدادات الاتصال
4. اضغط "اختبار الاتصال"

### ❌ **"لا تظهر التنبيهات"**
**الحل:**
1. تأكد من ظهور "متصل" في تطبيق الطبيب
2. أضف مريض جديد في تطبيق الاستقبال
3. انتظر 10-15 ثانية للفحص التلقائي
4. اضغط "تحديث البيانات"

### ❌ **"الخادم متوقف"**
**الحل:**
1. أعد تشغيل تطبيق الاستقبال
2. تأكد من عدم استخدام المنفذ 8080 من تطبيق آخر
3. تحقق من إعدادات Firewall في الهاتف

### ❌ **"خطأ في عنوان IP"**
**الحل:**
1. تأكد من العنوان الصحيح: `http://*************:8080`
2. لا تنس `http://` في البداية
3. تأكد من رقم المنفذ `:8080`
4. استخدم عنوان IP الحقيقي وليس 127.0.0.1

---

## 📁 **الملفات المطلوبة:**

### للهاتف الأول (الاستقبال):
- ✅ `android_registrar_server.py`

### للهاتف الثاني (الطبيب):
- ✅ `android_doctor_client.py`

### الملفات التي تُنشأ تلقائياً:
- 📄 `clinic_data_android.json` (في هاتف الاستقبال)

---

## 🎯 **التشغيل السريع:**

### **الطريقة المضمونة:**

#### 1. الهاتف الأول (الاستقبال):
```bash
pip install kivy requests
python android_registrar_server.py
```
**انتظر حتى ترى:** "الخادم يعمل على: 192.168.1.xxx:8080"

#### 2. الهاتف الثاني (الطبيب):
```bash
pip install kivy requests
python android_doctor_client.py
```

#### 3. في تطبيق الطبيب:
- اضغط "إعدادات الاتصال"
- أدخل العنوان من الخطوة 1
- اضغط "اختبار الاتصال"
- اضغط "حفظ"

#### 4. اختبار:
- أضف مريض في تطبيق الاستقبال
- شاهد التنبيه في تطبيق الطبيب!

---

## 🌟 **المزايا الجديدة:**

✅ **بدون كمبيوتر** - هاتفين فقط
✅ **بدون إنترنت** - راوتر محلي فقط
✅ **تنبيهات حقيقية** - فورية ومباشرة
✅ **إعداد بسيط** - خطوات قليلة
✅ **واجهات عربية** - سهلة الاستخدام
✅ **موثوق** - لا يعتمد على خدمات خارجية

---

## 🎉 **النتيجة النهائية:**

**نظام كامل لإدارة العيادة بهاتفين فقط!**

- **هاتف الاستقبال** = خادم + تطبيق
- **هاتف الطبيب** = عميل + تنبيهات
- **الربط** = راوتر محلي
- **البيانات** = محفوظة محلياً

**🚀 ابدأ الآن: شغل تطبيق الاستقبال ثم تطبيق الطبيب!**
