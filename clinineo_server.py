#!/usr/bin/env python3
"""
Clinineo Server - خادم نظام إدارة العيادة
نظام شامل لإدارة العيادة يعمل بدون إنترنت مع ربط محلي
"""

from flask import Flask, render_template_string, request, jsonify, redirect, url_for, send_file
from flask_socketio import SocketIO, emit, join_room, leave_room
import json
import os
import uuid
import sqlite3
import threading
import time
from datetime import datetime, timedelta
import socket
import qrcode
from io import BytesIO
import base64

app = Flask(__name__)
app.config['SECRET_KEY'] = 'clinineo_secret_2024'
socketio = SocketIO(app, cors_allowed_origins="*")

class ClinicDatabase:
    """قاعدة بيانات العيادة"""
    
    def __init__(self, db_path="clinineo.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المرضى
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id TEXT PRIMARY KEY,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                date_of_birth TEXT,
                gender TEXT,
                phone TEXT UNIQUE NOT NULL,
                email TEXT,
                address TEXT,
                blood_type TEXT,
                allergies TEXT,
                emergency_contact TEXT,
                insurance_info TEXT,
                created_at TEXT,
                updated_at TEXT,
                status TEXT DEFAULT 'active'
            )
        ''')
        
        # جدول المواعيد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS appointments (
                id TEXT PRIMARY KEY,
                patient_id TEXT,
                appointment_date TEXT,
                appointment_time TEXT,
                type TEXT,
                status TEXT DEFAULT 'scheduled',
                notes TEXT,
                created_at TEXT,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول الروشتات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS prescriptions (
                id TEXT PRIMARY KEY,
                patient_id TEXT,
                doctor_name TEXT,
                chief_complaint TEXT,
                diagnosis TEXT,
                disease_code TEXT,
                medications TEXT,
                instructions TEXT,
                created_at TEXT,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول الفحوصات والأشعة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS medical_tests (
                id TEXT PRIMARY KEY,
                patient_id TEXT,
                test_type TEXT,
                test_name TEXT,
                description TEXT,
                results TEXT,
                files_path TEXT,
                status TEXT DEFAULT 'pending',
                created_at TEXT,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول التنبيهات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id TEXT PRIMARY KEY,
                title TEXT,
                message TEXT,
                type TEXT,
                target_device TEXT,
                is_read INTEGER DEFAULT 0,
                created_at TEXT
            )
        ''')
        
        # جدول الجلسات النشطة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS active_sessions (
                id TEXT PRIMARY KEY,
                device_type TEXT,
                device_name TEXT,
                ip_address TEXT,
                last_seen TEXT,
                status TEXT DEFAULT 'online'
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def execute_query(self, query, params=None, fetch=False):
        """تنفيذ استعلام قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch:
                result = cursor.fetchall()
                conn.close()
                return [dict(row) for row in result]
            else:
                conn.commit()
                conn.close()
                return cursor.lastrowid
        except Exception as e:
            conn.close()
            raise e

# إنشاء قاعدة البيانات
db = ClinicDatabase()

# متغيرات النظام
connected_devices = {}
system_stats = {
    'server_start_time': datetime.now(),
    'total_connections': 0,
    'active_devices': 0
}

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def generate_qr_code(data):
    """إنشاء QR Code"""
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(data)
    qr.make(fit=True)
    
    img = qr.make_image(fill_color="black", back_color="white")
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    
    img_base64 = base64.b64encode(buffer.getvalue()).decode()
    return f"data:image/png;base64,{img_base64}"

def send_notification(title, message, target_device="all", notification_type="info"):
    """إرسال تنبيه"""
    notification_id = str(uuid.uuid4())[:8]
    
    # حفظ في قاعدة البيانات
    db.execute_query(
        "INSERT INTO notifications (id, title, message, type, target_device, created_at) VALUES (?, ?, ?, ?, ?, ?)",
        (notification_id, title, message, notification_type, target_device, datetime.now().isoformat())
    )
    
    # إرسال عبر WebSocket
    socketio.emit('new_notification', {
        'id': notification_id,
        'title': title,
        'message': message,
        'type': notification_type,
        'timestamp': datetime.now().isoformat()
    }, room=target_device if target_device != "all" else None)

# قوالب HTML المتقدمة
BASE_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Clinineo</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: {{ bg_gradient }};
            min-height: 100vh;
            padding: 10px;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .header {
            background: {{ header_gradient }};
            color: white;
            padding: 25px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }
        
        .status-bar {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }
        
        .content {
            padding: 25px;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 18px;
            margin: 12px 0;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .btn-success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
        .btn-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .btn-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .btn-secondary { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333; }
        .btn-danger { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: {{ accent_color }};
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-control:focus {
            outline: none;
            border-color: {{ accent_color }};
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-left: 5px solid {{ accent_color }};
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateX(5px);
        }
        
        .alert {
            padding: 15px;
            border-radius: 12px;
            margin: 15px 0;
            border-left: 5px solid;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .alert-danger {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid {{ accent_color }};
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .connection-status {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .online { background: rgba(40, 167, 69, 0.9) !important; }
        .offline { background: rgba(220, 53, 69, 0.9) !important; }
        
        @media (max-width: 480px) {
            .container {
                margin: 0;
                border-radius: 0;
                min-height: 100vh;
            }
            
            .stats-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        🔄 جاري الاتصال...
    </div>
    
    <div class="container">
        <div class="header">
            <h1>{{ header_title }}</h1>
            <p>{{ header_subtitle }}</p>
            <div class="status-bar">
                <span id="deviceInfo">📱 {{ device_type }}</span>
                <span id="timeInfo">🕒 {{ current_time }}</span>
            </div>
        </div>
        <div class="content">
            {{ content }}
        </div>
    </div>
    
    <script>
        // إعداد WebSocket
        const socket = io();
        const connectionStatus = document.getElementById('connectionStatus');
        
        socket.on('connect', function() {
            connectionStatus.textContent = '🟢 متصل';
            connectionStatus.className = 'connection-status online';
        });
        
        socket.on('disconnect', function() {
            connectionStatus.textContent = '🔴 غير متصل';
            connectionStatus.className = 'connection-status offline';
        });
        
        // استقبال التنبيهات
        socket.on('new_notification', function(data) {
            showNotification(data.title, data.message, data.type);
        });
        
        function showNotification(title, message, type = 'info') {
            // إنشاء تنبيه
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.innerHTML = `<strong>${title}</strong><br>${message}`;
            
            // إضافة للصفحة
            document.querySelector('.content').insertBefore(notification, document.querySelector('.content').firstChild);
            
            // إزالة بعد 5 ثوان
            setTimeout(() => {
                notification.remove();
            }, 5000);
            
            // صوت تنبيه (إذا كان متاحاً)
            try {
                new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT').play();
            } catch(e) {}
        }
        
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            document.getElementById('timeInfo').textContent = `🕒 ${now.toLocaleTimeString('ar-SA')}`;
        }
        
        setInterval(updateTime, 1000);
        updateTime();
        
        // تسجيل الجهاز
        socket.emit('register_device', {
            type: '{{ device_type }}',
            name: navigator.userAgent,
            timestamp: new Date().toISOString()
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    local_ip = get_local_ip()
    qr_registrar = generate_qr_code(f"http://{local_ip}:5000/registrar")
    qr_doctor = generate_qr_code(f"http://{local_ip}:5000/doctor")
    
    content = f"""
    <div style="text-align: center; padding: 20px;">
        <h2 style="color: #333; margin-bottom: 30px;">🏥 مرحباً بك في Clinineo</h2>
        <p style="color: #666; margin-bottom: 40px;">نظام إدارة العيادات المتطور - يعمل بدون إنترنت</p>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalPatients">0</div>
                <div class="stat-label">إجمالي المرضى</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeDevices">0</div>
                <div class="stat-label">الأجهزة المتصلة</div>
            </div>
        </div>
        
        <a href="/registrar" class="btn btn-primary">
            🏥 تطبيق الاستقبال
        </a>
        
        <a href="/doctor" class="btn btn-success">
            🩺 تطبيق الطبيب
        </a>
        
        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 15px;">
            <h3 style="color: #333; margin-bottom: 20px;">📱 للاتصال من الهاتف:</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div style="text-align: center;">
                    <h4>🏥 الاستقبال</h4>
                    <img src="{qr_registrar}" style="width: 120px; height: 120px; border-radius: 10px;">
                    <p style="font-size: 12px; color: #666; margin-top: 10px;">امسح للوصول السريع</p>
                </div>
                <div style="text-align: center;">
                    <h4>🩺 الطبيب</h4>
                    <img src="{qr_doctor}" style="width: 120px; height: 120px; border-radius: 10px;">
                    <p style="font-size: 12px; color: #666; margin-top: 10px;">امسح للوصول السريع</p>
                </div>
            </div>
            
            <div style="background: white; padding: 15px; border-radius: 10px; margin-top: 20px;">
                <h4 style="color: #333; margin-bottom: 10px;">🔗 الروابط المباشرة:</h4>
                <p style="font-size: 14px; color: #666; margin: 5px 0;">
                    <strong>الاستقبال:</strong> http://{local_ip}:5000/registrar
                </p>
                <p style="font-size: 14px; color: #666; margin: 5px 0;">
                    <strong>الطبيب:</strong> http://{local_ip}:5000/doctor
                </p>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #e3f2fd; border-radius: 10px;">
            <h4 style="color: #1976d2; margin-bottom: 10px;">💡 تعليمات الاستخدام:</h4>
            <ol style="text-align: right; color: #666; font-size: 14px;">
                <li>تأكد من اتصال الجهازين بنفس الشبكة</li>
                <li>امسح QR Code أو اكتب الرابط في المتصفح</li>
                <li>أضف الصفحة للشاشة الرئيسية للوصول السريع</li>
                <li>استخدم التطبيق مثل تطبيق حقيقي!</li>
            </ol>
        </div>
    </div>
    
    <script>
        // تحديث الإحصائيات
        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalPatients').textContent = data.total_patients;
                    document.getElementById('activeDevices').textContent = data.active_devices;
                });
        }
        
        updateStats();
        setInterval(updateStats, 5000);
    </script>
    """
    
    return render_template_string(BASE_TEMPLATE, 
        title="الرئيسية",
        header_title="🏥 Clinineo Server",
        header_subtitle="نظام إدارة العيادات المتطور",
        bg_gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        header_gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        accent_color="#667eea",
        device_type="خادم",
        current_time=datetime.now().strftime('%H:%M'),
        content=content
    )

@app.route('/registrar')
def registrar_home():
    """الصفحة الرئيسية للاستقبال"""
    # إحصائيات سريعة
    total_patients = len(db.execute_query("SELECT id FROM patients WHERE status = 'active'", fetch=True))
    today_patients = len(db.execute_query(
        "SELECT id FROM patients WHERE date(created_at) = date('now')",
        fetch=True
    ))
    pending_appointments = len(db.execute_query(
        "SELECT id FROM appointments WHERE status = 'scheduled' AND date(appointment_date) = date('now')",
        fetch=True
    ))

    content = f"""
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{total_patients}</div>
            <div class="stat-label">إجمالي المرضى</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{today_patients}</div>
            <div class="stat-label">مرضى اليوم</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{pending_appointments}</div>
            <div class="stat-label">مواعيد اليوم</div>
        </div>
    </div>

    <a href="/registrar/add_patient" class="btn btn-primary">
        ➕ إضافة مريض جديد
    </a>

    <a href="/registrar/patients" class="btn btn-info">
        👥 إدارة المرضى
    </a>

    <a href="/registrar/appointments" class="btn btn-warning">
        📅 إدارة المواعيد
    </a>

    <a href="/registrar/search" class="btn btn-secondary">
        🔍 البحث المتقدم
    </a>

    <a href="/registrar/reports" class="btn btn-success">
        📊 تقارير الاستقبال
    </a>

    <a href="/" class="btn btn-secondary">
        🏠 العودة للرئيسية
    </a>

    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
        <h4 style="color: #333; margin-bottom: 10px;">📋 آخر النشاطات:</h4>
        <div id="recentActivities">
            <div class="loading"></div> جاري التحميل...
        </div>
    </div>

    <script>
        // تحديث النشاطات الأخيرة
        function updateRecentActivities() {
            fetch('/api/recent_activities?type=registrar')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('recentActivities');
                    if (data.activities && data.activities.length > 0) {
                        container.innerHTML = data.activities.map(activity =>
                            `<div style="padding: 8px; margin: 5px 0; background: white; border-radius: 8px; border-left: 3px solid #2196f3;">
                                <strong>${activity.title}</strong><br>
                                <small style="color: #666;">${activity.time}</small>
                            </div>`
                        ).join('');
                    } else {
                        container.innerHTML = '<p style="color: #666; text-align: center;">لا توجد نشاطات حديثة</p>';
                    }
                });
        }

        updateRecentActivities();
        setInterval(updateRecentActivities, 10000);
    </script>
    """

    return render_template_string(BASE_TEMPLATE,
        title="الاستقبال",
        header_title="🏥 الاستقبال",
        header_subtitle="إدارة المرضى والمواعيد",
        bg_gradient="linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)",
        header_gradient="linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)",
        accent_color="#2196f3",
        device_type="استقبال",
        current_time=datetime.now().strftime('%H:%M'),
        content=content
    )

@app.route('/registrar/add_patient', methods=['GET', 'POST'])
def add_patient():
    """إضافة مريض جديد"""
    if request.method == 'POST':
        try:
            patient_id = str(uuid.uuid4())[:8]

            # جمع البيانات
            patient_data = {
                'id': patient_id,
                'first_name': request.form.get('first_name', '').strip(),
                'last_name': request.form.get('last_name', '').strip(),
                'date_of_birth': request.form.get('date_of_birth', ''),
                'gender': request.form.get('gender', ''),
                'phone': request.form.get('phone', '').strip(),
                'email': request.form.get('email', '').strip(),
                'address': request.form.get('address', '').strip(),
                'blood_type': request.form.get('blood_type', ''),
                'allergies': request.form.get('allergies', '').strip(),
                'emergency_contact': request.form.get('emergency_contact', '').strip(),
                'insurance_info': request.form.get('insurance_info', '').strip(),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }

            # التحقق من البيانات المطلوبة
            if not patient_data['first_name'] or not patient_data['last_name'] or not patient_data['phone']:
                return redirect(url_for('add_patient') + '?error=البيانات الأساسية مطلوبة')

            # إدراج في قاعدة البيانات
            db.execute_query("""
                INSERT INTO patients (id, first_name, last_name, date_of_birth, gender, phone, email,
                                    address, blood_type, allergies, emergency_contact, insurance_info,
                                    created_at, updated_at, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
            """, tuple(patient_data.values()))

            # إرسال تنبيه للطبيب
            send_notification(
                "مريض جديد",
                f"تم تسجيل مريض جديد: {patient_data['first_name']} {patient_data['last_name']}",
                "doctor",
                "success"
            )

            return redirect(url_for('registrar_home') + '?success=تم إضافة المريض بنجاح')

        except Exception as e:
            return redirect(url_for('add_patient') + f'?error=حدث خطأ: {str(e)}')

    # عرض النموذج
    error_msg = request.args.get('error', '')
    success_msg = request.args.get('success', '')

    content = f"""
    <a href="/registrar" class="btn btn-secondary" style="margin-bottom: 20px; padding: 10px 20px; font-size: 14px;">← العودة</a>

    {f'<div class="alert alert-danger">{error_msg}</div>' if error_msg else ''}
    {f'<div class="alert alert-success">{success_msg}</div>' if success_msg else ''}

    <form method="POST" id="patientForm">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <div class="form-group">
                <label>الاسم الأول *</label>
                <input type="text" name="first_name" class="form-control" required>
            </div>

            <div class="form-group">
                <label>اسم العائلة *</label>
                <input type="text" name="last_name" class="form-control" required>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <div class="form-group">
                <label>تاريخ الميلاد</label>
                <input type="date" name="date_of_birth" class="form-control">
            </div>

            <div class="form-group">
                <label>الجنس</label>
                <select name="gender" class="form-control">
                    <option value="">اختر الجنس</option>
                    <option value="male">ذكر</option>
                    <option value="female">أنثى</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <label>رقم الهاتف *</label>
            <input type="tel" name="phone" class="form-control" required placeholder="05xxxxxxxx">
        </div>

        <div class="form-group">
            <label>البريد الإلكتروني</label>
            <input type="email" name="email" class="form-control" placeholder="<EMAIL>">
        </div>

        <div class="form-group">
            <label>العنوان</label>
            <textarea name="address" class="form-control" rows="3" placeholder="العنوان التفصيلي..."></textarea>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <div class="form-group">
                <label>فصيلة الدم</label>
                <select name="blood_type" class="form-control">
                    <option value="">اختر فصيلة الدم</option>
                    <option value="A+">A+</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B-">B-</option>
                    <option value="AB+">AB+</option>
                    <option value="AB-">AB-</option>
                    <option value="O+">O+</option>
                    <option value="O-">O-</option>
                </select>
            </div>

            <div class="form-group">
                <label>جهة اتصال الطوارئ</label>
                <input type="tel" name="emergency_contact" class="form-control" placeholder="05xxxxxxxx">
            </div>
        </div>

        <div class="form-group">
            <label>الحساسية المعروفة</label>
            <textarea name="allergies" class="form-control" rows="2" placeholder="مثال: حساسية من البنسلين، الفول السوداني..."></textarea>
        </div>

        <div class="form-group">
            <label>معلومات التأمين</label>
            <textarea name="insurance_info" class="form-control" rows="2" placeholder="شركة التأمين، رقم البوليصة..."></textarea>
        </div>

        <button type="submit" class="btn btn-primary">
            💾 حفظ المريض
        </button>
    </form>

    <script>
        // التحقق من صحة النموذج
        document.getElementById('patientForm').addEventListener('submit', function(e) {
            const firstName = document.querySelector('input[name="first_name"]').value.trim();
            const lastName = document.querySelector('input[name="last_name"]').value.trim();
            const phone = document.querySelector('input[name="phone"]').value.trim();

            if (!firstName || !lastName || !phone) {
                e.preventDefault();
                alert('يرجى ملء جميع البيانات المطلوبة (*)');
                return;
            }

            // التحقق من صحة رقم الهاتف
            const phoneRegex = /^05[0-9]{8}$/;
            if (!phoneRegex.test(phone)) {
                e.preventDefault();
                alert('يرجى إدخال رقم هاتف صحيح (05xxxxxxxx)');
                return;
            }
        });
    </script>
    """

    return render_template_string(BASE_TEMPLATE,
        title="إضافة مريض",
        header_title="➕ إضافة مريض جديد",
        header_subtitle="إدخال بيانات المريض الكاملة",
        bg_gradient="linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)",
        header_gradient="linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)",
        accent_color="#4caf50",
        device_type="استقبال",
        current_time=datetime.now().strftime('%H:%M'),
        content=content
    )

@app.route('/doctor')
def doctor_home():
    """الصفحة الرئيسية للطبيب"""
    # إحصائيات سريعة
    total_patients = len(db.execute_query("SELECT id FROM patients WHERE status = 'active'", fetch=True))
    total_prescriptions = len(db.execute_query("SELECT id FROM prescriptions", fetch=True))
    unread_notifications = len(db.execute_query("SELECT id FROM notifications WHERE target_device = 'doctor' AND is_read = 0", fetch=True))
    today_appointments = len(db.execute_query(
        "SELECT id FROM appointments WHERE status = 'scheduled' AND date(appointment_date) = date('now')",
        fetch=True
    ))

    content = f"""
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{total_patients}</div>
            <div class="stat-label">إجمالي المرضى</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{total_prescriptions}</div>
            <div class="stat-label">الروشتات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{today_appointments}</div>
            <div class="stat-label">مواعيد اليوم</div>
        </div>
    </div>

    {f'<div class="alert alert-info">🔔 لديك {unread_notifications} تنبيه جديد</div>' if unread_notifications > 0 else ''}

    <a href="/doctor/notifications" class="btn btn-warning" style="position: relative;">
        🔔 التنبيهات
        {f'<span class="notification-badge">{unread_notifications}</span>' if unread_notifications > 0 else ''}
    </a>

    <a href="/doctor/patients" class="btn btn-primary">
        👥 المرضى
    </a>

    <a href="/doctor/prescriptions" class="btn btn-success">
        📝 الروشتات
    </a>

    <a href="/doctor/tests" class="btn btn-info">
        🩻 الفحوصات والأشعة
    </a>

    <a href="/doctor/reports" class="btn btn-warning">
        📊 التقارير والإحصائيات
    </a>

    <a href="/" class="btn btn-secondary">
        🏠 العودة للرئيسية
    </a>

    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
        <h4 style="color: #333; margin-bottom: 10px;">📋 آخر النشاطات:</h4>
        <div id="doctorActivities">
            <div class="loading"></div> جاري التحميل...
        </div>
    </div>

    <script>
        // تحديث النشاطات الأخيرة
        function updateDoctorActivities() {
            fetch('/api/recent_activities?type=doctor')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('doctorActivities');
                    if (data.activities && data.activities.length > 0) {
                        container.innerHTML = data.activities.map(activity =>
                            `<div style="padding: 8px; margin: 5px 0; background: white; border-radius: 8px; border-left: 3px solid #4caf50;">
                                <strong>${activity.title}</strong><br>
                                <small style="color: #666;">${activity.time}</small>
                            </div>`
                        ).join('');
                    } else {
                        container.innerHTML = '<p style="color: #666; text-align: center;">لا توجد نشاطات حديثة</p>';
                    }
                });
        }

        updateDoctorActivities();
        setInterval(updateDoctorActivities, 10000);
    </script>
    """

    return render_template_string(BASE_TEMPLATE,
        title="الطبيب",
        header_title="🩺 د. أحمد محمد",
        header_subtitle="طب عام - عيادة Clinineo",
        bg_gradient="linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)",
        header_gradient="linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)",
        accent_color="#4caf50",
        device_type="طبيب",
        current_time=datetime.now().strftime('%H:%M'),
        content=content
    )

# APIs للتطبيق
@app.route('/api/stats')
def api_stats():
    """API للإحصائيات"""
    try:
        total_patients = len(db.execute_query("SELECT id FROM patients WHERE status = 'active'", fetch=True))
        active_devices = len(connected_devices)

        return jsonify({
            'total_patients': total_patients,
            'active_devices': active_devices,
            'server_uptime': str(datetime.now() - system_stats['server_start_time']).split('.')[0]
        })
    except:
        return jsonify({'error': 'فشل في جلب الإحصائيات'}), 500

@app.route('/api/recent_activities')
def api_recent_activities():
    """API للنشاطات الأخيرة"""
    try:
        activity_type = request.args.get('type', 'all')

        activities = []

        if activity_type in ['registrar', 'all']:
            # نشاطات الاستقبال
            recent_patients = db.execute_query(
                "SELECT first_name, last_name, created_at FROM patients ORDER BY created_at DESC LIMIT 5",
                fetch=True
            )

            for patient in recent_patients:
                activities.append({
                    'title': f"مريض جديد: {patient['first_name']} {patient['last_name']}",
                    'time': patient['created_at'][:16].replace('T', ' '),
                    'type': 'patient'
                })

        if activity_type in ['doctor', 'all']:
            # نشاطات الطبيب
            recent_prescriptions = db.execute_query(
                "SELECT p.first_name, p.last_name, pr.created_at FROM prescriptions pr JOIN patients p ON pr.patient_id = p.id ORDER BY pr.created_at DESC LIMIT 5",
                fetch=True
            )

            for prescription in recent_prescriptions:
                activities.append({
                    'title': f"روشتة جديدة: {prescription['first_name']} {prescription['last_name']}",
                    'time': prescription['created_at'][:16].replace('T', ' '),
                    'type': 'prescription'
                })

        # ترتيب حسب الوقت
        activities.sort(key=lambda x: x['time'], reverse=True)

        return jsonify({'activities': activities[:5]})

    except Exception as e:
        return jsonify({'error': f'فشل في جلب النشاطات: {str(e)}'}), 500

# WebSocket Events
@socketio.on('connect')
def handle_connect():
    """عند الاتصال"""
    system_stats['total_connections'] += 1
    system_stats['active_devices'] = len(connected_devices) + 1
    print(f"جهاز جديد متصل. إجمالي الاتصالات: {system_stats['total_connections']}")

@socketio.on('disconnect')
def handle_disconnect():
    """عند قطع الاتصال"""
    system_stats['active_devices'] = max(0, system_stats['active_devices'] - 1)
    print(f"جهاز منقطع. الأجهزة النشطة: {system_stats['active_devices']}")

@socketio.on('register_device')
def handle_register_device(data):
    """تسجيل جهاز جديد"""
    device_id = str(uuid.uuid4())[:8]
    device_info = {
        'id': device_id,
        'type': data.get('type', 'unknown'),
        'name': data.get('name', 'Unknown Device'),
        'connected_at': datetime.now().isoformat(),
        'last_seen': datetime.now().isoformat()
    }

    connected_devices[device_id] = device_info

    # حفظ في قاعدة البيانات
    try:
        db.execute_query(
            "INSERT OR REPLACE INTO active_sessions (id, device_type, device_name, ip_address, last_seen, status) VALUES (?, ?, ?, ?, ?, 'online')",
            (device_id, device_info['type'], device_info['name'], request.remote_addr, device_info['last_seen'])
        )
    except:
        pass

    emit('device_registered', {'device_id': device_id, 'status': 'success'})
    print(f"تم تسجيل جهاز جديد: {device_info['type']} - {device_id}")

def start_server():
    """بدء تشغيل الخادم"""
    local_ip = get_local_ip()
    port = 5000

    print("🌐" + "="*60)
    print("🏥 Clinineo Advanced Server - خادم نظام إدارة العيادة")
    print("="*62)
    print(f"🔗 الروابط المتاحة:")
    print(f"   📱 الصفحة الرئيسية: http://{local_ip}:{port}/")
    print(f"   🏥 تطبيق الاستقبال: http://{local_ip}:{port}/registrar")
    print(f"   🩺 تطبيق الطبيب: http://{local_ip}:{port}/doctor")
    print("="*62)
    print(f"📱 للاستخدام على الهاتف:")
    print(f"   1. تأكد من اتصال الهاتف بنفس الشبكة")
    print(f"   2. افتح المتصفح على الهاتف")
    print(f"   3. اذهب إلى: http://{local_ip}:{port}/")
    print(f"   4. امسح QR Code أو اختر التطبيق")
    print(f"   5. أضف للشاشة الرئيسية")
    print("="*62)
    print(f"🔧 الخادم يعمل على: {local_ip}:{port}")
    print(f"💾 قاعدة البيانات: clinineo.db")
    print(f"🛑 اضغط Ctrl+C للإيقاف")
    print("="*62)

    try:
        socketio.run(app, host='0.0.0.0', port=port, debug=False, allow_unsafe_werkzeug=True)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == '__main__':
    start_server()
