#!/usr/bin/env python3
"""
Clinineo Doctor Application
تطبيق الطبيب - Clinineo
"""

import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
from datetime import datetime, date
import threading
import time

class DoctorApp:
    """تطبيق الطبيب"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Clinineo - الطبيب")
        self.root.geometry("1200x800")
        self.root.minsize(900, 600)
        
        # إعدادات الخادم
        self.server_ip = "127.0.0.1"
        self.server_port = "8080"
        self.base_url = f"http://{self.server_ip}:{self.server_port}"
        self.current_user = None
        self.token = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.check_server_connection()
        self.start_notification_checker()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة المرضى
        patients_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المرضى", menu=patients_menu)
        patients_menu.add_command(label="قائمة المرضى", command=self.show_patients_list)
        patients_menu.add_command(label="البحث عن مريض", command=self.show_patient_search)
        
        # قائمة المواعيد
        appointments_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المواعيد", menu=appointments_menu)
        appointments_menu.add_command(label="مواعيد اليوم", command=self.show_today_appointments)
        appointments_menu.add_command(label="جميع المواعيد", command=self.show_all_appointments)
        
        # قائمة السجلات الطبية
        records_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="السجلات الطبية", menu=records_menu)
        records_menu.add_command(label="إضافة سجل طبي", command=self.show_add_medical_record)
        records_menu.add_command(label="عرض السجلات", command=self.show_medical_records)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="لوحة المعلومات", command=self.show_dashboard)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="إعدادات الخادم", command=self.show_server_settings)
        settings_menu.add_command(label="تسجيل دخول", command=self.show_login)
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط الحالة والتنبيهات
        self.status_frame = ttk.Frame(main_frame)
        self.status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(self.status_frame, text="غير متصل بالخادم", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        # منطقة التنبيهات
        self.notification_frame = ttk.Frame(self.status_frame)
        self.notification_frame.pack(side=tk.RIGHT)
        
        self.notification_label = ttk.Label(self.notification_frame, text="", foreground="blue")
        self.notification_label.pack(side=tk.RIGHT, padx=10)
        
        self.user_label = ttk.Label(self.status_frame, text="")
        self.user_label.pack(side=tk.RIGHT)
        
        # منطقة المحتوى
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # الصفحة الرئيسية
        self.show_home_page()
    
    def check_server_connection(self):
        """فحص الاتصال بالخادم"""
        def check():
            try:
                response = requests.get(f"{self.base_url}/health", timeout=3)
                if response.status_code == 200:
                    self.root.after(0, self.on_server_connected)
                else:
                    self.root.after(0, self.on_server_disconnected)
            except:
                self.root.after(0, self.on_server_disconnected)
        
        threading.Thread(target=check, daemon=True).start()
    
    def on_server_connected(self):
        """عند الاتصال بالخادم"""
        self.status_label.config(text=f"متصل بالخادم: {self.base_url}", foreground="green")
    
    def on_server_disconnected(self):
        """عند انقطاع الاتصال بالخادم"""
        self.status_label.config(text="غير متصل بالخادم", foreground="red")
    
    def start_notification_checker(self):
        """بدء فحص التنبيهات الدورية"""
        def check_notifications():
            while True:
                if self.current_user and self.current_user.get('role') == 'doctor':
                    try:
                        # محاكاة فحص التنبيهات
                        # في التطبيق الحقيقي، ستكون هناك API للتنبيهات
                        current_time = datetime.now().strftime("%H:%M:%S")
                        self.root.after(0, lambda: self.notification_label.config(
                            text=f"آخر فحص للتنبيهات: {current_time}"
                        ))
                    except:
                        pass
                time.sleep(30)  # فحص كل 30 ثانية
        
        threading.Thread(target=check_notifications, daemon=True).start()
    
    def show_server_settings(self):
        """إظهار إعدادات الخادم"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إعدادات الخادم")
        dialog.geometry("300x150")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="عنوان IP للخادم:").pack(pady=5)
        ip_entry = ttk.Entry(dialog, width=20)
        ip_entry.pack(pady=5)
        ip_entry.insert(0, self.server_ip)
        
        ttk.Label(dialog, text="المنفذ:").pack(pady=5)
        port_entry = ttk.Entry(dialog, width=20)
        port_entry.pack(pady=5)
        port_entry.insert(0, self.server_port)
        
        def save_settings():
            self.server_ip = ip_entry.get().strip()
            self.server_port = port_entry.get().strip()
            self.base_url = f"http://{self.server_ip}:{self.server_port}"
            dialog.destroy()
            self.check_server_connection()
        
        ttk.Button(dialog, text="حفظ", command=save_settings).pack(pady=10)
    
    def show_login(self):
        """إظهار نافذة تسجيل الدخول"""
        dialog = tk.Toplevel(self.root)
        dialog.title("تسجيل الدخول")
        dialog.geometry("300x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="البريد الإلكتروني:").pack(pady=5)
        email_entry = ttk.Entry(dialog, width=25)
        email_entry.pack(pady=5)
        email_entry.insert(0, "<EMAIL>")
        
        ttk.Label(dialog, text="كلمة المرور:").pack(pady=5)
        password_entry = ttk.Entry(dialog, width=25, show="*")
        password_entry.pack(pady=5)
        password_entry.insert(0, "doctor123")
        
        def login():
            email = email_entry.get().strip()
            password = password_entry.get().strip()
            
            try:
                response = requests.post(f"{self.base_url}/api/auth/login", 
                                       json={"email": email, "password": password},
                                       timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        self.current_user = data["user"]
                        self.token = data["access_token"]
                        user_info = f"د. {self.current_user['first_name']} {self.current_user['last_name']}"
                        if self.current_user.get('specialization'):
                            user_info += f" - {self.current_user['specialization']}"
                        self.user_label.config(text=user_info)
                        dialog.destroy()
                        messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح")
                        self.show_dashboard()  # إظهار لوحة المعلومات بعد تسجيل الدخول
                    else:
                        messagebox.showerror("خطأ", data.get("message", "فشل تسجيل الدخول"))
                else:
                    messagebox.showerror("خطأ", "فشل في الاتصال بالخادم")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")
        
        ttk.Button(dialog, text="تسجيل دخول", command=login).pack(pady=10)
    
    def show_home_page(self):
        """إظهار الصفحة الرئيسية"""
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # العنوان
        title_label = ttk.Label(self.content_frame, text="نظام إدارة العيادة - الطبيب", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # الأزرار الرئيسية
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(pady=20)
        
        ttk.Button(buttons_frame, text="لوحة المعلومات", 
                  command=self.show_dashboard, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="مواعيد اليوم", 
                  command=self.show_today_appointments, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="قائمة المرضى", 
                  command=self.show_patients_list, width=20).pack(pady=5)
        
        ttk.Button(buttons_frame, text="إضافة سجل طبي", 
                  command=self.show_add_medical_record, width=20).pack(pady=5)
        
        # رسالة ترحيب
        if not self.current_user:
            welcome_label = ttk.Label(self.content_frame, 
                                    text="يرجى تسجيل الدخول للوصول إلى جميع الميزات",
                                    font=('Arial', 12), foreground="orange")
            welcome_label.pack(pady=20)
    
    def show_dashboard(self):
        """إظهار لوحة المعلومات"""
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        ttk.Label(self.content_frame, text="لوحة المعلومات", 
                 font=('Arial', 14, 'bold')).pack(pady=10)
        
        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(self.content_frame, text="الإحصائيات", padding="10")
        stats_frame.pack(fill=tk.X, pady=10, padx=20)
        
        # تحميل الإحصائيات
        try:
            response = requests.get(f"{self.base_url}/api/reports/dashboard", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    stats = data.get("data", {})
                    
                    # عرض الإحصائيات في شبكة
                    stats_grid = ttk.Frame(stats_frame)
                    stats_grid.pack(fill=tk.X)
                    
                    # الصف الأول
                    ttk.Label(stats_grid, text=f"إجمالي المرضى: {stats.get('total_patients', 0)}", 
                             font=('Arial', 12, 'bold')).grid(row=0, column=0, padx=20, pady=5, sticky='w')
                    
                    ttk.Label(stats_grid, text=f"مواعيد اليوم: {stats.get('today_appointments', 0)}", 
                             font=('Arial', 12, 'bold')).grid(row=0, column=1, padx=20, pady=5, sticky='w')
                    
                    # الصف الثاني
                    ttk.Label(stats_grid, text=f"الأطباء النشطين: {stats.get('active_doctors', 0)}", 
                             font=('Arial', 12, 'bold')).grid(row=1, column=0, padx=20, pady=5, sticky='w')
                    
                    ttk.Label(stats_grid, text=f"المواعيد المعلقة: {stats.get('pending_appointments', 0)}", 
                             font=('Arial', 12, 'bold')).grid(row=1, column=1, padx=20, pady=5, sticky='w')
                    
        except Exception as e:
            ttk.Label(stats_frame, text=f"فشل في تحميل الإحصائيات: {str(e)}", 
                     foreground="red").pack()
        
        # إطار التنبيهات
        notifications_frame = ttk.LabelFrame(self.content_frame, text="التنبيهات الحديثة", padding="10")
        notifications_frame.pack(fill=tk.BOTH, expand=True, pady=10, padx=20)
        
        # قائمة التنبيهات (محاكاة)
        notifications_text = tk.Text(notifications_frame, height=10, wrap=tk.WORD)
        notifications_text.pack(fill=tk.BOTH, expand=True)
        
        # إضافة تنبيهات تجريبية
        sample_notifications = [
            "🔔 مريض جديد تم تسجيله: أحمد محمد",
            "📅 موعد جديد تم حجزه لغداً الساعة 10:00",
            "📋 تذكير: مراجعة السجلات الطبية المعلقة",
            "⚡ النظام يعمل بشكل طبيعي"
        ]
        
        for notification in sample_notifications:
            notifications_text.insert(tk.END, f"{datetime.now().strftime('%H:%M')} - {notification}\n")
        
        notifications_text.config(state=tk.DISABLED)
        
        # زر العودة
        ttk.Button(self.content_frame, text="العودة للرئيسية", 
                  command=self.show_home_page).pack(pady=10)
    
    def show_patients_list(self):
        """إظهار قائمة المرضى"""
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        ttk.Label(self.content_frame, text="قائمة المرضى", 
                 font=('Arial', 14, 'bold')).pack(pady=10)
        
        # جدول المرضى
        columns = ('ID', 'الاسم', 'الهاتف', 'الجنس', 'العمر')
        tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        # تحميل البيانات
        try:
            response = requests.get(f"{self.base_url}/api/patients", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    patients = data.get("data", [])
                    for patient in patients:
                        # حساب العمر
                        try:
                            birth_date = datetime.strptime(patient.get('date_of_birth', ''), '%Y-%m-%d').date()
                            age = (date.today() - birth_date).days // 365
                        except:
                            age = "غير محدد"
                        
                        tree.insert('', tk.END, values=(
                            patient.get('id', ''),
                            f"{patient.get('first_name', '')} {patient.get('last_name', '')}",
                            patient.get('phone', ''),
                            'ذكر' if patient.get('gender') == 'male' else 'أنثى',
                            age
                        ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
        
        tree.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # زر العودة
        ttk.Button(self.content_frame, text="العودة للرئيسية", 
                  command=self.show_home_page).pack(pady=10)
    
    # باقي الدوال (محاكاة)
    def show_patient_search(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_today_appointments(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_all_appointments(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_add_medical_record(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def show_medical_records(self):
        messagebox.showinfo("قريباً", "ستتم إضافة هذه الميزة قريباً")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = DoctorApp()
    app.run()
