#!/usr/bin/env python3
"""
Test Connection - اختبار الاتصال
صفحة بسيطة لاختبار الاتصال من الموبايل
"""

from flask import Flask
import socket

app = Flask(__name__)

@app.route('/')
def test_page():
    return """
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>اختبار الاتصال - Clinineo</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                border-radius: 20px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                max-width: 400px;
                width: 100%;
            }
            h1 {
                color: #333;
                margin-bottom: 20px;
                font-size: 24px;
            }
            .success {
                background: #d4edda;
                color: #155724;
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                font-size: 18px;
                font-weight: bold;
            }
            .info {
                background: #e3f2fd;
                color: #0d47a1;
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                font-size: 14px;
            }
            .btn {
                display: inline-block;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px 30px;
                border-radius: 10px;
                text-decoration: none;
                font-weight: bold;
                margin: 10px;
                font-size: 16px;
            }
            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 تم الاتصال بنجاح!</h1>
            
            <div class="success">
                ✅ الهاتف متصل بالخادم بشكل صحيح
            </div>
            
            <div class="info">
                📱 هذا يعني أن التطبيق سيعمل على هاتفك بدون مشاكل
            </div>
            
            <a href="/registrar" class="btn">
                🏥 تطبيق الاستقبال
            </a>
            
            <a href="/doctor" class="btn">
                🩺 تطبيق الطبيب
            </a>
            
            <div class="info" style="margin-top: 30px;">
                💡 نصيحة: أضف هذه الصفحة للشاشة الرئيسية للوصول السريع
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/registrar')
def registrar_redirect():
    return """
    <script>
        alert('✅ تطبيق الاستقبال يعمل! سيتم تحويلك للتطبيق الكامل...');
        window.location.href = 'http://""" + get_local_ip() + """:8080/registrar';
    </script>
    """

@app.route('/doctor')
def doctor_redirect():
    return """
    <script>
        alert('✅ تطبيق الطبيب يعمل! سيتم تحويلك للتطبيق الكامل...');
        window.location.href = 'http://""" + get_local_ip() + """:8080/doctor';
    </script>
    """

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

if __name__ == '__main__':
    local_ip = get_local_ip()
    port = 9090  # منفذ مختلف للاختبار
    
    print("🧪" + "="*50)
    print("🔍 اختبار الاتصال - Clinineo")
    print("="*52)
    print(f"📱 اختبر الاتصال من الهاتف:")
    print(f"   http://{local_ip}:{port}/")
    print("="*52)
    print(f"🔧 خادم الاختبار يعمل على: {local_ip}:{port}")
    print("🛑 اضغط Ctrl+C للإيقاف")
    print("="*52)
    
    app.run(host='0.0.0.0', port=port, debug=False)
