#!/usr/bin/env python3
"""
Android Registrar App with Built-in Server
تطبيق موظف الاستقبال مع خادم مدمج - بدون كمبيوتر
"""

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.popup import Popup
from kivy.uix.spinner import Spinner
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
import json
import socket
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from datetime import datetime
import uuid
import os

class ClinicData:
    """مدير البيانات المحلي"""
    
    def __init__(self):
        self.data_file = "clinic_data_android.json"
        self.data = self.load_data()
    
    def load_data(self):
        """تحميل البيانات من الملف"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        # بيانات افتراضية
        return {
            "users": [
                {
                    "id": "1",
                    "email": "<EMAIL>",
                    "password": "registrar123",
                    "first_name": "سارة",
                    "last_name": "أحمد",
                    "role": "registrar"
                },
                {
                    "id": "2",
                    "email": "<EMAIL>",
                    "password": "doctor123",
                    "first_name": "د. أحمد",
                    "last_name": "محمد",
                    "role": "doctor",
                    "specialization": "طب عام"
                }
            ],
            "patients": [],
            "notifications": [],
            "last_updated": datetime.now().isoformat()
        }
    
    def save_data(self):
        """حفظ البيانات في الملف"""
        try:
            self.data["last_updated"] = datetime.now().isoformat()
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def add_patient(self, patient_data):
        """إضافة مريض جديد"""
        patient_id = str(uuid.uuid4())[:8]
        patient = {
            "id": patient_id,
            "first_name": patient_data.get("first_name", ""),
            "last_name": patient_data.get("last_name", ""),
            "date_of_birth": patient_data.get("date_of_birth", ""),
            "gender": patient_data.get("gender", ""),
            "phone": patient_data.get("phone", ""),
            "email": patient_data.get("email", ""),
            "address": patient_data.get("address", ""),
            "created_at": datetime.now().isoformat()
        }
        
        self.data["patients"].append(patient)
        
        # إضافة تنبيه للأطباء
        notification = {
            "id": str(uuid.uuid4())[:8],
            "title": "مريض جديد",
            "message": f"تم تسجيل مريض جديد: {patient['first_name']} {patient['last_name']}",
            "type": "info",
            "is_read": False,
            "created_at": datetime.now().isoformat()
        }
        
        self.data["notifications"].append(notification)
        self.save_data()
        return patient_id

class ClinicAPIHandler(BaseHTTPRequestHandler):
    """معالج API للعيادة"""
    
    def __init__(self, clinic_data, *args, **kwargs):
        self.clinic_data = clinic_data
        super().__init__(*args, **kwargs)
    
    def do_OPTIONS(self):
        """معالجة CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path == '/api/health':
            self.send_json_response({
                "status": "healthy",
                "message": "خادم الاستقبال يعمل",
                "timestamp": datetime.now().isoformat()
            })
        
        elif self.path == '/api/patients':
            patients = self.clinic_data.data.get("patients", [])
            self.send_json_response({
                "success": True,
                "data": patients
            })
        
        elif self.path == '/api/notifications':
            notifications = self.clinic_data.data.get("notifications", [])
            self.send_json_response({
                "success": True,
                "data": notifications
            })
        
        elif self.path == '/api/dashboard':
            stats = {
                "total_patients": len(self.clinic_data.data.get("patients", [])),
                "total_notifications": len(self.clinic_data.data.get("notifications", [])),
                "unread_notifications": len([n for n in self.clinic_data.data.get("notifications", []) if not n.get("is_read", False)]),
                "server_status": "online"
            }
            self.send_json_response({
                "success": True,
                "data": stats
            })
        
        else:
            self.send_error(404)
    
    def do_POST(self):
        """معالجة طلبات POST"""
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode('utf-8'))
            except:
                data = {}
        else:
            data = {}
        
        if self.path == '/api/auth/login':
            email = data.get('email', '')
            password = data.get('password', '')
            
            for user in self.clinic_data.data.get("users", []):
                if user.get("email") == email and user.get("password") == password:
                    self.send_json_response({
                        "success": True,
                        "user": user,
                        "access_token": f"token-{user['id']}"
                    })
                    return
            
            self.send_json_response({
                "success": False,
                "message": "بيانات تسجيل الدخول غير صحيحة"
            })
        
        elif self.path == '/api/patients':
            try:
                patient_id = self.clinic_data.add_patient(data)
                self.send_json_response({
                    "success": True,
                    "message": "تم إضافة المريض بنجاح",
                    "patient_id": patient_id
                })
            except Exception as e:
                self.send_json_response({
                    "success": False,
                    "message": f"خطأ في إضافة المريض: {str(e)}"
                })
        
        elif self.path == '/api/notifications/read':
            notification_id = data.get('notification_id')
            for notification in self.clinic_data.data.get("notifications", []):
                if notification.get("id") == notification_id:
                    notification["is_read"] = True
                    notification["read_at"] = datetime.now().isoformat()
                    break
            
            self.clinic_data.save_data()
            self.send_json_response({
                "success": True,
                "message": "تم تمييز التنبيه كمقروء"
            })
        
        else:
            self.send_error(404)
    
    def send_json_response(self, data):
        """إرسال استجابة JSON"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """تسجيل صامت"""
        pass

class RegistrarServerApp(App):
    """تطبيق موظف الاستقبال مع خادم مدمج"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.clinic_data = ClinicData()
        self.server = None
        self.server_thread = None
        self.server_ip = self.get_local_ip()
        self.server_port = 8080
        self.current_user = None
    
    def build(self):
        """بناء واجهة التطبيق"""
        self.title = "Clinineo - موظف الاستقبال (خادم)"
        
        # التخطيط الرئيسي
        main_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # شريط العنوان
        title_label = Label(
            text='نظام إدارة العيادة - موظف الاستقبال\n(خادم مدمج)',
            size_hint_y=None,
            height=60,
            font_size=18
        )
        main_layout.add_widget(title_label)
        
        # معلومات الخادم
        self.server_info_label = Label(
            text=f'عنوان الخادم: http://{self.server_ip}:{self.server_port}',
            size_hint_y=None,
            height=40,
            color=(0, 0, 1, 1)
        )
        main_layout.add_widget(self.server_info_label)
        
        # شريط الحالة
        self.status_label = Label(
            text='الخادم متوقف',
            size_hint_y=None,
            height=30,
            color=(1, 0, 0, 1)
        )
        main_layout.add_widget(self.status_label)
        
        # منطقة المحتوى
        self.content_layout = BoxLayout(orientation='vertical', spacing=10)
        main_layout.add_widget(self.content_layout)
        
        # عرض الشاشة الرئيسية
        self.show_main_screen()
        
        # بدء الخادم تلقائياً
        Clock.schedule_once(self.start_server, 1)
        
        return main_layout
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def start_server(self, dt=None):
        """بدء تشغيل الخادم"""
        try:
            def create_handler(clinic_data):
                def handler(*args, **kwargs):
                    return ClinicAPIHandler(clinic_data, *args, **kwargs)
                return handler
            
            self.server = HTTPServer(('', self.server_port), create_handler(self.clinic_data))
            
            def run_server():
                try:
                    self.server.serve_forever()
                except:
                    pass
            
            self.server_thread = threading.Thread(target=run_server, daemon=True)
            self.server_thread.start()
            
            self.status_label.text = f'الخادم يعمل على: {self.server_ip}:{self.server_port}'
            self.status_label.color = (0, 1, 0, 1)
            
        except Exception as e:
            self.status_label.text = f'خطأ في تشغيل الخادم: {str(e)}'
            self.status_label.color = (1, 0, 0, 1)
    
    def show_main_screen(self):
        """عرض الشاشة الرئيسية"""
        self.content_layout.clear_widgets()
        
        main_layout = GridLayout(cols=1, spacing=10, size_hint_y=None)
        main_layout.bind(minimum_height=main_layout.setter('height'))
        
        # تسجيل دخول تلقائي
        if not self.current_user:
            for user in self.clinic_data.data.get("users", []):
                if user.get("role") == "registrar":
                    self.current_user = user
                    break
        
        # ترحيب
        if self.current_user:
            welcome_text = f"مرحباً {self.current_user['first_name']} {self.current_user['last_name']}"
            main_layout.add_widget(Label(text=welcome_text, font_size=16, size_hint_y=None, height=40))
        
        # معلومات الاتصال
        connection_info = f"للاتصال من تطبيق الطبيب:\nhttp://{self.server_ip}:{self.server_port}"
        main_layout.add_widget(Label(text=connection_info, size_hint_y=None, height=60))
        
        # الأزرار الرئيسية
        add_patient_btn = Button(
            text='إضافة مريض جديد',
            size_hint_y=None,
            height=60
        )
        add_patient_btn.bind(on_press=self.show_add_patient_screen)
        main_layout.add_widget(add_patient_btn)
        
        patients_list_btn = Button(
            text='قائمة المرضى',
            size_hint_y=None,
            height=60
        )
        patients_list_btn.bind(on_press=self.show_patients_list)
        main_layout.add_widget(patients_list_btn)
        
        stats_btn = Button(
            text='الإحصائيات',
            size_hint_y=None,
            height=60
        )
        stats_btn.bind(on_press=self.show_stats)
        main_layout.add_widget(stats_btn)
        
        # إضافة إلى التمرير
        scroll = ScrollView()
        scroll.add_widget(main_layout)
        self.content_layout.add_widget(scroll)
    
    def show_add_patient_screen(self, instance):
        """عرض شاشة إضافة مريض"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        # حقول المريض
        fields_layout = GridLayout(cols=1, spacing=5, size_hint_y=None)
        fields_layout.bind(minimum_height=fields_layout.setter('height'))
        
        fields_layout.add_widget(Label(text='إضافة مريض جديد', font_size=18, size_hint_y=None, height=40))
        
        self.first_name_input = TextInput(hint_text='الاسم الأول', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.first_name_input)
        
        self.last_name_input = TextInput(hint_text='الاسم الأخير', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.last_name_input)
        
        self.birth_date_input = TextInput(hint_text='تاريخ الميلاد (YYYY-MM-DD)', text='1990-01-01', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.birth_date_input)
        
        self.gender_spinner = Spinner(
            text='male',
            values=['male', 'female'],
            size_hint_y=None,
            height=40
        )
        fields_layout.add_widget(self.gender_spinner)
        
        self.phone_input = TextInput(hint_text='رقم الهاتف', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.phone_input)
        
        self.email_patient_input = TextInput(hint_text='البريد الإلكتروني (اختياري)', multiline=False, size_hint_y=None, height=40)
        fields_layout.add_widget(self.email_patient_input)
        
        # أزرار
        buttons_layout = BoxLayout(spacing=10, size_hint_y=None, height=60)
        
        save_btn = Button(text='حفظ')
        save_btn.bind(on_press=lambda x: self.save_patient(popup))
        buttons_layout.add_widget(save_btn)
        
        cancel_btn = Button(text='إلغاء')
        cancel_btn.bind(on_press=lambda x: popup.dismiss())
        buttons_layout.add_widget(cancel_btn)
        
        fields_layout.add_widget(buttons_layout)
        
        # إضافة إلى التمرير
        scroll = ScrollView()
        scroll.add_widget(fields_layout)
        content.add_widget(scroll)
        
        popup = Popup(
            title='إضافة مريض جديد',
            content=content,
            size_hint=(0.9, 0.8)
        )
        popup.open()
    
    def save_patient(self, popup):
        """حفظ بيانات المريض"""
        # التحقق من البيانات المطلوبة
        if not all([
            self.first_name_input.text.strip(),
            self.last_name_input.text.strip(),
            self.birth_date_input.text.strip(),
            self.phone_input.text.strip()
        ]):
            self.show_popup('يرجى ملء جميع الحقول المطلوبة')
            return
        
        patient_data = {
            "first_name": self.first_name_input.text.strip(),
            "last_name": self.last_name_input.text.strip(),
            "date_of_birth": self.birth_date_input.text.strip(),
            "gender": self.gender_spinner.text,
            "phone": self.phone_input.text.strip(),
            "email": self.email_patient_input.text.strip()
        }
        
        try:
            patient_id = self.clinic_data.add_patient(patient_data)
            popup.dismiss()
            self.show_popup("تم إضافة المريض بنجاح\nسيتم إرسال تنبيه للطبيب")
        except Exception as e:
            self.show_popup(f"خطأ في حفظ البيانات: {str(e)}")
    
    def show_patients_list(self, instance):
        """عرض قائمة المرضى"""
        patients = self.clinic_data.data.get("patients", [])
        self.display_patients_popup(patients)
    
    def display_patients_popup(self, patients):
        """عرض قائمة المرضى في نافذة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        
        patients_layout = GridLayout(cols=1, spacing=5, size_hint_y=None)
        patients_layout.bind(minimum_height=patients_layout.setter('height'))
        
        patients_layout.add_widget(Label(text=f'قائمة المرضى ({len(patients)})', font_size=16, size_hint_y=None, height=40))
        
        for patient in patients:
            patient_text = f"{patient.get('first_name', '')} {patient.get('last_name', '')} - {patient.get('phone', '')}"
            patients_layout.add_widget(Label(text=patient_text, size_hint_y=None, height=30))
        
        scroll = ScrollView()
        scroll.add_widget(patients_layout)
        content.add_widget(scroll)
        
        close_btn = Button(text='إغلاق', size_hint_y=None, height=50)
        close_btn.bind(on_press=lambda x: popup.dismiss())
        content.add_widget(close_btn)
        
        popup = Popup(
            title='قائمة المرضى',
            content=content,
            size_hint=(0.9, 0.8)
        )
        popup.open()
    
    def show_stats(self, instance):
        """عرض الإحصائيات"""
        stats = {
            "total_patients": len(self.clinic_data.data.get("patients", [])),
            "total_notifications": len(self.clinic_data.data.get("notifications", [])),
            "unread_notifications": len([n for n in self.clinic_data.data.get("notifications", []) if not n.get("is_read", False)])
        }
        
        content = BoxLayout(orientation='vertical', spacing=10)
        
        content.add_widget(Label(text='إحصائيات العيادة', font_size=18))
        content.add_widget(Label(text=f"إجمالي المرضى: {stats['total_patients']}"))
        content.add_widget(Label(text=f"إجمالي التنبيهات: {stats['total_notifications']}"))
        content.add_widget(Label(text=f"التنبيهات غير المقروءة: {stats['unread_notifications']}"))
        content.add_widget(Label(text=f"عنوان الخادم: http://{self.server_ip}:{self.server_port}"))
        
        close_btn = Button(text='إغلاق', size_hint_y=None, height=50)
        close_btn.bind(on_press=lambda x: popup.dismiss())
        content.add_widget(close_btn)
        
        popup = Popup(
            title='الإحصائيات',
            content=content,
            size_hint=(0.8, 0.6)
        )
        popup.open()
    
    def show_popup(self, message):
        """عرض رسالة منبثقة"""
        content = BoxLayout(orientation='vertical', spacing=10)
        content.add_widget(Label(text=message))
        
        close_btn = Button(text='موافق', size_hint_y=None, height=50)
        content.add_widget(close_btn)
        
        popup = Popup(
            title='رسالة',
            content=content,
            size_hint=(0.8, 0.4)
        )
        
        close_btn.bind(on_press=popup.dismiss)
        popup.open()
    
    def on_stop(self):
        """عند إغلاق التطبيق"""
        if self.server:
            self.server.shutdown()

if __name__ == '__main__':
    RegistrarServerApp().run()
