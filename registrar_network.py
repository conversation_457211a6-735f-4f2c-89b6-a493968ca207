#!/usr/bin/env python3
"""
Network Registrar App - For Multiple Devices
تطبيق موظف الاستقبال للشبكة - لأجهزة متعددة
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from datetime import datetime, date
import os
import sys
import socket

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from network_shared_data import network_shared_data
except ImportError:
    messagebox.showerror("خطأ", "ملف network_shared_data.py غير موجود")
    sys.exit(1)

class NetworkRegistrarApp:
    """تطبيق موظف الاستقبال للشبكة"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(f"Clinineo - موظف الاستقبال (الشبكة) - {socket.gethostname()}")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        self.current_user = None
        self.auto_refresh = True
        
        # إعداد الواجهة
        self.setup_ui()
        self.start_auto_refresh()
        
        # محاولة تسجيل دخول تلقائي
        self.auto_login()
        
        # عرض معلومات الشبكة
        self.show_network_info()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة المرضى
        patients_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المرضى", menu=patients_menu)
        patients_menu.add_command(label="إضافة مريض جديد", command=self.show_add_patient)
        patients_menu.add_command(label="قائمة المرضى", command=self.show_patients_list)
        
        # قائمة الشبكة
        network_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الشبكة", menu=network_menu)
        network_menu.add_command(label="معلومات الشبكة", command=self.show_network_info)
        network_menu.add_command(label="الأجهزة المتصلة", command=self.show_connected_devices)
        network_menu.add_command(label="تغيير مجلد البيانات", command=self.change_data_folder)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="تسجيل دخول", command=self.show_login)
        settings_menu.add_command(label="تحديث البيانات", command=self.refresh_data)
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط الحالة
        self.status_frame = ttk.Frame(main_frame)
        self.status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(self.status_frame, text="جاهز", foreground="green")
        self.status_label.pack(side=tk.LEFT)
        
        self.network_label = ttk.Label(self.status_frame, text="", foreground="blue")
        self.network_label.pack(side=tk.LEFT, padx=20)
        
        self.user_label = ttk.Label(self.status_frame, text="")
        self.user_label.pack(side=tk.RIGHT)
        
        # منطقة المحتوى
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # الصفحة الرئيسية
        self.show_home_page()
    
    def show_network_info(self):
        """عرض معلومات الشبكة"""
        try:
            stats = network_shared_data.get_dashboard_stats()
            hostname = socket.gethostname()
            ip = network_shared_data.get_local_ip()
            shared_folder = stats.get("shared_folder", "غير محدد")
            
            self.network_label.config(
                text=f"الجهاز: {hostname} | IP: {ip} | المجلد: {shared_folder}"
            )
        except Exception as e:
            self.network_label.config(text=f"خطأ في الشبكة: {str(e)}")
    
    def change_data_folder(self):
        """تغيير مجلد البيانات المشتركة"""
        folder = filedialog.askdirectory(title="اختر مجلد البيانات المشتركة")
        if folder:
            try:
                # إنشاء مدير بيانات جديد مع المجلد المحدد
                global network_shared_data
                from network_shared_data import NetworkSharedDataManager
                network_shared_data = NetworkSharedDataManager(folder)
                
                messagebox.showinfo("نجح", f"تم تغيير مجلد البيانات إلى:\n{folder}")
                self.show_network_info()
                self.refresh_data()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تغيير مجلد البيانات:\n{str(e)}")
    
    def auto_login(self):
        """تسجيل دخول تلقائي للاختبار"""
        user = network_shared_data.authenticate_user("<EMAIL>", "registrar123")
        if user:
            self.current_user = user
            self.user_label.config(text=f"المستخدم: {user['first_name']} {user['last_name']}")
            self.status_label.config(text="تم تسجيل الدخول تلقائياً", foreground="blue")
    
    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        def refresh_loop():
            while self.auto_refresh:
                try:
                    # تحديث معلومات الشبكة كل 30 ثانية
                    self.root.after(0, self.show_network_info)
                    time.sleep(30)
                except:
                    break
        
        threading.Thread(target=refresh_loop, daemon=True).start()
    
    def show_login(self):
        """إظهار نافذة تسجيل الدخول"""
        dialog = tk.Toplevel(self.root)
        dialog.title("تسجيل الدخول")
        dialog.geometry("300x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="البريد الإلكتروني:").pack(pady=5)
        email_entry = ttk.Entry(dialog, width=25)
        email_entry.pack(pady=5)
        email_entry.insert(0, "<EMAIL>")
        
        ttk.Label(dialog, text="كلمة المرور:").pack(pady=5)
        password_entry = ttk.Entry(dialog, width=25, show="*")
        password_entry.pack(pady=5)
        password_entry.insert(0, "registrar123")
        
        def login():
            email = email_entry.get().strip()
            password = password_entry.get().strip()
            
            user = network_shared_data.authenticate_user(email, password)
            if user:
                self.current_user = user
                self.user_label.config(text=f"المستخدم: {user['first_name']} {user['last_name']}")
                dialog.destroy()
                messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح")
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "بيانات تسجيل الدخول غير صحيحة")
        
        ttk.Button(dialog, text="تسجيل دخول", command=login).pack(pady=10)
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.config(text="تم تحديث البيانات", foreground="green")
        self.show_network_info()
        # إعادة تحميل الصفحة الحالية
        if hasattr(self, 'current_page'):
            if self.current_page == 'home':
                self.show_home_page()
            elif self.current_page == 'patients':
                self.show_patients_list()
    
    def show_home_page(self):
        """إظهار الصفحة الرئيسية"""
        self.current_page = 'home'
        
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # العنوان
        title_label = ttk.Label(self.content_frame, text="نظام إدارة العيادة - موظف الاستقبال (الشبكة)", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # معلومات الجهاز
        device_frame = ttk.LabelFrame(self.content_frame, text="معلومات الجهاز", padding="10")
        device_frame.pack(fill=tk.X, pady=10, padx=50)
        
        try:
            hostname = socket.gethostname()
            ip = network_shared_data.get_local_ip()
            stats = network_shared_data.get_dashboard_stats()
            
            ttk.Label(device_frame, text=f"اسم الجهاز: {hostname}", font=('Arial', 10)).pack(anchor='w')
            ttk.Label(device_frame, text=f"عنوان IP: {ip}", font=('Arial', 10)).pack(anchor='w')
            ttk.Label(device_frame, text=f"مجلد البيانات: {stats.get('shared_folder', 'غير محدد')}", 
                     font=('Arial', 10)).pack(anchor='w')
            ttk.Label(device_frame, text=f"الأجهزة المتصلة: {stats.get('online_devices', 0)}", 
                     font=('Arial', 10)).pack(anchor='w')
        except Exception as e:
            ttk.Label(device_frame, text=f"خطأ في تحميل معلومات الجهاز: {str(e)}", 
                     foreground="red").pack()
        
        # الأزرار الرئيسية
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(pady=20)
        
        ttk.Button(buttons_frame, text="إضافة مريض جديد", 
                  command=self.show_add_patient, width=25).pack(pady=5)
        
        ttk.Button(buttons_frame, text="قائمة المرضى", 
                  command=self.show_patients_list, width=25).pack(pady=5)
        
        ttk.Button(buttons_frame, text="الأجهزة المتصلة", 
                  command=self.show_connected_devices, width=25).pack(pady=5)
        
        ttk.Button(buttons_frame, text="تحديث البيانات", 
                  command=self.refresh_data, width=25).pack(pady=5)
        
        # إحصائيات سريعة
        stats_frame = ttk.LabelFrame(self.content_frame, text="إحصائيات سريعة", padding="10")
        stats_frame.pack(fill=tk.X, pady=20, padx=50)
        
        try:
            stats = network_shared_data.get_dashboard_stats()
            
            stats_grid = ttk.Frame(stats_frame)
            stats_grid.pack(fill=tk.X)
            
            ttk.Label(stats_grid, text=f"إجمالي المرضى: {stats.get('total_patients', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=0, column=0, padx=20, pady=5, sticky='w')
            
            ttk.Label(stats_grid, text=f"مواعيد اليوم: {stats.get('today_appointments', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=0, column=1, padx=20, pady=5, sticky='w')
            
            ttk.Label(stats_grid, text=f"الأطباء النشطين: {stats.get('active_doctors', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=1, column=0, padx=20, pady=5, sticky='w')
            
            ttk.Label(stats_grid, text=f"الأجهزة المتصلة: {stats.get('online_devices', 0)}", 
                     font=('Arial', 12, 'bold')).grid(row=1, column=1, padx=20, pady=5, sticky='w')
            
        except Exception as e:
            ttk.Label(stats_frame, text=f"خطأ في تحميل الإحصائيات: {str(e)}", 
                     foreground="red").pack()
    
    def show_connected_devices(self):
        """إظهار الأجهزة المتصلة"""
        dialog = tk.Toplevel(self.root)
        dialog.title("الأجهزة المتصلة")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        
        ttk.Label(dialog, text="الأجهزة المتصلة بالشبكة", font=('Arial', 14, 'bold')).pack(pady=10)
        
        # جدول الأجهزة
        columns = ('اسم الجهاز', 'عنوان IP', 'آخر اتصال', 'الحالة')
        tree = ttk.Treeview(dialog, columns=columns, show='headings', height=12)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=140)
        
        try:
            devices = network_shared_data.get_connected_devices()
            
            for device_id, device_info in devices.items():
                hostname = device_info.get('hostname', 'غير معروف')
                ip = device_info.get('ip', 'غير معروف')
                last_seen = device_info.get('last_seen', '')[:16].replace('T', ' ')
                status = device_info.get('status', 'غير معروف')
                
                tree.insert('', tk.END, values=(hostname, ip, last_seen, status))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل قائمة الأجهزة: {str(e)}")
        
        tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Button(dialog, text="تحديث", command=lambda: self.refresh_devices_list(tree)).pack(pady=5)
        ttk.Button(dialog, text="إغلاق", command=dialog.destroy).pack(pady=5)
    
    def refresh_devices_list(self, tree):
        """تحديث قائمة الأجهزة"""
        # مسح القائمة الحالية
        for item in tree.get_children():
            tree.delete(item)
        
        try:
            devices = network_shared_data.get_connected_devices()
            
            for device_id, device_info in devices.items():
                hostname = device_info.get('hostname', 'غير معروف')
                ip = device_info.get('ip', 'غير معروف')
                last_seen = device_info.get('last_seen', '')[:16].replace('T', ' ')
                status = device_info.get('status', 'غير معروف')
                
                tree.insert('', tk.END, values=(hostname, ip, last_seen, status))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة الأجهزة: {str(e)}")
    
    def show_add_patient(self):
        """إظهار نافذة إضافة مريض جديد"""
        if not self.current_user:
            messagebox.showwarning("تحذير", "يرجى تسجيل الدخول أولاً")
            return
        
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة مريض جديد")
        dialog.geometry("400x500")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # إطار التمرير
        canvas = tk.Canvas(dialog)
        scrollbar = ttk.Scrollbar(dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # الحقول
        fields = {}
        
        ttk.Label(scrollable_frame, text="الاسم الأول:").pack(pady=2, anchor='w')
        fields['first_name'] = ttk.Entry(scrollable_frame, width=30)
        fields['first_name'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="الاسم الأخير:").pack(pady=2, anchor='w')
        fields['last_name'] = ttk.Entry(scrollable_frame, width=30)
        fields['last_name'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="تاريخ الميلاد (YYYY-MM-DD):").pack(pady=2, anchor='w')
        fields['date_of_birth'] = ttk.Entry(scrollable_frame, width=30)
        fields['date_of_birth'].pack(pady=2, fill='x')
        fields['date_of_birth'].insert(0, "1990-01-01")
        
        ttk.Label(scrollable_frame, text="الجنس:").pack(pady=2, anchor='w')
        fields['gender'] = ttk.Combobox(scrollable_frame, values=['male', 'female'], width=27)
        fields['gender'].pack(pady=2, fill='x')
        fields['gender'].set('male')
        
        ttk.Label(scrollable_frame, text="رقم الهاتف:").pack(pady=2, anchor='w')
        fields['phone'] = ttk.Entry(scrollable_frame, width=30)
        fields['phone'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="البريد الإلكتروني:").pack(pady=2, anchor='w')
        fields['email'] = ttk.Entry(scrollable_frame, width=30)
        fields['email'].pack(pady=2, fill='x')
        
        ttk.Label(scrollable_frame, text="العنوان:").pack(pady=2, anchor='w')
        fields['address'] = tk.Text(scrollable_frame, width=30, height=3)
        fields['address'].pack(pady=2, fill='x')
        
        def save_patient():
            # جمع البيانات
            patient_data = {}
            for field_name, widget in fields.items():
                if isinstance(widget, tk.Text):
                    patient_data[field_name] = widget.get("1.0", tk.END).strip()
                else:
                    patient_data[field_name] = widget.get().strip()
            
            # التحقق من البيانات المطلوبة
            required_fields = ['first_name', 'last_name', 'date_of_birth', 'gender', 'phone']
            for field in required_fields:
                if not patient_data.get(field):
                    messagebox.showerror("خطأ", f"يرجى ملء حقل {field}")
                    return
            
            # حفظ المريض
            try:
                success = network_shared_data.add_patient(patient_data, self.current_user['id'])
                if success:
                    hostname = socket.gethostname()
                    messagebox.showinfo("نجح", 
                                      f"تم إضافة المريض بنجاح من الجهاز: {hostname}\n"
                                      f"سيتم إرسال تنبيه لجميع الأطباء في الشبكة")
                    dialog.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المريض")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حفظ البيانات: {str(e)}")
        
        ttk.Button(scrollable_frame, text="حفظ", command=save_patient).pack(pady=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def show_patients_list(self):
        """إظهار قائمة المرضى"""
        self.current_page = 'patients'
        
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        ttk.Label(self.content_frame, text="قائمة المرضى", 
                 font=('Arial', 14, 'bold')).pack(pady=10)
        
        # جدول المرضى
        columns = ('ID', 'الاسم', 'الهاتف', 'الجنس', 'الجهاز المُضيف', 'تاريخ الإضافة')
        tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)
        
        # تحميل البيانات
        try:
            patients = network_shared_data.get_patients()
            for patient in patients:
                created_by_device = patient.get('created_by_device', 'غير معروف')
                tree.insert('', tk.END, values=(
                    patient.get('id', ''),
                    f"{patient.get('first_name', '')} {patient.get('last_name', '')}",
                    patient.get('phone', ''),
                    'ذكر' if patient.get('gender') == 'male' else 'أنثى',
                    created_by_device.split('_')[0] if '_' in created_by_device else created_by_device,
                    patient.get('created_at', '')[:10]
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
        
        tree.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(pady=10)
        
        ttk.Button(buttons_frame, text="تحديث", command=self.show_patients_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="العودة للرئيسية", command=self.show_home_page).pack(side=tk.LEFT, padx=5)
    
    def on_closing(self):
        """عند إغلاق التطبيق"""
        self.auto_refresh = False
        self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    print("🚀 تشغيل تطبيق موظف الاستقبال للشبكة...")
    print(f"🖥️ الجهاز: {socket.gethostname()}")
    print(f"🌐 IP: {socket.gethostbyname(socket.gethostname())}")
    app = NetworkRegistrarApp()
    app.run()
